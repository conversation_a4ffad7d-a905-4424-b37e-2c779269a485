<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;e5856e60-9bc6-4cba-ab3a-69e6b8f91925&quot;,&quot;conversations&quot;:{&quot;e5856e60-9bc6-4cba-ab3a-69e6b8f91925&quot;:{&quot;id&quot;:&quot;e5856e60-9bc6-4cba-ab3a-69e6b8f91925&quot;,&quot;createdAtIso&quot;:&quot;2025-07-30T11:16:10.988Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-30T11:20:03.383Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;66cb5a43-70fc-4140-8279-19d118713e42&quot;,&quot;uuid&quot;:&quot;98db2bda-58c6-4c41-9985-db8564c8a60b&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1753874170990,&quot;seen_state&quot;:&quot;seen&quot;},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;80864def-47bd-422f-bae7-3c05c93706ac&quot;,&quot;request_message&quot;:&quot; 仔细检查代码\n1.勾选垂直聊天条复选框 聊天条状态需要RL才会变化\n2.垂直聊天条的起始位置需要和水平的其实位置一样\n3.垂直和水平是两个不同的个体 &quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot; 仔细检查代码&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;1.勾选垂直聊天条复选框 聊天条状态需要RL才会变化&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;2.垂直聊天条的起始位置需要和水平的其实位置一样&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;3.垂直和水平是两个不同的个体 &quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01JukCxKggeDfkqhWJy3BgW3&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;Moudel/WChat.lua\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;我来仔细检查 `Moudel/WChat.lua` 文件中的代码，特别关注垂直聊天条的相关问题。\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot; 仔细检查代码\n1.勾选垂直聊天条复选框 聊天条状态需要RL才会变化\n2.垂直聊天条的起始位置需要和水平的其实位置一样\n3.垂直和水平是两个不同的个体 &quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;,&quot;folderRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-30T11:18:42.985Z&quot;,&quot;response_text&quot;:&quot;我来仔细检查 `Moudel/WChat.lua` 文件中的代码，特别关注垂直聊天条的相关问题。\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;05ce5fc6-7aa0-46e6-9dcc-9374eee8f605&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01396ZibeUTnDRrNbgGhdieT&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;Moudel/WChat.lua\&quot;, \&quot;type\&quot;: \&quot;file\&quot;, \&quot;view_range\&quot;: [1330, 1605]}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看代码的其余部分，特别是垂直聊天条相关的代码：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on Moudel/WChat.lua:\n     1\t-------------------------------WChat 聊天增强模块-------------------------------\n     2\t-- 独立聊天增强模块，移除ACE3依赖\n     3\t-- 作者: VGGFAC\n     4\t-- 版本: 2.0.0\n     5\t\n     6\tlocal addonName, addon = ...\n     7\t\n     8\t-- 模块配置\n     9\tlocal WChat = {}\n    10\tlocal WChat_Config = {\n    11\t    -- 默认配置\n    12\t    ShortChannel = true,        -- 频道标签精简\n    13\t    EnableEmoteInput = true,    -- 启用表情输入\n    14\t    EmoteIconSize = 16,         -- 表情图标大小\n    15\t    EmoteIconListSize = 20,     -- 表情列表图标大小\n    16\t    EmoteOffsetX = 0,           -- 表情面板X偏移\n    17\t    EmoteOffsetY = 30,          -- 表情面板Y偏移\n    18\t    UseVertical = false,        -- 聊天条垂直布局\n    19\t    UseTopChatbar = false,      -- 聊天条在上方\n    20\t    UseTopInput = false,        -- 输入框在上方\n    21\t    ChatBarOffsetX = 0,         -- 聊天条X偏移\n    22\t    ChatBarOffsetY = 0,         -- 聊天条Y偏移\n    23\t    DistanceVertical = -25,     -- 垂直间距\n    24\t    DistanceHorizontal = 25,    -- 水平间距\n    25\t    AlphaOnEnter = 1.0,         -- 鼠标悬停透明度\n    26\t    AlphaOnLeave = 0.7,         -- 鼠标离开透明度\n    27\t    Position = nil,             -- 聊天条位置\n    28\t    EnableTimestampCopy = true, -- 启用时间戳点击复制\n    29\t    TimestampFormat = \&quot;%H:%M:%S\&quot;, -- 时间戳格式\n    30\t    TimestampColor = { 1.0, 1.0, 1.0 }, -- 时间戳颜色\n    31\t    -- 插件按钮显示控制\n    32\t    ShowBiaoGeButton = true,    -- 显示BiaoGe按钮\n    33\t    ShowBiaoGeAIButton = true,  -- 显示BiaoGeAI按钮\n    34\t    ShowAtlasButton = true,     -- 显示Atlas按钮\n    35\t    ShowMeetingHornButton = true, -- 显示MeetingHorn按钮\n    36\t    -- 频道隐藏设置\n    37\t    HiddenChannels = {}\n    38\t}\n    39\t\n    40\t-- 频道配置映射\n    41\tlocal CHANNEL_CONFIG = {\n    42\t    MAPPINGS = {\n    43\t        chnGen = \&quot;综合\&quot;,\n    44\t        chnTrade = \&quot;交易\&quot;,\n    45\t        chnLFG = \&quot;寻求组队\&quot;,\n    46\t        world = \&quot;大脚世界频道\&quot;\n    47\t    }\n    48\t}\n    49\t\n    50\t-- 频道显示/隐藏管理函数\n    51\tlocal function IsChannelShown(channelName)\n    52\t    local channels = {GetChatWindowChannels(DEFAULT_CHAT_FRAME:GetID() or 1)}\n    53\t    for i = 1, #channels, 2 do\n    54\t        if channels[i] == channelName then return true end\n    55\t    end\n    56\t    return false\n    57\tend\n    58\t\n    59\tlocal function ToggleChannelShowHide(channelName)\n    60\t    local isShown = IsChannelShown(channelName)\n    61\t    if isShown then\n    62\t        ChatFrame_RemoveChannel(DEFAULT_CHAT_FRAME, channelName)\n    63\t        WChat_Config.HiddenChannels[channelName] = true\n    64\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 已隐藏频道: \&quot; .. channelName)\n    65\t    else\n    66\t        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)\n    67\t        WChat_Config.HiddenChannels[channelName] = nil\n    68\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 已显示频道: \&quot; .. channelName)\n    69\t    end\n    70\tend\n    71\t\n    72\tlocal function UpdateChannelXIcons()\n    73\t    if not WChatBar then return end\n    74\t\n    75\t    for _, child in pairs({WChatBar:GetChildren()}) do\n    76\t        if child.X and child.buttonName and CHANNEL_CONFIG.MAPPINGS[child.buttonName] then\n    77\t            local channelName = CHANNEL_CONFIG.MAPPINGS[child.buttonName]\n    78\t            child.X:SetShown(not IsChannelShown(channelName))\n    79\t        end\n    80\t    end\n    81\tend\n    82\t\n    83\t-- 获取语言\n    84\tlocal Language = GetLocale()\n    85\tif (Language == \&quot;zhTW\&quot;) then\n    86\t    --公会\n    87\t    CHAT_GUILD_GET = \&quot;|Hchannel:GUILD|h[公會]|h %s: \&quot;\n    88\t    CHAT_OFFICER_GET = \&quot;|Hchannel:OFFICER|h[官員]|h %s: \&quot;\n    89\t    \n    90\t    --团队\n    91\t    CHAT_RAID_GET = \&quot;|Hchannel:RAID|h[團隊]|h %s: \&quot;\n    92\t    CHAT_RAID_WARNING_GET = \&quot;[通知] %s: \&quot;\n    93\t    CHAT_RAID_LEADER_GET = \&quot;|Hchannel:RAID|h[團長]|h %s: \&quot;\n    94\t    \n    95\t    --队伍\n    96\t    CHAT_PARTY_GET = \&quot;|Hchannel:PARTY|h[隊伍]|h %s: \&quot;\n    97\t    CHAT_PARTY_LEADER_GET = \&quot;|Hchannel:PARTY|h[隊長]|h %s: \&quot;\n    98\t    CHAT_PARTY_GUIDE_GET = \&quot;|Hchannel:PARTY|h[向導]|h %s: \&quot;\n    99\t    \n   100\t    --战场\n   101\t    CHAT_BATTLEGROUND_GET = \&quot;|Hchannel:BATTLEGROUND|h[戰場]|h %s: \&quot;\n   102\t    CHAT_BATTLEGROUND_LEADER_GET = \&quot;|Hchannel:BATTLEGROUND|h[領袖]|h %s: \&quot;\n   103\t    \n   104\t    --说 / 喊\n   105\t    CHAT_SAY_GET = \&quot;%s: \&quot;\n   106\t    CHAT_YELL_GET = \&quot;%s: \&quot;\n   107\t    \n   108\t    --密语\n   109\t    CHAT_WHISPER_INFORM_GET = \&quot;發送給%s: \&quot;\n   110\t    CHAT_WHISPER_GET = \&quot;%s悄悄話: \&quot;\n   111\t    \n   112\t    --flags\n   113\t    CHAT_FLAG_AFK = \&quot;[暫離] \&quot;\n   114\t    CHAT_FLAG_DND = \&quot;[勿擾] \&quot;\n   115\t    CHAT_FLAG_GM = \&quot;[GM] \&quot;\n   116\telseif (Language == \&quot;zhCN\&quot;) then\n   117\t    --公会\n   118\t    CHAT_GUILD_GET = \&quot;|Hchannel:GUILD|h[公会]|h %s: \&quot;\n   119\t    CHAT_OFFICER_GET = \&quot;|Hchannel:OFFICER|h[官员]|h %s: \&quot;\n   120\t    \n   121\t    --团队\n   122\t    CHAT_RAID_GET = \&quot;|Hchannel:RAID|h[团队]|h %s: \&quot;\n   123\t    CHAT_RAID_WARNING_GET = \&quot;[通知] %s: \&quot;\n   124\t    CHAT_RAID_LEADER_GET = \&quot;|Hchannel:RAID|h[团长]|h %s: \&quot;\n   125\t    \n   126\t    --队伍\n   127\t    CHAT_PARTY_GET = \&quot;|Hchannel:PARTY|h[队伍]|h %s: \&quot;\n   128\t    CHAT_PARTY_LEADER_GET = \&quot;|Hchannel:PARTY|h[队长]|h %s: \&quot;\n   129\t    CHAT_PARTY_GUIDE_GET = \&quot;|Hchannel:PARTY|h[向导]:|h %s: \&quot;\n   130\t    \n   131\t    --战场\n   132\t    CHAT_BATTLEGROUND_GET = \&quot;|Hchannel:BATTLEGROUND|h[副本]|h %s: \&quot;\n   133\t    CHAT_BATTLEGROUND_LEADER_GET = \&quot;|Hchannel:BATTLEGROUND|h[领袖]|h %s: \&quot;\n   134\t    \n   135\t    --密语\n   136\t    CHAT_WHISPER_INFORM_GET = \&quot;发送给%s: \&quot;\n   137\t    CHAT_WHISPER_GET = \&quot;%s悄悄的说: \&quot;\n   138\t    CHAT_BN_WHISPER_INFORM_GET = \&quot;发送给%s: \&quot;\n   139\t    CHAT_BN_WHISPER_GET = \&quot;悄悄的说%s: \&quot;\n   140\t    \n   141\t    --说 / 喊\n   142\t    CHAT_SAY_GET = \&quot;%s: \&quot;\n   143\t    CHAT_YELL_GET = \&quot;%s: \&quot;\n   144\t    \n   145\t    --flags\n   146\t    CHAT_FLAG_AFK = \&quot;[暂离] \&quot;\n   147\t    CHAT_FLAG_DND = \&quot;[勿扰] \&quot;\n   148\t    CHAT_FLAG_GM = \&quot;[GM] \&quot;\n   149\telse\n   150\t    CHAT_GUILD_GET = \&quot;|Hchannel:GUILD|hG|h %s \&quot;\n   151\t    CHAT_OFFICER_GET = \&quot;|Hchannel:OFFICER|hO|h %s \&quot;\n   152\t    CHAT_RAID_GET = \&quot;|Hchannel:RAID|hR|h %s \&quot;\n   153\t    CHAT_RAID_WARNING_GET = \&quot;RW %s \&quot;\n   154\t    CHAT_RAID_LEADER_GET = \&quot;|Hchannel:RAID|hRL|h %s \&quot;\n   155\t    CHAT_PARTY_GET = \&quot;|Hchannel:PARTY|hP|h %s \&quot;\n   156\t    CHAT_PARTY_LEADER_GET = \&quot;|Hchannel:PARTY|hPL|h %s \&quot;\n   157\t    CHAT_PARTY_GUIDE_GET = \&quot;|Hchannel:PARTY|hPG|h %s \&quot;\n   158\t    CHAT_BATTLEGROUND_GET = \&quot;|Hchannel:BATTLEGROUND|hB|h %s \&quot;\n   159\t    CHAT_BATTLEGROUND_LEADER_GET = \&quot;|Hchannel:BATTLEGROUND|hBL|h %s \&quot;\n   160\t    CHAT_WHISPER_INFORM_GET = \&quot;to %s \&quot;\n   161\t    CHAT_WHISPER_GET = \&quot;from %s \&quot;\n   162\t    CHAT_BN_WHISPER_INFORM_GET = \&quot;to %s \&quot;\n   163\t    CHAT_BN_WHISPER_GET = \&quot;from %s \&quot;\n   164\t    CHAT_SAY_GET = \&quot;%s \&quot;\n   165\t    CHAT_YELL_GET = \&quot;%s \&quot;\n   166\t    CHAT_FLAG_AFK = \&quot;[AFK] \&quot;\n   167\t    CHAT_FLAG_DND = \&quot;[DND] \&quot;\n   168\t    CHAT_FLAG_GM = \&quot;[GM] \&quot;\n   169\tend\n   170\t\n   171\t--================================公共频道和自定义频道精简================================--\n   172\tlocal gsub = _G.string.gsub\n   173\tlocal newAddMsg = {}\n   174\tlocal chn = {\n   175\t    \&quot;%[%d+%. General.-%]\&quot;,\n   176\t    \&quot;%[%d+%. Trade.-%]\&quot;,\n   177\t    \&quot;%[%d+%. LocalDefense.-%]\&quot;,\n   178\t    \&quot;%[%d+%. LookingForGroup%]\&quot;,\n   179\t    \&quot;%[%d+%. WorldDefense%]\&quot;,\n   180\t    \&quot;%[%d+%. GuildRecruitment.-%]\&quot;,\n   181\t    \&quot;%[%d+%. BigFootChannel.-%]\&quot;,\n   182\t    \&quot;%[%d+%. CustomChannel.-%]\&quot; -- 自定义频道英文名随便填写\n   183\t}\n   184\t\n   185\tlocal rplc = {\n   186\t    \&quot;[GEN]\&quot;,\n   187\t    \&quot;[TR]\&quot;,\n   188\t    \&quot;[WD]\&quot;,\n   189\t    \&quot;[LD]\&quot;,\n   190\t    \&quot;[LFG]\&quot;,\n   191\t    \&quot;[GR]\&quot;,\n   192\t    \&quot;[BFC]\&quot;,\n   193\t    \&quot;[CL]\&quot; -- 英文缩写\n   194\t}\n   195\t\n   196\tif (Language == \&quot;zhCN\&quot;) then ---国服\n   197\t    rplc[1] = \&quot;[%1综]\&quot;\n   198\t    rplc[2] = \&quot;[%1交]\&quot;\n   199\t    rplc[3] = \&quot;[%1防]\&quot;\n   200\t    rplc[4] = \&quot;[%1组]\&quot;\n   201\t    rplc[5] = \&quot;[%1守]\&quot;\n   202\t    rplc[6] = \&quot;[%1招]\&quot;\n   203\t    rplc[7] = \&quot;[%1世]\&quot;\n   204\t    rplc[8] = \&quot;[%1自定义]\&quot; -- 自定义频道缩写请自行修改\n   205\telseif (Language == \&quot;zhTW\&quot;) then ---台服\n   206\t    rplc[1] = \&quot;[%1綜合]\&quot;\n   207\t    rplc[2] = \&quot;[%1貿易]\&quot;\n   208\t    rplc[3] = \&quot;[%1防務]\&quot;\n   209\t    rplc[4] = \&quot;[%1組隊]\&quot;\n   210\t    rplc[5] = \&quot;[%1守備]\&quot;\n   211\t    rplc[6] = \&quot;[%1招募]\&quot;\n   212\t    rplc[7] = \&quot;[%1世界]\&quot;\n   213\t    rplc[8] = \&quot;[%1自定义]\&quot; -- 自定义频道缩写请自行修改\n   214\tend\n   215\t\n   216\tif Language == \&quot;zhCN\&quot; then\n   217\t    ---------------------------------------- 国服简体中文 ---------------------------------------------\n   218\t    chn[1] = \&quot;%[%d+%. 综合.-%]\&quot;\n   219\t    chn[2] = \&quot;%[%d+%. 交易.-%]\&quot;\n   220\t    chn[3] = \&quot;%[%d+%. 本地防务.-%]\&quot;\n   221\t    chn[4] = \&quot;%[%d+%. 寻求组队%]\&quot;\n   222\t    chn[5] = \&quot;%[%d+%. 世界防务%]\&quot;\n   223\t    chn[6] = \&quot;%[%d+%. 公会招募.-%]\&quot;\n   224\t    chn[7] = \&quot;%[%d+%. 大脚世界频道.-%]\&quot;\n   225\t    chn[8] = \&quot;%[%d+%. 自定义频道.-%]\&quot; -- 请修改频道名对应你游戏里的频道\n   226\telseif Language == \&quot;zhTW\&quot; then\n   227\t    ---------------------------------------- 台服繁体中文 ---------------------------------------------\n   228\t    chn[1] = \&quot;%[%d+%. 綜合.-%]\&quot;\n   229\t    chn[2] = \&quot;%[%d+%. 貿易.-%]\&quot;\n   230\t    chn[3] = \&quot;%[%d+%. 本地防務.-%]\&quot;\n   231\t    chn[4] = \&quot;%[%d+%. 尋求組隊%]\&quot;\n   232\t    chn[5] = \&quot;%[%d+%. 世界防務%]\&quot;\n   233\t    chn[6] = \&quot;%[%d+%. 公會招募.-%]\&quot;\n   234\t    chn[7] = \&quot;%[%d+%. 大脚世界频道.-%]\&quot;\n   235\t    chn[8] = \&quot;%[%d+%. 自定义频道.-%]\&quot; -- 请修改频道名对应你游戏里的频道\n   236\tend\n   237\t\n   238\tlocal rules = {\n   239\t        --!!不要改\n   240\t        {pat = \&quot;|c%x+|HChatCopy|h.-|h|r\&quot;, repl = \&quot;\&quot;},\n   241\t        {pat = \&quot;|c%x%x%x%x%x%x%x%x(.-)|r\&quot;, repl = \&quot;%1\&quot;},\n   242\t        --左鍵\n   243\t        {pat = \&quot;|Hchannel:.-|h.-|h\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   244\t        {pat = \&quot;|Hplayer:.-|h.-|h\&quot; .. \&quot;:\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   245\t        {pat = \&quot;|Hplayer:.-|h.-|h\&quot; .. \&quot;：\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   246\t        {pat = \&quot;|HBNplayer:.-|h.-|h\&quot; .. \&quot;:\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   247\t        {pat = \&quot;|HBNplayer:.-|h.-|h\&quot; .. \&quot;：\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   248\t        --右鍵\n   249\t        {pat = \&quot;|Hchannel:.-|h(.-)|h\&quot;, repl = \&quot;%1\&quot;, button = \&quot;RightButton\&quot;},\n   250\t        {pat = \&quot;|Hplayer:.-|h(.-)|h\&quot;, repl = \&quot;%1\&quot;, button = \&quot;RightButton\&quot;},\n   251\t        {pat = \&quot;|HBNplayer:.-|h(.-)|h\&quot;, repl = \&quot;%1\&quot;, button = \&quot;RightButton\&quot;},\n   252\t        --!!不要改\n   253\t        {pat = \&quot;|H.-|h(.-)|h\&quot;, repl = \&quot;%1\&quot;},\n   254\t        {pat = \&quot;|TInterface\\\\TargetingFrame\\\\UI%-RaidTargetingIcon_(%d):0|t\&quot;, repl = \&quot;{rt%1}\&quot;},\n   255\t        {pat = \&quot;|T.-|t\&quot;, repl = \&quot;\&quot;},\n   256\t        {pat = \&quot;^%s+\&quot;, repl = \&quot;\&quot;}\n   257\t}\n   258\t\n   259\t-- 文字修改函数\n   260\tfunction WChat:AddMessage(text, ...)\n   261\t    -- 频道标签精简\n   262\t    if (type(text) ~= \&quot;string\&quot;) then\n   263\t        text = tostring(text)\n   264\t    end\n   265\t    for i = 1, 8 do -- 对应上面几个频道(如果有9个频道就for i = 1, 9 do)\n   266\t        text = text:gsub(chn[i], rplc[i])\n   267\t    end\n   268\t    text = text:gsub(\&quot;%[(%d0?)%. .-%]\&quot;, \&quot;%1.\&quot;)\n   269\t    return self.DefaultAddMessage(self, text, ...)\n   270\tend\n   271\t\n   272\t-- 初始化频道信息精简模块\n   273\tfunction WChat:InitChannel()\n   274\t    if WChat_Config.ShortChannel then\n   275\t        for i = 1, (NUM_CHAT_WINDOWS or 10) do\n   276\t            if i ~= 2 then\n   277\t                local cf = _G['ChatFrame' .. i]\n   278\t                cf.DefaultAddMessage = cf.AddMessage\n   279\t                cf.AddMessage = self.AddMessage\n   280\t            end\n   281\t        end\n   282\t    end\n   283\tend\n   284\t-------------------------------聊天复制------------------------------------\n   285\t\n   286\tlocal lines = {}\n   287\t\n   288\tlocal chatCopyFrame = CreateFrame(\&quot;Frame\&quot;, \&quot;ChatCopyFrame\&quot;, UIParent, BackdropTemplateMixin and \&quot;BackdropTemplate\&quot; or nil)\n   289\tchatCopyFrame:SetPoint(\&quot;CENTER\&quot;, UIParent, \&quot;CENTER\&quot;)\n   290\tchatCopyFrame:SetSize(700, 400)\n   291\tchatCopyFrame:Hide()\n   292\tchatCopyFrame:SetFrameStrata(\&quot;DIALOG\&quot;)\n   293\tchatCopyFrame.close = CreateFrame(\&quot;Button\&quot;, nil, chatCopyFrame, \&quot;UIPanelCloseButton\&quot;)\n   294\tchatCopyFrame.close:SetPoint(\&quot;TOPRIGHT\&quot;, chatCopyFrame, \&quot;TOPRIGHT\&quot;)\n   295\tchatCopyFrame:SetBackdrop({\n   296\t    bgFile = \&quot;Interface/DialogFrame/UI-DialogBox-Background\&quot;,\n   297\t    edgeFile = \&quot;Interface/DialogFrame/UI-DialogBox-Border\&quot;,\n   298\t    tile = true,\n   299\t    tileSize = 16,\n   300\t    edgeSize = 16,\n   301\t    insets = {left = 4, right = 4, top = 4, bottom = 4}\n   302\t})\n   303\t\n   304\tlocal scrollArea = CreateFrame(\&quot;ScrollFrame\&quot;, \&quot;ChatCopyScrollFrame\&quot;, chatCopyFrame, \&quot;UIPanelScrollFrameTemplate\&quot;)\n   305\tscrollArea:SetPoint(\&quot;TOPLEFT\&quot;, chatCopyFrame, \&quot;TOPLEFT\&quot;, 10, -30)\n   306\tscrollArea:SetPoint(\&quot;BOTTOMRIGHT\&quot;, chatCopyFrame, \&quot;BOTTOMRIGHT\&quot;, -30, 10)\n   307\t\n   308\tlocal editBox = CreateFrame(\&quot;EditBox\&quot;, nil, chatCopyFrame)\n   309\teditBox:SetMultiLine(true)\n   310\teditBox:SetMaxLetters(99999)\n   311\teditBox:EnableMouse(true)\n   312\teditBox:SetAutoFocus(false)\n   313\teditBox:SetFontObject(ChatFontNormal)\n   314\teditBox:SetWidth(scrollArea:GetWidth())\n   315\teditBox:SetHeight(270)\n   316\teditBox:SetScript(\&quot;OnEscapePressed\&quot;, function(f)f:GetParent():GetParent():Hide()f:SetText(\&quot;\&quot;) end)\n   317\tscrollArea:SetScrollChild(editBox)\n   318\t\n   319\tfunction WChat:CopyFunc()\n   320\t    local cf = SELECTED_CHAT_FRAME\n   321\t    local _, size = cf:GetFont()\n   322\t    FCF_SetChatWindowFontSize(cf, cf, .01)\n   323\t    local ct = 1\n   324\t    for i = select(\&quot;#\&quot;, cf.FontStringContainer:GetRegions()), 1, -1 do\n   325\t        local region = select(i, cf.FontStringContainer:GetRegions())\n   326\t        if region:GetObjectType() == \&quot;FontString\&quot; then\n   327\t            if region:GetText() ~= nil then\n   328\t                lines[ct] = tostring(region:GetText())\n   329\t                ct = ct + 1\n   330\t            end\n   331\t        end\n   332\t    end\n   333\t    local lineCt = ct - 1\n   334\t    local text = table.concat(lines, \&quot;\\n\&quot;, 1, lineCt)\n   335\t    FCF_SetChatWindowFontSize(cf, cf, size)\n   336\t    chatCopyFrame:Show()\n   337\t    editBox:SetText(text)\n   338\t    editBox:HighlightText(0)\n   339\t    wipe(lines)\n   340\tend\n   341\t----------------------------聊天表情相关代码-----------------------------\n   342\t\n   343\t-- 表情选择器框架\n   344\tlocal EmoteTableFrame\n   345\t\n   346\t-- 表情解析规则\n   347\tlocal fmtstring\n   348\t\n   349\t-- 自定义表情开始的序号\n   350\tlocal customEmoteStartIndex = 9\n   351\t\n   352\tlocal emotes = {\n   353\t        --原版暴雪提供的8个图标\n   354\t        {\&quot;{rt1}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_1]=]},\n   355\t        {\&quot;{rt2}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_2]=]},\n   356\t        {\&quot;{rt3}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_3]=]},\n   357\t        {\&quot;{rt4}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_4]=]},\n   358\t        {\&quot;{rt5}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_5]=]},\n   359\t        {\&quot;{rt6}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_6]=]},\n   360\t        {\&quot;{rt7}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_7]=]},\n   361\t        {\&quot;{rt8}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_8]=]},\n   362\t        --自定义表情\n   363\t        {\&quot;{天使}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Angel]=]},\n   364\t        {\&quot;{生气}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Angry]=]},\n   365\t        {\&quot;{大笑}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Biglaugh]=]},\n   366\t        {\&quot;{鼓掌}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Clap]=]},\n   367\t        {\&quot;{酷}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Cool]=]},\n   368\t        {\&quot;{哭}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Cry]=]},\n   369\t        {\&quot;{可爱}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Cutie]=]},\n   370\t        {\&quot;{鄙视}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Despise]=]},\n   371\t        {\&quot;{美梦}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Dreamsmile]=]},\n   372\t        {\&quot;{尴尬}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Embarrass]=]},\n   373\t        {\&quot;{邪恶}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Evil]=]},\n   374\t        {\&quot;{兴奋}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Excited]=]},\n   375\t        {\&quot;{晕}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Faint]=]},\n   376\t        {\&quot;{打架}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Fight]=]},\n   377\t        {\&quot;{流感}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Flu]=]},\n   378\t        {\&quot;{呆}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Freeze]=]},\n   379\t        {\&quot;{皱眉}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Frown]=]},\n   380\t        {\&quot;{致敬}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Greet]=]},\n   381\t        {\&quot;{鬼脸}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Grimace]=]},\n   382\t        {\&quot;{龇牙}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Growl]=]},\n   383\t        {\&quot;{开心}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Happy]=]},\n   384\t        {\&quot;{心}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Heart]=]},\n   385\t        {\&quot;{恐惧}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Horror]=]},\n   386\t        {\&quot;{生病}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Ill]=]},\n   387\t        {\&quot;{无辜}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Innocent]=]},\n   388\t        {\&quot;{功夫}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Kongfu]=]},\n   389\t        {\&quot;{花痴}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Love]=]},\n   390\t        {\&quot;{邮件}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Mail]=]},\n   391\t        {\&quot;{化妆}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Makeup]=]},\n   392\t        -- {\&quot;{马里奥}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Mario]=]},\n   393\t        {\&quot;{沉思}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Meditate]=]},\n   394\t        {\&quot;{可怜}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Miserable]=]},\n   395\t        {\&quot;{好}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Okay]=]},\n   396\t        {\&quot;{漂亮}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Pretty]=]},\n   397\t        {\&quot;{吐}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Puke]=]},\n   398\t        {\&quot;{握手}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Shake]=]},\n   399\t        {\&quot;{喊}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Shout]=]},\n   400\t        {\&quot;{闭嘴}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Shuuuu]=]},\n   401\t        {\&quot;{害羞}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Shy]=]},\n   402\t        {\&quot;{睡觉}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Sleep]=]},\n   403\t        {\&quot;{微笑}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Smile]=]},\n   404\t        {\&quot;{吃惊}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Suprise]=]},\n   405\t        {\&quot;{失败}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Surrender]=]},\n   406\t        {\&quot;{流汗}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Sweat]=]},\n   407\t        {\&quot;{流泪}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Tear]=]},\n   408\t        {\&quot;{悲剧}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Tears]=]},\n   409\t        {\&quot;{想}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Think]=]},\n   410\t        {\&quot;{偷笑}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Titter]=]},\n   411\t        {\&quot;{猥琐}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Ugly]=]},\n   412\t        {\&quot;{胜利}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Victory]=]},\n   413\t        {\&quot;{雷锋}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Volunteer]=]},\n   414\t        {\&quot;{委屈}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Wronged]=]}\n   415\t}\n   416\t\n   417\tlocal function ChatEmoteFilter(self, event, msg, ...)\n   418\t    if (WChat_Config and WChat_Config.EnableEmoteInput) then\n   419\t        for i = customEmoteStartIndex, #emotes do\n   420\t            if msg:find(emotes[i][1]) then\n   421\t                msg = msg:gsub(emotes[i][1], format(fmtstring, emotes[i][2]), 1)\n   422\t            end\n   423\t        end\n   424\t    end\n   425\t    return false, msg, ...\n   426\tend\n   427\t\n   428\tlocal function EmoteIconMouseUp(frame, button)\n   429\t    if (button == \&quot;LeftButton\&quot;) then\n   430\t        local chatFrame = GetCVar(\&quot;chatStyle\&quot;) == \&quot;im\&quot; and SELECTED_CHAT_FRAME or DEFAULT_CHAT_FRAME\n   431\t        local eb = chatFrame and chatFrame.editBox\n   432\t        if (eb) then\n   433\t            eb:Insert(frame.text)\n   434\t            eb:Show();\n   435\t            eb:SetFocus()\n   436\t        end\n   437\t    end\n   438\t    WChat:ToggleEmoteTable()\n   439\tend\n   440\t\n   441\tfunction WChat:InitEmoteTableFrame()\n   442\t    fmtstring = format(\&quot;\\124T%%s:%d\\124t\&quot;, max(floor(select(2, SELECTED_CHAT_FRAME:GetFont())), WChat_Config.EmoteIconSize))\n   443\t    \n   444\t    EmoteTableFrame = CreateFrame(\&quot;Frame\&quot;, \&quot;EmoteTableFrame\&quot;, UIParent, BackdropTemplateMixin and \&quot;BackdropTemplate\&quot; or nil)\n   445\t    EmoteTableFrame:SetMovable(true)\n   446\t    EmoteTableFrame:RegisterForDrag(\&quot;LeftButton\&quot;)\n   447\t    EmoteTableFrame:SetScript(\&quot;OnDragStart\&quot;, EmoteTableFrame.StartMoving)\n   448\t    EmoteTableFrame:SetScript(\&quot;OnDragStop\&quot;, EmoteTableFrame.StopMovingOrSizing)\n   449\t    EmoteTableFrame:EnableMouse(true)\n   450\t    EmoteTableFrame:SetWidth((WChat_Config.EmoteIconListSize + 6) * 12 + 10)\n   451\t    EmoteTableFrame:SetHeight((WChat_Config.EmoteIconListSize + 6) * 5 + 10)\n   452\t    EmoteTableFrame:SetPoint(\&quot;BOTTOM\&quot;, ChatFrame1EditBox, WChat_Config.EmoteOffsetX, WChat_Config.EmoteOffsetY)\n   453\t    EmoteTableFrame:SetBackdrop({\n   454\t        bgFile = \&quot;Interface\\\\Buttons\\\\WHITE8x8\&quot;,\n   455\t        edgeFile = \&quot;Interface\\\\Tooltips\\\\UI-Tooltip-Border\&quot;,\n   456\t        tile = true,\n   457\t        tileSize = 16,\n   458\t        edgeSize = 16,\n   459\t        insets = {left = 3, right = 3, top = 3, bottom = 3}\n   460\t    })\n   461\t    EmoteTableFrame:SetBackdropColor(0.05, 0.05, 0.05, 0.8)\n   462\t    EmoteTableFrame:SetBackdropBorderColor(0.3, 0.3, 0.3)\n   463\t    -- 表情选择框出现位置 默认30,30\n   464\t    EmoteTableFrame:Hide()\n   465\t    EmoteTableFrame:SetFrameStrata(\&quot;DIALOG\&quot;)\n   466\t    local icon, row, col\n   467\t    row = 1\n   468\t    col = 1\n   469\t    for i = 1, #emotes do\n   470\t        text = emotes[i][1]\n   471\t        texture = emotes[i][2]\n   472\t        icon = CreateFrame(\&quot;Frame\&quot;, format(\&quot;IconButton%d\&quot;, i), EmoteTableFrame)\n   473\t        icon:SetWidth(WChat_Config.EmoteIconListSize + 6)\n   474\t        icon:SetHeight(WChat_Config.EmoteIconListSize + 6)\n   475\t        icon.text = text\n   476\t        icon.texture = icon:CreateTexture(nil, \&quot;ARTWORK\&quot;)\n   477\t        icon.texture:SetTexture(texture)\n   478\t        icon.texture:SetAllPoints(icon)\n   479\t        icon:Show()\n   480\t        icon:SetPoint(\n   481\t            \&quot;TOPLEFT\&quot;,\n   482\t            5 + (col - 1) * (WChat_Config.EmoteIconListSize + 6),\n   483\t            -5 - (row - 1) * (WChat_Config.EmoteIconListSize + 6)\n   484\t        )\n   485\t        icon:SetScript(\&quot;OnMouseUp\&quot;, EmoteIconMouseUp)\n   486\t        icon:EnableMouse(true)\n   487\t        col = col + 1\n   488\t        if (col &gt; 12) then\n   489\t            row = row + 1\n   490\t            col = 1\n   491\t        end\n   492\t    end\n   493\t    \n   494\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_CHANNEL\&quot;, ChatEmoteFilter)-- 公共频道\n   495\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_SAY\&quot;, ChatEmoteFilter)-- 说\n   496\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_YELL\&quot;, ChatEmoteFilter)-- 大喊\n   497\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_RAID\&quot;, ChatEmoteFilter)-- 团队\n   498\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_RAID_LEADER\&quot;, ChatEmoteFilter)-- 团队领袖\n   499\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_PARTY\&quot;, ChatEmoteFilter)-- 队伍\n   500\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_PARTY_LEADER\&quot;, ChatEmoteFilter)-- 队伍领袖\n   501\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_GUILD\&quot;, ChatEmoteFilter)-- 公会\n   502\t    \n   503\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_AFK\&quot;, ChatEmoteFilter)-- AFK玩家自动回复\n   504\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_DND\&quot;, ChatEmoteFilter)-- 切勿打扰自动回复\n   505\t    \n   506\t    -- 副本和副本领袖\n   507\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_INSTANCE_CHAT\&quot;, ChatEmoteFilter)\n   508\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_INSTANCE_CHAT_LEADER\&quot;, ChatEmoteFilter)\n   509\t    -- 解析战网私聊\n   510\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_WHISPER\&quot;, ChatEmoteFilter)\n   511\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_WHISPER_INFORM\&quot;, ChatEmoteFilter)\n   512\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_BN_WHISPER\&quot;, ChatEmoteFilter)\n   513\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_BN_WHISPER_INFORM\&quot;, ChatEmoteFilter)\n   514\t    -- 解析社区聊天内容\n   515\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_COMMUNITIES_CHANNEL\&quot;, ChatEmoteFilter)\n   516\tend\n   517\t\n   518\tfunction WChat:ToggleEmoteTable()\n   519\t    if (EmoteTableFrame:IsShown()) then\n   520\t        EmoteTableFrame:Hide()\n   521\t    else\n   522\t        EmoteTableFrame:Show()\n   523\t    end\n   524\tend\n   525\t-----------------------------------频道和密语对象的快速切换\n   526\t\n   527\tfunction ChatEdit_CustomTabPressed(...)\n   528\t    return ChatEdit_CustomTabPressed_Inner(...)\n   529\tend\n   530\t\n   531\tlocal cycles = {\n   532\t        -- \&quot;说\&quot;\n   533\t        {\n   534\t            chatType = \&quot;SAY\&quot;,\n   535\t            use = function(self, editbox)\n   536\t                return 1\n   537\t            end\n   538\t        },\n   539\t        --大喊\n   540\t        {\n   541\t            chatType = \&quot;YELL\&quot;,\n   542\t            use = function(self, editbox)\n   543\t                return 1\n   544\t            end\n   545\t        },\n   546\t        --小队\n   547\t        {\n   548\t            chatType = \&quot;PARTY\&quot;,\n   549\t            use = function(self, editbox)\n   550\t                return IsInGroup()\n   551\t            end\n   552\t        },\n   553\t        --团队\n   554\t        {\n   555\t            chatType = \&quot;RAID\&quot;,\n   556\t            use = function(self, editbox)\n   557\t                return IsInRaid()\n   558\t            end\n   559\t        },\n   560\t        --实时聊天\n   561\t        {\n   562\t            chatType = \&quot;INSTANCE_CHAT\&quot;,\n   563\t            use = function(self, editbox)\n   564\t                return select(2, IsInInstance()) == \&quot;pvp\&quot;\n   565\t            end\n   566\t        },\n   567\t        --公会\n   568\t        {\n   569\t            chatType = \&quot;GUILD\&quot;,\n   570\t            use = function(self, editbox)\n   571\t                return IsInGuild()\n   572\t            end\n   573\t        },\n   574\t        --频道\n   575\t        {\n   576\t            chatType = \&quot;CHANNEL\&quot;,\n   577\t            use = function(self, editbox, currChatType)\n   578\t                local currNum\n   579\t                if currChatType ~= \&quot;CHANNEL\&quot; then\n   580\t                    currNum = IsShiftKeyDown() and 21 or 0\n   581\t                else\n   582\t                    currNum = editbox:GetAttribute(\&quot;channelTarget\&quot;)\n   583\t                end\n   584\t                local h, r, step = currNum + 1, 20, 1\n   585\t                if IsShiftKeyDown() then\n   586\t                    h, r, step = currNum - 1, 1, -1\n   587\t                end\n   588\t                for i = h, r, step do\n   589\t                    local channelNum, channelName = GetChannelName(i)\n   590\t                    if channelNum &gt; 0 and channelName:find(\&quot;大脚世界频道\&quot;) then\n   591\t                        --print(channelName); --DEBUG\n   592\t                        editbox:SetAttribute(\&quot;channelTarget\&quot;, i)\n   593\t                        return true\n   594\t                    end\n   595\t                end\n   596\t            end\n   597\t        },\n   598\t        {\n   599\t            chatType = \&quot;SAY\&quot;,\n   600\t            use = function(self, editbox)\n   601\t                return 1\n   602\t            end\n   603\t        }\n   604\t}\n   605\t\n   606\tlocal chatTypeBeforeSwitch, tellTargetBeforeSwitch --记录在频道和密语之间切换时的状态\n   607\tfunction ChatEdit_CustomTabPressed_Inner(self)\n   608\t    if strsub(tostring(self:GetText()), 1, 1) == \&quot;/\&quot; then\n   609\t        return\n   610\t    end\n   611\t    local currChatType = self:GetAttribute(\&quot;chatType\&quot;)\n   612\t    if (IsControlKeyDown()) then\n   613\t        if (currChatType == \&quot;WHISPER\&quot; or currChatType == \&quot;BN_WHISPER\&quot;) then\n   614\t            --记录之前的密语对象，以便后续切回\n   615\t            self:SetAttribute(\&quot;chatType\&quot;, chatTypeBeforeSwitch or \&quot;SAY\&quot;)\n   616\t            ChatEdit_UpdateHeader(self)\n   617\t            chatTypeBeforeSwitch = \&quot;WHISPER\&quot;\n   618\t            tellTargetBeforeSwitch = self:GetAttribute(\&quot;tellTarget\&quot;)\n   619\t            return --这里和下面不同，这里可以不返回true\n   620\t        else\n   621\t            local newTarget, newTargetType = ChatEdit_GetNextTellTarget()\n   622\t            if tellTargetBeforeSwitch or (newTarget and newTarget ~= \&quot;\&quot;) then\n   623\t                self:SetAttribute(\&quot;chatType\&quot;, tellTargetBeforeSwitch and chatTypeBeforeSwitch or newTargetType)\n   624\t                self:SetAttribute(\&quot;tellTarget\&quot;, tellTargetBeforeSwitch or newTarget)\n   625\t                ChatEdit_UpdateHeader(self)\n   626\t                chatTypeBeforeSwitch = currChatType\n   627\t                tellTargetBeforeSwitch = nil\n   628\t                return true --这里必须返回true，否则会被暴雪默认的再切换一次密语对象\n   629\t            end\n   630\t        end\n   631\t    end\n   632\t    \n   633\t    --对于说然后SHIFT的情况，因为没有return，所以第一层循环会一直遍历到最后的SAY\n   634\t    for i, curr in ipairs(cycles) do\n   635\t        if curr.chatType == currChatType then\n   636\t            local h, r, step = i + 1, #cycles, 1\n   637\t            if IsShiftKeyDown() then\n   638\t                h, r, step = i - 1, 1, -1\n   639\t            end\n   640\t            if currChatType == \&quot;CHANNEL\&quot; then\n   641\t                h = i\n   642\t            end --频道仍然要测试一下\n   643\t            for j = h, r, step do\n   644\t                if cycles[j]:use(self, currChatType) then\n   645\t                    self:SetAttribute(\&quot;chatType\&quot;, cycles[j].chatType)\n   646\t                    ChatEdit_UpdateHeader(self)\n   647\t                    return\n   648\t                end\n   649\t            end\n   650\t        end\n   651\t    end\n   652\tend\n   653\t\n   654\t\n   655\t\n   656\t-----------------------------点击时间复制 (alaCHAT完整实现)\n   657\t-- 基于alaCHAT的完整时间戳点击复制功能\n   658\t\n   659\tlocal hooksecurefunc = hooksecurefunc;\n   660\tlocal date = date;\n   661\tlocal format, gsub, strmatch = string.format, string.gsub, string.match;\n   662\tlocal GetCVar, SetCVar = GetCVar, SetCVar;\n   663\tlocal ChatEdit_ChooseBoxForSend, ChatEdit_ActivateChat = ChatEdit_ChooseBoxForSend, ChatEdit_ActivateChat;\n   664\tlocal ItemRefTooltip = ItemRefTooltip;\n   665\tlocal _G = _G;\n   666\t\n   667\tlocal __copy = {};\n   668\tlocal _db = {};\n   669\t\n   670\tlocal __TAG = \&quot;\&quot;;\n   671\tlocal __FMT = \&quot;\&quot;;\n   672\tlocal __CLR = { 1.0, 1.0, 1.0, };\n   673\t\n   674\t-- 设置时间戳 (alaCHAT原版)\n   675\tlocal function SetTimeStamp()\n   676\t    __TAG = format(\&quot;|cff%.2x%.2x%.2x|Haccopy:-1|h%s|h|r\&quot;, __CLR[1] * 255, __CLR[2] * 255, __CLR[3] * 255, (__FMT == nil or __FMT == \&quot;\&quot; or __FMT == \&quot;none\&quot;) and \&quot;*\&quot; or __FMT);\n   677\t    if GetCVar(\&quot;showTimestamps\&quot;) ~= __TAG then\n   678\t        SetCVar(\&quot;showTimestamps\&quot;, __TAG);\n   679\t        _G.CHAT_TIMESTAMP_FORMAT = __TAG;\n   680\t    end\n   681\tend\n   682\t\n   683\t-- 初始化时间戳点击复制功能 (alaCHAT原版)\n   684\tlocal B_Initialized = false;\n   685\tlocal function Init()\n   686\t    B_Initialized = true;\n   687\t    local _ItemRefTooltip_SetHyperlink = ItemRefTooltip.SetHyperlink;\n   688\t    function ItemRefTooltip:SetHyperlink(link, ...)\n   689\t        if link == \&quot;accopy:-1\&quot; then\n   690\t            local focus = GetMouseFocus and GetMouseFocus() or GetMouseFoci and GetMouseFoci()[1];\n   691\t            if not focus:IsObjectType(\&quot;FontString\&quot;) then\n   692\t                focus = focus:GetParent();\n   693\t                if not focus:IsObjectType(\&quot;FontString\&quot;) then\n   694\t                    return;\n   695\t                end\n   696\t            end\n   697\t            local tx = focus:GetText();\n   698\t            if tx == nil or tx == \&quot;\&quot; then\n   699\t                return;\n   700\t            end\n   701\t            -- 清理文本格式 (alaCHAT原版处理)\n   702\t            tx = gsub(tx, \&quot;|H.-|h\&quot;, \&quot;\&quot;);\n   703\t            tx = gsub(tx, \&quot;|c%x%x%x%x%x%x%x%x\&quot;, \&quot;\&quot;);\n   704\t            tx = gsub(tx, \&quot;|[Hhr]\&quot;, \&quot;\&quot;);\n   705\t            local editBox = ChatEdit_ChooseBoxForSend();\n   706\t            if not editBox:HasFocus() then\n   707\t                ChatEdit_ActivateChat(editBox);\n   708\t            end\n   709\t            editBox:SetText(tx);\n   710\t            return;\n   711\t        end\n   712\t        return _ItemRefTooltip_SetHyperlink(self, link, ...);\n   713\t    end\n   714\t    -- 监听时间戳设置变化\n   715\t    if InterfaceOptionsSocialPanelTimestamps ~= nil and InterfaceOptionsSocialPanelTimestamps.SetValue ~= nil then\n   716\t        hooksecurefunc(InterfaceOptionsSocialPanelTimestamps, \&quot;SetValue\&quot;, function(self, value)\n   717\t            _db.toggle = false;\n   718\t        end);\n   719\t    end\n   720\tend\n   721\t\n   722\t-- alaCHAT模块功能实现\n   723\tfunction __copy.color(value, loading)\n   724\t    if not loading and _db.toggle then\n   725\t        if value ~= nil and( __CLR[1] ~= value[1] or __CLR[2] ~= value[2] or __CLR[3] ~= value[3]) then\n   726\t            __CLR = { value[1], value[2], value[3], };\n   727\t            SetTimeStamp();\n   728\t        end\n   729\t    end\n   730\tend\n   731\t\n   732\tfunction __copy.format(value, loading)\n   733\t    if not loading and _db.toggle then\n   734\t        if value ~= nil and __FMT ~= value then\n   735\t            __FMT = value;\n   736\t            SetTimeStamp();\n   737\t        end\n   738\t    end\n   739\tend\n   740\t\n   741\tfunction __copy.toggle(value, loading)\n   742\t    if value then\n   743\t        if not B_Initialized then\n   744\t            Init();\n   745\t        end\n   746\t        local c = _db.color;\n   747\t        local f = _db.format;\n   748\t        __FMT = f;\n   749\t        __CLR = { c[1], c[2], c[3], };\n   750\t        SetTimeStamp();\n   751\t    elseif loading then\n   752\t        local fmt = GetCVar(\&quot;showTimestamps\&quot;);\n   753\t        if fmt ~= \&quot;none\&quot; then\n   754\t            local fmt2 = strmatch(fmt, \&quot;|h(.+)|h\&quot;);\n   755\t            if fmt2 ~= nil then\n   756\t                SetCVar(\&quot;showTimestamps\&quot;, fmt2);\n   757\t                _G.CHAT_TIMESTAMP_FORMAT = fmt2;\n   758\t            end\n   759\t        end\n   760\t    else\n   761\t        if __FMT == \&quot;none\&quot; or __FMT == \&quot;\&quot; or __FMT == nil then\n   762\t            if GetCVar(\&quot;showTimestamps\&quot;) ~= \&quot;none\&quot; then\n   763\t                SetCVar(\&quot;showTimestamps\&quot;, \&quot;none\&quot;);\n   764\t                _G.CHAT_TIMESTAMP_FORMAT = nil;\n   765\t            end\n   766\t        else\n   767\t            if GetCVar(\&quot;showTimestamps\&quot;) ~= __FMT then\n   768\t                SetCVar(\&quot;showTimestamps\&quot;, __FMT);\n   769\t                _G.CHAT_TIMESTAMP_FORMAT = __FMT;\n   770\t            end\n   771\t        end\n   772\t    end\n   773\tend\n   774\t\n   775\t-- WChat集成的时间戳复制功能\n   776\tfunction WChat:InitTimestampCopy()\n   777\t    if WChat_Config.EnableTimestampCopy then\n   778\t        -- 初始化_db配置\n   779\t        _db = {\n   780\t            toggle = true,\n   781\t            format = WChat_Config.TimestampFormat or \&quot;%H:%M:%S\&quot;,\n   782\t            color = WChat_Config.TimestampColor or { 1.0, 1.0, 1.0 }\n   783\t        };\n   784\t\n   785\t        -- 启用功能\n   786\t        __copy.toggle(true, false);\n   787\t    end\n   788\tend\n   789\t\n   790\t-----------------------------聊天条\n   791\t\n   792\t--[[=========================== 变量区 ==========================]]\n   793\t-- 是否可移动的标记\n   794\tlocal IsMovable = false -- 没事干别动这个，你改成ture那么进入游戏后聊天条就是可以移动的\n   795\t--[[=============================== END ==============================]]\n   796\tlocal chatFrame = SELECTED_DOCK_FRAME -- 聊天框架\n   797\tlocal inputbox = chatFrame.editBox -- 输入框\n   798\t\n   799\tCOLORSCHEME_BORDER = {0.3, 0.3, 0.3, 1}\n   800\t-- 边框颜色\n   801\t-- 主框架初始化\n   802\tlocal ChatBar = CreateFrame(\&quot;Frame\&quot;, nil, UIParent, BackdropTemplateMixin and \&quot;BackdropTemplate\&quot; or nil)\n   803\tWChatBar = ChatBar\n   804\t\n   805\tlocal function ChannelSay_OnClick()\n   806\t    ChatFrame_OpenChat(\&quot;/s \&quot; .. inputbox:GetText(), chatFrame)\n   807\tend\n   808\t\n   809\tlocal function ChannelYell_OnClick()\n   810\t    ChatFrame_OpenChat(\&quot;/y \&quot; .. inputbox:GetText(), chatFrame)\n   811\tend\n   812\t\n   813\tlocal function ChannelParty_OnClick()\n   814\t    ChatFrame_OpenChat(\&quot;/p \&quot; .. inputbox:GetText(), chatFrame)\n   815\tend\n   816\t\n   817\tlocal function ChannelGuild_OnClick()\n   818\t    ChatFrame_OpenChat(\&quot;/g \&quot; .. inputbox:GetText(), chatFrame)\n   819\tend\n   820\t\n   821\tlocal function ChannelRaid_OnClick()\n   822\t    ChatFrame_OpenChat(\&quot;/raid \&quot; .. inputbox:GetText(), chatFrame)\n   823\tend\n   824\t\n   825\t-- 综合频道点击处理\n   826\tlocal function ChannelGen_OnClick(self, button)\n   827\t    if button == \&quot;RightButton\&quot; then\n   828\t        -- 右键：切换频道显示/隐藏\n   829\t        ToggleChannelShowHide(\&quot;综合\&quot;)\n   830\t        C_Timer.After(0.1, UpdateChannelXIcons)\n   831\t    else\n   832\t        -- 左键：如果频道未显示则先显示，然后发言\n   833\t        if not IsChannelShown(\&quot;综合\&quot;) then\n   834\t            ToggleChannelShowHide(\&quot;综合\&quot;)\n   835\t            C_Timer.After(0.1, UpdateChannelXIcons)\n   836\t        end\n   837\t        local channel, _, _ = GetChannelName(\&quot;综合\&quot;)\n   838\t        if channel then\n   839\t            ChatFrame_OpenChat(\&quot;/\&quot; .. channel .. \&quot; \&quot; .. inputbox:GetText(), chatFrame)\n   840\t        end\n   841\t    end\n   842\tend\n   843\t\n   844\t-- 交易频道点击处理\n   845\tlocal function ChannelTrade_OnClick(self, button)\n   846\t    if button == \&quot;RightButton\&quot; then\n   847\t        -- 右键：切换频道显示/隐藏\n   848\t        ToggleChannelShowHide(\&quot;交易\&quot;)\n   849\t        C_Timer.After(0.1, UpdateChannelXIcons)\n   850\t    else\n   851\t        -- 左键：如果频道未显示则先显示，然后发言\n   852\t        if not IsChannelShown(\&quot;交易\&quot;) then\n   853\t            ToggleChannelShowHide(\&quot;交易\&quot;)\n   854\t            C_Timer.After(0.1, UpdateChannelXIcons)\n   855\t        end\n   856\t        local channel, _, _ = GetChannelName(\&quot;交易\&quot;)\n   857\t        if channel then\n   858\t            ChatFrame_OpenChat(\&quot;/\&quot; .. channel .. \&quot; \&quot; .. inputbox:GetText(), chatFrame)\n   859\t        end\n   860\t    end\n   861\tend\n   862\t\n   863\t-- 寻求组队频道点击处理\n   864\tlocal function ChannelLFG_OnClick(self, button)\n   865\t    if button == \&quot;RightButton\&quot; then\n   866\t        -- 右键：切换频道显示/隐藏\n   867\t        ToggleChannelShowHide(\&quot;寻求组队\&quot;)\n   868\t        C_Timer.After(0.1, UpdateChannelXIcons)\n   869\t    else\n   870\t        -- 左键：如果频道未显示则先显示，然后发言\n   871\t        if not IsChannelShown(\&quot;寻求组队\&quot;) then\n   872\t            ToggleChannelShowHide(\&quot;寻求组队\&quot;)\n   873\t            C_Timer.After(0.1, UpdateChannelXIcons)\n   874\t        end\n   875\t        local channel, _, _ = GetChannelName(\&quot;寻求组队\&quot;)\n   876\t        if channel then\n   877\t            ChatFrame_OpenChat(\&quot;/\&quot; .. channel .. \&quot; \&quot; .. inputbox:GetText(), chatFrame)\n   878\t        end\n   879\t    end\n   880\tend\n   881\t\n   882\tlocal function ChannelBG_OnClick(self, button)\n   883\t    -- 副本频道按钮，只有左键功能\n   884\t    ChatFrame_OpenChat(\&quot;/bg \&quot; .. inputbox:GetText(), chatFrame)\n   885\tend\n   886\t\n   887\t-- function Channel01_OnClick()\n   888\t--     ChatFrame_OpenChat(\&quot;/1 \&quot;, chatFrame)\n   889\t-- end\n   890\tlocal function ChatEmote_OnClick()\n   891\t    WChat:ToggleEmoteTable()\n   892\tend\n   893\t\n   894\tlocal function ChannelWorld_OnClick(self, button)\n   895\t    if button == \&quot;RightButton\&quot; then\n   896\t        -- 右键：切换频道显示/隐藏\n   897\t        ToggleChannelShowHide(\&quot;大脚世界频道\&quot;)\n   898\t        C_Timer.After(0.1, UpdateChannelXIcons)\n   899\t    else\n   900\t        -- 左键：发言或加入频道后发言\n   901\t        local _, channelName, _ = GetChannelName(\&quot;大脚世界频道\&quot;)\n   902\t        if channelName == nil then\n   903\t            JoinPermanentChannel(\&quot;大脚世界频道\&quot;, nil, 1, 1)\n   904\t            ChatFrame_RemoveMessageGroup(chatFrame, \&quot;CHANNEL\&quot;)\n   905\t            ChatFrame_AddChannel(chatFrame, \&quot;大脚世界频道\&quot;)\n   906\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cff00d200已加入大脚世界频道|r\&quot;)\n   907\t            -- 确保频道显示\n   908\t            C_Timer.After(0.2, function()\n   909\t                if not IsChannelShown(\&quot;大脚世界频道\&quot;) then\n   910\t                    ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, \&quot;大脚世界频道\&quot;)\n   911\t                    WChat_Config.HiddenChannels[\&quot;大脚世界频道\&quot;] = nil\n   912\t                end\n   913\t                UpdateChannelXIcons()\n   914\t            end)\n   915\t        else\n   916\t            -- 如果频道未显示则先显示\n   917\t            if not IsChannelShown(\&quot;大脚世界频道\&quot;) then\n   918\t                ToggleChannelShowHide(\&quot;大脚世界频道\&quot;)\n   919\t                C_Timer.After(0.1, UpdateChannelXIcons)\n   920\t            end\n   921\t        end\n   922\t\n   923\t        local channel, _, _ = GetChannelName(\&quot;大脚世界频道\&quot;)\n   924\t        if channel then\n   925\t            ChatFrame_OpenChat(\&quot;/\&quot; .. channel .. \&quot; \&quot; .. inputbox:GetText(), chatFrame)\n   926\t        end\n   927\t    end\n   928\tend\n   929\t\n   930\tlocal function Roll_OnClick()\n   931\t    RandomRoll(1, 100)\n   932\tend\n   933\t\n   934\tlocal function Report_OnClick(self, button)\n   935\t    local statText = WChat:StatReport()\n   936\t    if button == \&quot;RightButton\&quot; then\n   937\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cff00d200我的属性：|r\&quot; .. statText)\n   938\t    else\n   939\t        local editBox = ChatEdit_ChooseBoxForSend()\n   940\t        if not editBox:HasFocus() then\n   941\t            ChatEdit_ActivateChat(editBox)\n   942\t        end\n   943\t        editBox:Insert(statText)\n   944\t    end\n   945\tend\n   946\t\n   947\tlocal function ChatCopy_OnClick()\n   948\t    WChat:CopyFunc()\n   949\tend\n   950\t\n   951\t-- 插件配置表\n   952\tlocal AddonConfigs = {\n   953\t    BiaoGe = {\n   954\t        addonName = \&quot;BiaoGe\&quot;,\n   955\t        displayName = \&quot;BiaoGe金团\&quot;,\n   956\t        globalVar = \&quot;BG\&quot;,\n   957\t        mainFrameKey = \&quot;MainFrame\&quot;,\n   958\t        slashCmd = nil,\n   959\t        tooltip = function()\n   960\t            return \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开BiaoGe金团|r\&quot;\n   961\t        end\n   962\t    },\n   963\t    BiaoGeAI = {\n   964\t        addonName = \&quot;BiaoGeAI\&quot;,\n   965\t        displayName = \&quot;BiaoGeAI\&quot;,\n   966\t        globalVar = \&quot;BGAI\&quot;,\n   967\t        mainFrameKey = \&quot;MainFrame\&quot;,\n   968\t        slashCmd = nil,\n   969\t        tooltip = function()\n   970\t            return \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开BiaoGeAI|r\&quot;\n   971\t        end\n   972\t    },\n   973\t    AtlasLootClassic = {\n   974\t        addonName = \&quot;AtlasLootClassic\&quot;,\n   975\t        displayName = \&quot;Atlas掉落\&quot;,\n   976\t        globalVar = nil,\n   977\t        mainFrameKey = nil,\n   978\t        slashCmd = \&quot;ATLASLOOT\&quot;,\n   979\t        tooltip = function()\n   980\t            return \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开Atlas掉落查询|r\&quot;\n   981\t        end\n   982\t    },\n   983\t    MeetingHorn = {\n   984\t        addonName = \&quot;MeetingHorn\&quot;,\n   985\t        displayName = \&quot;MeetingHorn集合石\&quot;,\n   986\t        globalVar = nil,\n   987\t        mainFrameKey = nil,\n   988\t        slashCmd = \&quot;MEETINGHORN\&quot;,\n   989\t        tooltip = function()\n   990\t            local tooltip = \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开集结号|r\&quot;\n   991\t\n   992\t            local addon = LibStub and LibStub('AceAddon-3.0'):GetAddon('MeetingHorn', true)\n   993\t            if not addon then return tooltip end\n   994\t\n   995\t            local lfg = addon:GetModule('LFG', true)\n   996\t            if not lfg then return tooltip end\n   997\t\n   998\t            local icon1 = \&quot;|TInterface\\\\AddOns\\\\MeetingHorn\\\\Media\\\\DataBroker:16:16:0:0:64:32:0:32:0:32|t\&quot;\n   999\t            local icon2 = \&quot;|TInterface\\\\AddOns\\\\MeetingHorn\\\\Media\\\\DataBroker:16:16:0:0:64:32:32:64:0:32|t\&quot;\n  1000\t            tooltip = tooltip .. \&quot;\\n\&quot; .. icon2 .. \&quot;|cffFFD700活动数量: \&quot; .. lfg:GetActivityCount() .. \&quot;|r\&quot;\n  1001\t\n  1002\t            local count = lfg:GetCurrentActivity() and lfg:GetApplicantCount() or lfg:GetApplicationCount()\n  1003\t            local label = lfg:GetCurrentActivity() and \&quot;申请者数量\&quot; or \&quot;申请数量\&quot;\n  1004\t            return tooltip .. \&quot;\\n\&quot; .. icon1 .. \&quot;|cffFFD700\&quot; .. label .. \&quot;: \&quot; .. count .. \&quot;|r\&quot;\n  1005\t        end\n  1006\t    }\n  1007\t}\n  1008\t\n  1009\t-- 通用插件按钮点击处理函数\n  1010\tlocal function CreateAddonClickHandler(configKey)\n  1011\t    return function()\n  1012\t        local config = AddonConfigs[configKey]\n  1013\t        if not config then return end\n  1014\t\n  1015\t        -- 检查并加载插件\n  1016\t        if not IsAddOnLoaded(config.addonName) then\n  1017\t            local loaded, reason = LoadAddOn(config.addonName)\n  1018\t            if not loaded then\n  1019\t                print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cffff0000\&quot; .. config.displayName .. \&quot;插件加载失败: \&quot; .. (reason or \&quot;未知原因\&quot;) .. \&quot;|r\&quot;)\n  1020\t                return\n  1021\t            end\n  1022\t            -- 刷新聊天条按钮\n  1023\t            WChat:RefreshChatBarButtons()\n  1024\t        end\n  1025\t\n  1026\t        -- 尝试打开插件界面\n  1027\t        if config.globalVar and config.mainFrameKey then\n  1028\t            -- 使用MainFrame方式\n  1029\t            local addon = _G[config.globalVar]\n  1030\t            if addon and addon[config.mainFrameKey] then\n  1031\t                addon[config.mainFrameKey]:SetShown(not addon[config.mainFrameKey]:IsVisible())\n  1032\t            end\n  1033\t        elseif config.slashCmd then\n  1034\t            -- 使用斜杠命令方式\n  1035\t            if configKey == \&quot;MeetingHorn\&quot; then\n  1036\t                -- MeetingHorn特殊处理\n  1037\t                local meetingHornAddon = LibStub and LibStub('AceAddon-3.0'):GetAddon('MeetingHorn', true)\n  1038\t                if meetingHornAddon and meetingHornAddon.Toggle then\n  1039\t                    meetingHornAddon:Toggle()\n  1040\t                elseif SlashCmdList[config.slashCmd] then\n  1041\t                    SlashCmdList[config.slashCmd](\&quot;\&quot;)\n  1042\t                end\n  1043\t            else\n  1044\t                -- 其他插件使用斜杠命令\n  1045\t                if SlashCmdList[config.slashCmd] then\n  1046\t                    SlashCmdList[config.slashCmd](\&quot;\&quot;)\n  1047\t                end\n  1048\t            end\n  1049\t        end\n  1050\t    end\n  1051\tend\n  1052\t\n  1053\t-- 创建具体的点击处理函数\n  1054\tlocal Gold_OnClick = CreateAddonClickHandler(\&quot;BiaoGe\&quot;)\n  1055\tlocal AI_OnClick = CreateAddonClickHandler(\&quot;BiaoGeAI\&quot;)\n  1056\tlocal Atlas_OnClick = CreateAddonClickHandler(\&quot;AtlasLootClassic\&quot;)\n  1057\tlocal MeetingHorn_OnClick = CreateAddonClickHandler(\&quot;MeetingHorn\&quot;)\n  1058\t\n  1059\tlocal function Movelock_OnClick(self, button)\n  1060\t    if button == \&quot;LeftButton\&quot; then\n  1061\t        if IsMovable then\n  1062\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cffd20000锁定聊天条|r\&quot;)\n  1063\t            IsMovable = false\n  1064\t            WChatBar:SetBackdrop(nil)\n  1065\t\n  1066\t            local point, relativeTo, relativePoint, xOfs, yOfs = WChatBar:GetPoint()\n  1067\t\n  1068\t            if relativeTo then\n  1069\t                WChat_Config.Position = {point = point, relativeTo = relativeTo:GetName(), relativePoint = relativePoint, xOfs = xOfs, yOfs = yOfs}\n  1070\t            else\n  1071\t                WChat_Config.Position = {point = point, relativeTo = nil, relativePoint = relativePoint, xOfs = xOfs, yOfs = yOfs}\n  1072\t            end\n  1073\t        else\n  1074\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cff00d200解锁聊天条|r\&quot;)\n  1075\t            IsMovable = true\n  1076\t            WChatBar:SetBackdrop(\n  1077\t                {\n  1078\t                    bgFile = \&quot;Interface/DialogFrame/UI-DialogBox-Background\&quot;,\n  1079\t                    edgeFile = \&quot;Interface/DialogFrame/UI-DialogBox-Border\&quot;,\n  1080\t                    tile = true,\n  1081\t                    tileSize = 16,\n  1082\t                    edgeSize = 16,\n  1083\t                    insets = {left = 4, right = 4, top = 4, bottom = 4}\n  1084\t                }\n  1085\t        )\n  1086\t        end\n  1087\t        WChatBar:EnableMouse(IsMovable)\n  1088\t    elseif button == \&quot;MiddleButton\&quot; then\n  1089\t        if IsMovable == false then\n  1090\t            return\n  1091\t        end\n  1092\t        WChatBar:ClearAllPoints()\n  1093\t        if WChat_Config.UseVertical then\n  1094\t            if WChat_Config.UseTopChatbar then\n  1095\t                WChatBar:SetPoint(\&quot;TOPRIGHT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX - 30, WChat_Config.ChatBarOffsetY + 25)\n  1096\t            else\n  1097\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPRIGHT\&quot;, WChat_Config.ChatBarOffsetX + 30, WChat_Config.ChatBarOffsetY + 25)\n  1098\t            end\n  1099\t        else\n  1100\t            if WChat_Config.UseTopChatbar then\n  1101\t                WChatBar:SetPoint(\&quot;BOTTOMLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY + 30)\n  1102\t            else\n  1103\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;BOTTOMLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY - 30)\n  1104\t            end\n  1105\t        end\n  1106\t    end\n  1107\tend\n  1108\t\n  1109\tlocal ChannelButtons = {\n  1110\t    {name = \&quot;say\&quot;, text = \&quot;说\&quot;, color = {1.00, 1.00, 1.00}, callback = ChannelSay_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到说话频道|r\&quot; end},\n  1111\t    {name = \&quot;yell\&quot;, text = \&quot;喊\&quot;, color = {1.00, 0.25, 0.25}, callback = ChannelYell_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到大喊频道|r\&quot; end},\n  1112\t    {name = \&quot;party\&quot;, text = \&quot;队\&quot;, color = {0.66, 0.66, 1.00}, callback = ChannelParty_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到队伍频道|r\&quot; end},\n  1113\t    {name = \&quot;guild\&quot;, text = \&quot;会\&quot;, color = {0.25, 1.00, 0.25}, callback = ChannelGuild_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到公会频道|r\&quot; end},\n  1114\t    {name = \&quot;raid\&quot;, text = \&quot;团\&quot;, color = {1.00, 0.50, 0.00}, callback = ChannelRaid_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到团队频道|r\&quot; end},\n  1115\t    {name = \&quot;LFT\&quot;, text = \&quot;副\&quot;, color = {1.00, 0.50, 0.00}, callback = ChannelBG_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到副本频道|r\&quot; end},\n  1116\t    {name = \&quot;chnGen\&quot;, text = \&quot;综\&quot;, color = {0.82, 0.70, 0.55}, callback = ChannelGen_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到综合频道|r\\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r\&quot; end},\n  1117\t    {name = \&quot;chnTrade\&quot;, text = \&quot;交\&quot;, color = {1.00, 0.82, 0.00}, callback = ChannelTrade_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到交易频道|r\\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r\&quot; end},\n  1118\t    {name = \&quot;chnLFG\&quot;, text = \&quot;组\&quot;, color = {0.50, 1.00, 0.50}, callback = ChannelLFG_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到寻求组队频道|r\\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r\&quot; end},\n  1119\t    {name = \&quot;world\&quot;, text = \&quot;世\&quot;, color = {0.78, 1.00, 0.59}, callback = ChannelWorld_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到世界频道|r\\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r\&quot; end},\n  1120\t    {name = \&quot;emote\&quot;, text = \&quot;表\&quot;, color = {1.00, 0.50, 1.00}, callback = ChatEmote_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开表情选择器|r\&quot; end},\n  1121\t    {name = \&quot;roll\&quot;, text = \&quot;骰\&quot;, color = {1.00, 1.00, 0.00}, callback = Roll_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff投掷骰子|r\&quot; end},\n  1122\t    {name = \&quot;report\&quot;, text = \&quot;报\&quot;, color = {0.80, 0.30, 0.30}, callback = Report_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff发送属性报告|r\\n|cff00ffff鼠标右键|r-|cffff80ff在聊天框显示属性|r\&quot; end},\n  1123\t    {name = \&quot;movelock\&quot;, text = \&quot;锁\&quot;, color = {0.20, 0.20, 0.80}, callback = Movelock_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff锁定/解锁聊天条位置|r\&quot; end},\n  1124\t    {name = \&quot;chatcopy\&quot;, text = \&quot;复\&quot;, color = {0.20, 0.60, 0.80}, callback = ChatCopy_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff复制聊天内容|r\&quot; end},\n  1125\t    -- 插件按钮\n  1126\t    {name = \&quot;biaoge\&quot;, text = \&quot;金\&quot;, color = {1.00, 0.84, 0.00}, callback = Gold_OnClick, tooltip = AddonConfigs.BiaoGe.tooltip},\n  1127\t    {name = \&quot;biaogeai\&quot;, text = \&quot;AI\&quot;, color = {0.00, 0.80, 1.00}, callback = AI_OnClick, tooltip = AddonConfigs.BiaoGeAI.tooltip},\n  1128\t    {name = \&quot;atlas\&quot;, text = \&quot;掉\&quot;, color = {0.80, 0.20, 0.80}, callback = Atlas_OnClick, tooltip = AddonConfigs.AtlasLootClassic.tooltip},\n  1129\t    {name = \&quot;meetinghorn\&quot;, text = \&quot;集\&quot;, color = {0.20, 0.80, 0.20}, callback = MeetingHorn_OnClick, tooltip = AddonConfigs.MeetingHorn.tooltip}\n  1130\t}\n  1131\t\n  1132\tlocal function CreateChannelButton(data, index)\n  1133\t    local frame = CreateFrame(\&quot;Button\&quot;, \&quot;frameName\&quot;, WChatBar)\n  1134\t    frame:SetWidth(22)\n  1135\t    -- 按钮宽度\n  1136\t    frame:SetHeight(22)\n  1137\t    -- 按钮高度\n  1138\t    frame:SetAlpha(WChat_Config.AlphaOnLeave)\n  1139\t    \n  1140\t    frame:SetFrameLevel(1)\n  1141\t    \n  1142\t    frame:SetScript(\n  1143\t        \&quot;OnEnter\&quot;,\n  1144\t        function(self)\n  1145\t            self:SetAlpha(WChat_Config.AlphaOnEnter)\n  1146\t            -- 显示鼠标提示\n  1147\t            if data.tooltip then\n  1148\t                GameTooltip:SetOwner(self, \&quot;ANCHOR_TOP\&quot;)\n  1149\t                GameTooltip:SetText(data.tooltip(), nil, nil, nil, nil, true)\n  1150\t                GameTooltip:Show()\n  1151\t            end\n  1152\t        end\n  1153\t    )\n  1154\t    frame:SetScript(\n  1155\t        \&quot;OnLeave\&quot;,\n  1156\t        function(self)\n  1157\t            self:SetAlpha(WChat_Config.AlphaOnLeave)\n  1158\t            -- 隐藏鼠标提示\n  1159\t            GameTooltip:Hide()\n  1160\t        end\n  1161\t    )\n  1162\t    if WChat_Config.UseVertical then\n  1163\t        frame:SetPoint(\&quot;TOP\&quot;, WChatBar, \&quot;TOP\&quot;, 0, (1 - index) * WChat_Config.DistanceVertical)\n  1164\t    else\n  1165\t        frame:SetPoint(\&quot;LEFT\&quot;, WChatBar, \&quot;LEFT\&quot;, 10 + (index - 1) * WChat_Config.DistanceHorizontal, 0)\n  1166\t    end\n  1167\t    \n  1168\t    frame:RegisterForClicks(\&quot;AnyUp\&quot;)\n  1169\t    frame:SetScript(\&quot;OnClick\&quot;, data.callback)\n  1170\t    -- 显示的文字\n  1171\t    frameText = frame:CreateFontString(data.name .. \&quot;Text\&quot;, \&quot;OVERLAY\&quot;)\n  1172\t    -- 字体设置\n  1173\t    frameText:SetFont(STANDARD_TEXT_FONT, 15, \&quot;OUTLINE\&quot;)\n  1174\t    \n  1175\t    frameText:SetJustifyH(\&quot;CENTER\&quot;)\n  1176\t    frameText:SetWidth(26)\n  1177\t    frameText:SetHeight(26)\n  1178\t    frameText:SetText(data.text)\n  1179\t    frameText:SetPoint(\&quot;CENTER\&quot;, 0, 0)\n  1180\t    \n  1181\t    -- 文字按钮的颜色\n  1182\t    frameText:SetTextColor(data.color[1], data.color[2], data.color[3])\n  1183\t\n  1184\t    -- 创建频道屏蔽X图标\n  1185\t    if CHANNEL_CONFIG.MAPPINGS[data.name] then\n  1186\t        -- 创建文字X图标（更可靠）\n  1187\t        frame.X = frame:CreateFontString(nil, \&quot;OVERLAY\&quot;, \&quot;GameFontNormal\&quot;)\n  1188\t        frame.X:SetText(\&quot;X\&quot;)\n  1189\t        frame.X:SetTextColor(1, 0, 0, 0.8) -- 红色X\n  1190\t        frame.X:SetFont(\&quot;Fonts\\\\FRIZQT__.TTF\&quot;, 16, \&quot;OUTLINE\&quot;)\n  1191\t        frame.X:SetPoint(\&quot;CENTER\&quot;, frame, \&quot;CENTER\&quot;, 0, 0)\n  1192\t        frame.X:Hide() -- 默认隐藏\n  1193\t        print(\&quot;创建文字X图标: \&quot; .. data.name)\n  1194\t    end\n  1195\t\n  1196\t    -- 设置按钮名称用于识别\n  1197\t    frame.buttonName = data.name\n  1198\tend\n  1199\t\n  1200\tfunction WChat:InitChatBar()\n  1201\t\n  1202\t    WChatBar:SetFrameLevel(0)\n  1203\t\n  1204\t    -- 使用竖直布局\n  1205\t    if WChat_Config.UseVertical then\n  1206\t        -- 主框体宽度\n  1207\t        WChatBar:SetWidth(30)\n  1208\t        -- 主框体高度\n  1209\t        WChatBar:SetHeight(#ChannelButtons * WChat_Config.DistanceVertical + 10)\n  1210\t    else\n  1211\t        -- 主框体宽度\n  1212\t        WChatBar:SetWidth(#ChannelButtons * WChat_Config.DistanceHorizontal + 10)\n  1213\t        -- 主框体高度\n  1214\t        WChatBar:SetHeight(30)\n  1215\t    end\n  1216\t\n  1217\t    -- 上方聊天输入框\n  1218\t    if WChat_Config.UseTopInput then\n  1219\t        inputbox:ClearAllPoints()\n  1220\t        inputbox:SetPoint(\&quot;BOTTOMLEFT\&quot;, chatFrame, \&quot;TOPLEFT\&quot;, 0, 20)\n  1221\t        inputbox:SetPoint(\&quot;BOTTOMRIGHT\&quot;, chatFrame, \&quot;TOPRIGHT\&quot;, 0, 20)\n  1222\t    end\n  1223\t    \n  1224\t    -- 位置设定\n  1225\t    if WChat_Config.Position == nil then\n  1226\t        if WChat_Config.UseVertical then\n  1227\t            if WChat_Config.UseTopChatbar then\n  1228\t                WChatBar:SetPoint(\&quot;TOPRIGHT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX - 30, WChat_Config.ChatBarOffsetY + 25)\n  1229\t            else\n  1230\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPRIGHT\&quot;, WChat_Config.ChatBarOffsetX + 30, WChat_Config.ChatBarOffsetY + 25)\n  1231\t            end\n  1232\t        else\n  1233\t            if WChat_Config.UseTopChatbar then\n  1234\t                WChatBar:SetPoint(\&quot;BOTTOMLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY + 30)\n  1235\t            else\n  1236\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;BOTTOMLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY - 30)\n  1237\t            end\n  1238\t        end\n  1239\t    else\n  1240\t        local point = WChat_Config.Position.point\n  1241\t        local relativeTo = WChat_Config.Position.relativeTo\n  1242\t        local relativePoint = WChat_Config.Position.relativePoint\n  1243\t        local xOfs = WChat_Config.Position.xOfs\n  1244\t        local yOfs = WChat_Config.Position.yOfs\n  1245\t        WChatBar:SetPoint(point, relativeTo, relativePoint, xOfs, yOfs)\n  1246\t    end\n  1247\t\n  1248\t    WChatBar:SetMovable(true)\n  1249\t    WChatBar:RegisterForDrag(\&quot;LeftButton\&quot;)\n  1250\t    WChatBar:SetScript(\&quot;OnDragStart\&quot;, WChatBar.StartMoving)\n  1251\t    WChatBar:SetScript(\&quot;OnDragStop\&quot;, WChatBar.StopMovingOrSizing)\n  1252\t    \n  1253\t    -- 动态创建按钮，根据配置和插件存在性\n  1254\t    local buttonIndex = 1\n  1255\t    for i = 1, #ChannelButtons do\n  1256\t        local buttonData = ChannelButtons[i]\n  1257\t        local shouldShow = true\n  1258\t\n  1259\t        -- 检查插件按钮是否应该显示（必须已加载才显示）\n  1260\t        if buttonData.name == \&quot;biaoge\&quot; then\n  1261\t            shouldShow = WChat_Config.ShowBiaoGeButton and IsAddOnLoaded(\&quot;BiaoGe\&quot;)\n  1262\t        elseif buttonData.name == \&quot;biaogeai\&quot; then\n  1263\t            shouldShow = WChat_Config.ShowBiaoGeAIButton and IsAddOnLoaded(\&quot;BiaoGeAI\&quot;)\n  1264\t        elseif buttonData.name == \&quot;atlas\&quot; then\n  1265\t            shouldShow = WChat_Config.ShowAtlasButton and IsAddOnLoaded(\&quot;AtlasLootClassic\&quot;)\n  1266\t        elseif buttonData.name == \&quot;meetinghorn\&quot; then\n  1267\t            shouldShow = WChat_Config.ShowMeetingHornButton and IsAddOnLoaded(\&quot;MeetingHorn\&quot;)\n  1268\t        end\n  1269\t\n  1270\t        if shouldShow then\n  1271\t            CreateChannelButton(buttonData, buttonIndex)\n  1272\t            buttonIndex = buttonIndex + 1\n  1273\t        end\n  1274\t    end\n  1275\t\n  1276\t    -- 延迟更新频道X图标状态\n  1277\t    C_Timer.After(0.5, UpdateChannelXIcons)\n  1278\t\n  1279\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 聊天条加载完毕\&quot;)\n  1280\tend\n  1281\t\n  1282\t-- 添加StatReport函数\n  1283\tfunction WChat:StatReport()\n  1284\t    -- 属性报告功能 (参考alaChat实现)\n  1285\t    local function GetItemLevel()\n  1286\t        local slots = { 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15 }\n  1287\t        local playerClass = UnitClassBase('player')\n  1288\t        if playerClass ~= \&quot;DRUID\&quot; and playerClass ~= \&quot;PALADIN\&quot; and playerClass ~= \&quot;SHAMAN\&quot; then\n  1289\t            slots[#slots + 1] = 18 -- 远程武器槽\n  1290\t        end\n  1291\t        slots[#slots + 1] = 16 -- 主手\n  1292\t        slots[#slots + 1] = 17 -- 副手\n  1293\t\n  1294\t        local total = 0\n  1295\t        local num = 0\n  1296\t        for index = 1, #slots do\n  1297\t            local slot = slots[index]\n  1298\t            local item = GetInventoryItemLink('player', slot)\n  1299\t            if item ~= nil and item ~= \&quot;\&quot; then\n  1300\t                local _, _, _, level, _, _, _, _, loc = GetItemInfo(item)\n  1301\t                if level ~= nil then\n  1302\t                    total = total + level\n  1303\t                    num = num + 1\n  1304\t                end\n  1305\t                if slot == 16 and loc == \&quot;INVTYPE_2HWEAPON\&quot; then\n  1306\t                    break -- 双手武器不计算副手\n  1307\t                end\n  1308\t            end\n  1309\t        end\n  1310\t        if num == 0 then return nil end\n  1311\t        local lvl = total / num + 0.05\n  1312\t        return math.floor(lvl * 10) / 10\n  1313\t    end\n  1314\t\n  1315\t    local function GetTalentDesc()\n  1316\t        local _, n1, _, _, p1 = GetTalentTabInfo(1)\n  1317\t        local _, n2, _, _, p2 = GetTalentTabInfo(2)\n  1318\t        local _, n3, _, _, p3 = GetTalentTabInfo(3)\n  1319\t        if p1 == p2 or p2 == p3 or p1 == p3 then\n  1320\t            return \&quot;天赋 (\&quot; .. p1 .. \&quot;/\&quot; .. p2 .. \&quot;/\&quot; .. p3 .. \&quot;)\&quot;\n  1321\t        elseif p1 &gt; p2 and p1 &gt; p3 then\n  1322\t            return \&quot;天赋 \&quot; .. n1 .. \&quot;(\&quot; .. p1 .. \&quot;/\&quot; .. p2 .. \&quot;/\&quot; .. p3 .. \&quot;)\&quot;\n  1323\t        elseif p2 &gt; p1 and p2 &gt; p3 then\n  1324\t            return \&quot;天赋 \&quot; .. n2 .. \&quot;(\&quot; .. p1 .. \&quot;/\&quot; .. p2 .. \&quot;/\&quot; .. p3 .. \&quot;)\&quot;\n  1325\t        else\n  1326\t            return \&quot;天赋 \&quot; .. n3 .. \&quot;(\&quot; .. p1 .. \&quot;/\&quot; .. p2 .. \&quot;/\&quot; .. p3 .. \&quot;)\&quot;\n  1327\t        end\n  1328\t    end\n  1329\t\n  1330\t    local function GetStatReport()\n  1331\t        local&lt;response clipped&gt;&lt;NOTE&gt;To save on context only part of this file has been shown to you.&lt;/NOTE&gt;\nTotal lines in file: 1605\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01JukCxKggeDfkqhWJy3BgW3&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;,&quot;folderRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-30T11:18:47.231Z&quot;,&quot;response_text&quot;:&quot;现在让我查看代码的其余部分，特别是垂直聊天条相关的代码：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;a835dba3-0b06-4603-9d75-d872757484e5&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01TJFVuqQWwJUJv9YkS2wFC9&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;\\u5782\\u76f4\\u804a\\u5929\\u6761\\u914d\\u7f6e\\u754c\\u9762\\u76f8\\u5173\\u4ee3\\u7801\\uff0c\\u5305\\u62ecUseVertical\\u590d\\u9009\\u6846\\u7684\\u521b\\u5efa\\u548c\\u4e8b\\u4ef6\\u5904\\u7406\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看配置界面文件，了解垂直聊天条的配置选项：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on Moudel/WChat.lua:\n  1330\t    local function GetStatReport()\n  1331\t        local ilv = GetItemLevel()\n  1332\t        local class, file = UnitClass('player')\n  1333\t        local health = UnitHealthMax('player')\n  1334\t        local mana = UnitPowerMax('player', 0)\n  1335\t\n  1336\t        -- 基础信息\n  1337\t        local report = class .. \&quot;, \&quot; .. \&quot;等级\&quot; .. UnitLevel('player') .. \&quot;, \&quot; .. GetTalentDesc()\n  1338\t        if ilv then\n  1339\t            report = report .. \&quot;, 装等 \&quot; .. ilv\n  1340\t        end\n  1341\t        report = report .. \&quot;, 生命 \&quot; .. health\n  1342\t        if mana &gt; 0 then\n  1343\t            report = report .. \&quot;, 法力 \&quot; .. mana\n  1344\t        end\n  1345\t\n  1346\t        -- 根据职业添加特定属性\n  1347\t        if file == \&quot;WARRIOR\&quot; then\n  1348\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1349\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1350\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1351\t            if p3 &gt;= p1 and p3 &gt;= p2 then\n  1352\t                -- 防护战士\n  1353\t                local _, armor = UnitArmor('player')\n  1354\t                local pChance = GetParryChance()\n  1355\t                local dChance = GetDodgeChance()\n  1356\t                local bChance = GetBlockChance()\n  1357\t                local block = GetShieldBlock()\n  1358\t                report = report .. \&quot;, 护甲 \&quot; .. armor\n  1359\t                report = report .. \&quot;, 招架 \&quot; .. string.format(\&quot;%.1f\&quot;, pChance) .. \&quot;%\&quot;\n  1360\t                report = report .. \&quot;, 躲闪 \&quot; .. string.format(\&quot;%.1f\&quot;, dChance) .. \&quot;%\&quot;\n  1361\t                report = report .. \&quot;, 格挡 \&quot; .. string.format(\&quot;%.1f\&quot;, bChance) .. \&quot;% (\&quot; .. block .. \&quot;)\&quot;\n  1362\t            else\n  1363\t                -- 输出战士\n  1364\t                local apBase, apPos, apNeg = UnitAttackPower('player')\n  1365\t                local critChance = GetCritChance()\n  1366\t                report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1367\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1368\t            end\n  1369\t        elseif file == \&quot;HUNTER\&quot; then\n  1370\t            local rapBase, rapPos, rapNeg = UnitRangedAttackPower('player')\n  1371\t            local critChance = GetRangedCritChance()\n  1372\t            report = report .. \&quot;, 远攻 \&quot; .. (rapBase + rapPos + rapNeg)\n  1373\t            report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1374\t        elseif file == \&quot;MAGE\&quot; or file == \&quot;WARLOCK\&quot; then\n  1375\t            local sp = GetSpellBonusDamage(2)\n  1376\t            local critChance = GetSpellCritChance(2)\n  1377\t            report = report .. \&quot;, 法强 \&quot; .. sp\n  1378\t            report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1379\t        elseif file == \&quot;PRIEST\&quot; then\n  1380\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1381\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1382\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1383\t            if p3 &gt;= p1 and p3 &gt;= p2 then\n  1384\t                -- 暗牧\n  1385\t                local sp = GetSpellBonusDamage(2)\n  1386\t                local critChance = GetSpellCritChance(2)\n  1387\t                report = report .. \&quot;, 法强 \&quot; .. sp\n  1388\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1389\t            else\n  1390\t                -- 治疗牧师\n  1391\t                local heal = GetSpellBonusHealing()\n  1392\t                local critChance = GetSpellCritChance(2)\n  1393\t                report = report .. \&quot;, 治疗 \&quot; .. heal\n  1394\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1395\t            end\n  1396\t        elseif file == \&quot;PALADIN\&quot; then\n  1397\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1398\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1399\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1400\t            if p1 &gt;= p2 and p1 &gt;= p3 then\n  1401\t                -- 神圣骑士\n  1402\t                local heal = GetSpellBonusHealing()\n  1403\t                local critChance = GetSpellCritChance(2)\n  1404\t                report = report .. \&quot;, 治疗 \&quot; .. heal\n  1405\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1406\t            elseif p2 &gt;= p1 and p2 &gt;= p3 then\n  1407\t                -- 防护骑士\n  1408\t                local _, armor = UnitArmor('player')\n  1409\t                local pChance = GetParryChance()\n  1410\t                local dChance = GetDodgeChance()\n  1411\t                local bChance = GetBlockChance()\n  1412\t                local block = GetShieldBlock()\n  1413\t                report = report .. \&quot;, 护甲 \&quot; .. armor\n  1414\t                report = report .. \&quot;, 招架 \&quot; .. string.format(\&quot;%.1f\&quot;, pChance) .. \&quot;%\&quot;\n  1415\t                report = report .. \&quot;, 躲闪 \&quot; .. string.format(\&quot;%.1f\&quot;, dChance) .. \&quot;%\&quot;\n  1416\t                report = report .. \&quot;, 格挡 \&quot; .. string.format(\&quot;%.1f\&quot;, bChance) .. \&quot;% (\&quot; .. block .. \&quot;)\&quot;\n  1417\t            else\n  1418\t                -- 惩戒骑士\n  1419\t                local apBase, apPos, apNeg = UnitAttackPower('player')\n  1420\t                local critChance = GetCritChance()\n  1421\t                report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1422\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1423\t            end\n  1424\t        elseif file == \&quot;ROGUE\&quot; then\n  1425\t            local apBase, apPos, apNeg = UnitAttackPower('player')\n  1426\t            local critChance = GetCritChance()\n  1427\t            report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1428\t            report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1429\t        elseif file == \&quot;DRUID\&quot; then\n  1430\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1431\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1432\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1433\t            if p1 &gt; p2 and p1 &gt;= p3 then\n  1434\t                -- 平衡德鲁伊\n  1435\t                local sp = GetSpellBonusDamage(4) -- 自然法术\n  1436\t                local critChance = GetSpellCritChance(4)\n  1437\t                report = report .. \&quot;, 法强 \&quot; .. sp\n  1438\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1439\t            elseif p3 &gt;= p1 and p3 &gt;= p2 then\n  1440\t                -- 恢复德鲁伊\n  1441\t                local heal = GetSpellBonusHealing()\n  1442\t                local critChance = GetSpellCritChance(4)\n  1443\t                report = report .. \&quot;, 治疗 \&quot; .. heal\n  1444\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1445\t            else\n  1446\t                -- 野性德鲁伊 (需要根据形态判断)\n  1447\t                local apBase, apPos, apNeg = UnitAttackPower('player')\n  1448\t                local critChance = GetCritChance()\n  1449\t                report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1450\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1451\t            end\n  1452\t        elseif file == \&quot;SHAMAN\&quot; then\n  1453\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1454\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1455\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1456\t            if p1 &gt; p2 and p1 &gt;= p3 then\n  1457\t                -- 元素萨满\n  1458\t                local sp = GetSpellBonusDamage(4) -- 自然法术\n  1459\t                local critChance = GetSpellCritChance(4)\n  1460\t                report = report .. \&quot;, 法强 \&quot; .. sp\n  1461\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1462\t            elseif p2 &gt;= p1 and p2 &gt;= p3 then\n  1463\t                -- 增强萨满\n  1464\t                local apBase, apPos, apNeg = UnitAttackPower('player')\n  1465\t                local critChance = GetCritChance()\n  1466\t                report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1467\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1468\t            else\n  1469\t                -- 恢复萨满\n  1470\t                local heal = GetSpellBonusHealing()\n  1471\t                local critChance = GetSpellCritChance(4)\n  1472\t                report = report .. \&quot;, 治疗 \&quot; .. heal\n  1473\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1474\t            end\n  1475\t        end\n  1476\t\n  1477\t        return report\n  1478\t    end\n  1479\t\n  1480\t\n  1481\t    -- 使用新的详细属性报告\n  1482\t    return GetStatReport()\n  1483\tend\n  1484\t\n  1485\t\n  1486\t\n  1487\t-- 刷新聊天条按钮（当插件加载状态改变时调用）\n  1488\tfunction WChat:RefreshChatBarButtons()\n  1489\t    if not WChatBar then return end\n  1490\t\n  1491\t    -- 隐藏所有现有按钮\n  1492\t    for i = 1, WChatBar:GetNumChildren() do\n  1493\t        local child = select(i, WChatBar:GetChildren())\n  1494\t        if child and child:GetObjectType() == \&quot;Button\&quot; then\n  1495\t            child:Hide()\n  1496\t        end\n  1497\t    end\n  1498\t\n  1499\t    -- 重新创建按钮\n  1500\t    local buttonIndex = 1\n  1501\t    for i = 1, #ChannelButtons do\n  1502\t        local buttonData = ChannelButtons[i]\n  1503\t        local shouldShow = true\n  1504\t\n  1505\t        -- 检查插件按钮是否应该显示（必须已加载才显示）\n  1506\t        if buttonData.name == \&quot;biaoge\&quot; then\n  1507\t            shouldShow = WChat_Config.ShowBiaoGeButton and IsAddOnLoaded(\&quot;BiaoGe\&quot;)\n  1508\t        elseif buttonData.name == \&quot;biaogeai\&quot; then\n  1509\t            shouldShow = WChat_Config.ShowBiaoGeAIButton and IsAddOnLoaded(\&quot;BiaoGeAI\&quot;)\n  1510\t        elseif buttonData.name == \&quot;atlas\&quot; then\n  1511\t            shouldShow = WChat_Config.ShowAtlasButton and IsAddOnLoaded(\&quot;AtlasLootClassic\&quot;)\n  1512\t        elseif buttonData.name == \&quot;meetinghorn\&quot; then\n  1513\t            shouldShow = WChat_Config.ShowMeetingHornButton and IsAddOnLoaded(\&quot;MeetingHorn\&quot;)\n  1514\t        end\n  1515\t\n  1516\t        if shouldShow then\n  1517\t            CreateChannelButton(buttonData, buttonIndex)\n  1518\t            buttonIndex = buttonIndex + 1\n  1519\t        end\n  1520\t    end\n  1521\t\n  1522\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 聊天条按钮已刷新\&quot;)\n  1523\tend\n  1524\t\n  1525\t-- 模块初始化函数\n  1526\tfunction WChat:Initialize()\n  1527\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 开始初始化聊天增强模块...\&quot;)\n  1528\t\n  1529\t    -- 逐步初始化各个子模块，便于排查问题\n  1530\t    local success, err = pcall(function()\n  1531\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化频道功能...\&quot;)\n  1532\t        self:InitChannel()\n  1533\t\n  1534\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化表情功能...\&quot;)\n  1535\t        self:InitEmoteTableFrame()\n  1536\t\n  1537\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化聊天条...\&quot;)\n  1538\t        self:InitChatBar()\n  1539\t\n  1540\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化时间戳复制...\&quot;)\n  1541\t        self:InitTimestampCopy()\n  1542\t    end)\n  1543\t\n  1544\t    if success then\n  1545\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 聊天增强模块加载完成\&quot;)\n  1546\t    else\n  1547\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 模块初始化失败: \&quot; .. tostring(err))\n  1548\t    end\n  1549\tend\n  1550\t\n  1551\t-- 配置保存和加载\n  1552\tfunction WChat:LoadConfig()\n  1553\t    if WChatDB then\n  1554\t        for key, value in pairs(WChatDB) do\n  1555\t            if WChat_Config[key] ~= nil then\n  1556\t                WChat_Config[key] = value\n  1557\t            end\n  1558\t        end\n  1559\t    end\n  1560\tend\n  1561\t\n  1562\tfunction WChat:SaveConfig()\n  1563\t    WChatDB = {}\n  1564\t    for key, value in pairs(WChat_Config) do\n  1565\t        WChatDB[key] = value\n  1566\t    end\n  1567\tend\n  1568\t\n  1569\t-- 防止重复初始化\n  1570\tlocal WChat_Initialized = false\n  1571\t\n  1572\t-- 事件处理\n  1573\tlocal WChatFrame = CreateFrame(\&quot;Frame\&quot;)\n  1574\tWChatFrame:RegisterEvent(\&quot;PLAYER_LOGOUT\&quot;)\n  1575\tWChatFrame:SetScript(\&quot;OnEvent\&quot;, function(self, event, addonName)\n  1576\t    if event == \&quot;PLAYER_LOGOUT\&quot; then\n  1577\t        WChat:SaveConfig()\n  1578\t    end\n  1579\tend)\n  1580\t\n  1581\t-- 注册到WanTiny模块系统\n  1582\tif WanTiny_RegisterModule then\n  1583\t    WanTiny_RegisterModule(\&quot;WChat\&quot;, function()\n  1584\t        if not WChat_Initialized then\n  1585\t            WChat_Initialized = true\n  1586\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r WChat模块开始注册...\&quot;)\n  1587\t\n  1588\t            local success, err = pcall(function()\n  1589\t                WChat:LoadConfig()\n  1590\t                WChat:Initialize()\n  1591\t            end)\n  1592\t            if not success then\n  1593\t                print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 模块加载失败: \&quot; .. tostring(err))\n  1594\t            end\n  1595\t        end\n  1596\t    end)\n  1597\telse\n  1598\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r WanTiny_RegisterModule 函数不存在\&quot;)\n  1599\tend\n  1600\t\n  1601\t\n  1602\t\n  1603\t\n  1604\t\n  1605\t\nTotal lines in file: 1605\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01396ZibeUTnDRrNbgGhdieT&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;,&quot;folderRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-30T11:18:53.061Z&quot;,&quot;response_text&quot;:&quot;现在让我查看配置界面文件，了解垂直聊天条的配置选项：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;9386cad7-d5e5-45ab-9c6e-d7b27448c854&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_015G3qvKSiC6eYyqhveB6TUd&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;WanTinyUI.lua\&quot;, \&quot;type\&quot;: \&quot;file\&quot;, \&quot;search_query_regex\&quot;: \&quot;WChat.*\\u5782\\u76f4|UseVertical|\\u804a\\u5929\\u6761.*\\u5782\\u76f4\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看配置界面文件，找到垂直聊天条的配置界面：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: Moudel/WChat.lua\n...\n     7\t\n     8\t-- 模块配置\n     9\tlocal WChat = {}\n    10\tlocal WChat_Config = {\n    11\t    -- 默认配置\n    12\t    ShortChannel = true,        -- 频道标签精简\n    13\t    EnableEmoteInput = true,    -- 启用表情输入\n    14\t    EmoteIconSize = 16,         -- 表情图标大小\n    15\t    EmoteIconListSize = 20,     -- 表情列表图标大小\n    16\t    EmoteOffsetX = 0,           -- 表情面板X偏移\n    17\t    EmoteOffsetY = 30,          -- 表情面板Y偏移\n    18\t    UseVertical = false,        -- 聊天条垂直布局\n    19\t    UseTopChatbar = false,      -- 聊天条在上方\n    20\t    UseTopInput = false,        -- 输入框在上方\n    21\t    ChatBarOffsetX = 0,         -- 聊天条X偏移\n    22\t    ChatBarOffsetY = 0,         -- 聊天条Y偏移\n    23\t    DistanceVertical = -25,     -- 垂直间距\n    24\t    DistanceHorizontal = 25,    -- 水平间距\n    25\t    AlphaOnEnter = 1.0,         -- 鼠标悬停透明度\n    26\t    AlphaOnLeave = 0.7,         -- 鼠标离开透明度\n    27\t    Position = nil,             -- 聊天条位置\n    28\t    EnableTimestampCopy = true, -- 启用时间戳点击复制\n    29\t    TimestampFormat = \&quot;%H:%M:%S\&quot;, -- 时间戳格式\n    30\t    TimestampColor = { 1.0, 1.0, 1.0 }, -- 时间戳颜色\n    31\t    -- 插件按钮显示控制\n    32\t    ShowBiaoGeButton = true,    -- 显示BiaoGe按钮\n    33\t    ShowBiaoGeAIButton = true,  -- 显示BiaoGeAI按钮\n    34\t    ShowAtlasButton = true,     -- 显示Atlas按钮\n    35\t    ShowMeetingHornButton = true, -- 显示MeetingHorn按钮\n    36\t    -- 频道隐藏设置\n    37\t    HiddenChannels = {}\n    38\t}\n    39\t\n    40\t-- 频道配置映射\n    41\tlocal CHANNEL_CONFIG = {\n    42\t    MAPPINGS = {\n    43\t        chnGen = \&quot;综合\&quot;,\n    44\t        chnTrade = \&quot;交易\&quot;,\n    45\t        chnLFG = \&quot;寻求组队\&quot;,\n    46\t        world = \&quot;大脚世界频道\&quot;\n    47\t    }\n    48\t}\n    49\t\n    50\t-- 频道显示/隐藏管理函数\n    51\tlocal function IsChannelShown(channelName)\n    52\t    local channels = {GetChatWindowChannels(DEFAULT_CHAT_FRAME:GetID() or 1)}\n    53\t    for i = 1, #channels, 2 do\n    54\t        if channels[i] == channelName then return true end\n    55\t    end\n    56\t    return false\n    57\tend\n...\n   530\t\n   531\tlocal cycles = {\n   532\t        -- \&quot;说\&quot;\n   533\t        {\n   534\t            chatType = \&quot;SAY\&quot;,\n   535\t            use = function(self, editbox)\n   536\t                return 1\n   537\t            end\n   538\t        },\n   539\t        --大喊\n   540\t        {\n   541\t            chatType = \&quot;YELL\&quot;,\n   542\t            use = function(self, editbox)\n   543\t                return 1\n   544\t            end\n   545\t        },\n   546\t        --小队\n   547\t        {\n   548\t            chatType = \&quot;PARTY\&quot;,\n   549\t            use = function(self, editbox)\n   550\t                return IsInGroup()\n   551\t            end\n   552\t        },\n   553\t        --团队\n   554\t        {\n   555\t            chatType = \&quot;RAID\&quot;,\n   556\t            use = function(self, editbox)\n   557\t                return IsInRaid()\n   558\t            end\n   559\t        },\n   560\t        --实时聊天\n   561\t        {\n   562\t            chatType = \&quot;INSTANCE_CHAT\&quot;,\n   563\t            use = function(self, editbox)\n   564\t                return select(2, IsInInstance()) == \&quot;pvp\&quot;\n   565\t            end\n   566\t        },\n   567\t        --公会\n   568\t        {\n   569\t            chatType = \&quot;GUILD\&quot;,\n   570\t            use = function(self, editbox)\n   571\t                return IsInGuild()\n   572\t            end\n   573\t        },\n...\n   605\t\n   606\tlocal chatTypeBeforeSwitch, tellTargetBeforeSwitch --记录在频道和密语之间切换时的状态\n   607\tfunction ChatEdit_CustomTabPressed_Inner(self)\n   608\t    if strsub(tostring(self:GetText()), 1, 1) == \&quot;/\&quot; then\n   609\t        return\n   610\t    end\n   611\t    local currChatType = self:GetAttribute(\&quot;chatType\&quot;)\n   612\t    if (IsControlKeyDown()) then\n   613\t        if (currChatType == \&quot;WHISPER\&quot; or currChatType == \&quot;BN_WHISPER\&quot;) then\n   614\t            --记录之前的密语对象，以便后续切回\n   615\t            self:SetAttribute(\&quot;chatType\&quot;, chatTypeBeforeSwitch or \&quot;SAY\&quot;)\n   616\t            ChatEdit_UpdateHeader(self)\n   617\t            chatTypeBeforeSwitch = \&quot;WHISPER\&quot;\n   618\t            tellTargetBeforeSwitch = self:GetAttribute(\&quot;tellTarget\&quot;)\n   619\t            return --这里和下面不同，这里可以不返回true\n   620\t        else\n   621\t            local newTarget, newTargetType = ChatEdit_GetNextTellTarget()\n   622\t            if tellTargetBeforeSwitch or (newTarget and newTarget ~= \&quot;\&quot;) then\n...\n   789\t\n   790\t-----------------------------聊天条\n   791\t\n   792\t--[[=========================== 变量区 ==========================]]\n   793\t-- 是否可移动的标记\n   794\tlocal IsMovable = false -- 没事干别动这个，你改成ture那么进入游戏后聊天条就是可以移动的\n   795\t--[[=============================== END ==============================]]\n   796\tlocal chatFrame = SELECTED_DOCK_FRAME -- 聊天框架\n   797\tlocal inputbox = chatFrame.editBox -- 输入框\n   798\t\n   799\tCOLORSCHEME_BORDER = {0.3, 0.3, 0.3, 1}\n   800\t-- 边框颜色\n   801\t-- 主框架初始化\n   802\tlocal ChatBar = CreateFrame(\&quot;Frame\&quot;, nil, UIParent, BackdropTemplateMixin and \&quot;BackdropTemplate\&quot; or nil)\n   803\tWChatBar = ChatBar\n   804\t\n   805\tlocal function ChannelSay_OnClick()\n   806\t    ChatFrame_OpenChat(\&quot;/s \&quot; .. inputbox:GetText(), chatFrame)\n   807\tend\n   808\t\n   809\tlocal function ChannelYell_OnClick()\n   810\t    ChatFrame_OpenChat(\&quot;/y \&quot; .. inputbox:GetText(), chatFrame)\n   811\tend\n...\n  1067\t\n  1068\t            if relativeTo then\n  1069\t                WChat_Config.Position = {point = point, relativeTo = relativeTo:GetName(), relativePoint = relativePoint, xOfs = xOfs, yOfs = yOfs}\n  1070\t            else\n  1071\t                WChat_Config.Position = {point = point, relativeTo = nil, relativePoint = relativePoint, xOfs = xOfs, yOfs = yOfs}\n  1072\t            end\n  1073\t        else\n  1074\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cff00d200解锁聊天条|r\&quot;)\n  1075\t            IsMovable = true\n  1076\t            WChatBar:SetBackdrop(\n  1077\t                {\n  1078\t                    bgFile = \&quot;Interface/DialogFrame/UI-DialogBox-Background\&quot;,\n  1079\t                    edgeFile = \&quot;Interface/DialogFrame/UI-DialogBox-Border\&quot;,\n  1080\t                    tile = true,\n  1081\t                    tileSize = 16,\n  1082\t                    edgeSize = 16,\n  1083\t                    insets = {left = 4, right = 4, top = 4, bottom = 4}\n  1084\t                }\n  1085\t        )\n  1086\t        end\n  1087\t        WChatBar:EnableMouse(IsMovable)\n  1088\t    elseif button == \&quot;MiddleButton\&quot; then\n  1089\t        if IsMovable == false then\n  1090\t            return\n  1091\t        end\n  1092\t        WChatBar:ClearAllPoints()\n  1093\t        if WChat_Config.UseVertical then\n  1094\t            if WChat_Config.UseTopChatbar then\n  1095\t                WChatBar:SetPoint(\&quot;TOPRIGHT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX - 30, WChat_Config.ChatBarOffsetY + 25)\n  1096\t            else\n  1097\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPRIGHT\&quot;, WChat_Config.ChatBarOffsetX + 30, WChat_Config.ChatBarOffsetY + 25)\n  1098\t            end\n  1099\t        else\n  1100\t            if WChat_Config.UseTopChatbar then\n  1101\t                WChatBar:SetPoint(\&quot;BOTTOMLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY + 30)\n  1102\t            else\n  1103\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;BOTTOMLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY - 30)\n  1104\t            end\n  1105\t        end\n  1106\t    end\n  1107\tend\n...\n  1162\t    if WChat_Config.UseVertical then\n  1163\t        frame:SetPoint(\&quot;TOP\&quot;, WChatBar, \&quot;TOP\&quot;, 0, (1 - index) * WChat_Config.DistanceVertical)\n  1164\t    else\n  1165\t        frame:SetPoint(\&quot;LEFT\&quot;, WChatBar, \&quot;LEFT\&quot;, 10 + (index - 1) * WChat_Config.DistanceHorizontal, 0)\n  1166\t    end\n  1167\t    \n  1168\t    frame:RegisterForClicks(\&quot;AnyUp\&quot;)\n  1169\t    frame:SetScript(\&quot;OnClick\&quot;, data.callback)\n  1170\t    -- 显示的文字\n  1171\t    frameText = frame:CreateFontString(data.name .. \&quot;Text\&quot;, \&quot;OVERLAY\&quot;)\n  1172\t    -- 字体设置\n  1173\t    frameText:SetFont(STANDARD_TEXT_FONT, 15, \&quot;OUTLINE\&quot;)\n  1174\t    \n  1175\t    frameText:SetJustifyH(\&quot;CENTER\&quot;)\n  1176\t    frameText:SetWidth(26)\n  1177\t    frameText:SetHeight(26)\n  1178\t    frameText:SetText(data.text)\n  1179\t    frameText:SetPoint(\&quot;CENTER\&quot;, 0, 0)\n  1180\t    \n  1181\t    -- 文字按钮的颜色\n  1182\t    frameText:SetTextColor(data.color[1], data.color[2], data.color[3])\n...\n  1199\t\n  1200\tfunction WChat:InitChatBar()\n  1201\t\n  1202\t    WChatBar:SetFrameLevel(0)\n  1203\t\n  1204\t    -- 使用竖直布局\n  1205\t    if WChat_Config.UseVertical then\n  1206\t        -- 主框体宽度\n  1207\t        WChatBar:SetWidth(30)\n  1208\t        -- 主框体高度\n  1209\t        WChatBar:SetHeight(#ChannelButtons * WChat_Config.DistanceVertical + 10)\n  1210\t    else\n  1211\t        -- 主框体宽度\n  1212\t        WChatBar:SetWidth(#ChannelButtons * WChat_Config.DistanceHorizontal + 10)\n  1213\t        -- 主框体高度\n  1214\t        WChatBar:SetHeight(30)\n  1215\t    end\n  1216\t\n  1217\t    -- 上方聊天输入框\n  1218\t    if WChat_Config.UseTopInput then\n  1219\t        inputbox:ClearAllPoints()\n  1220\t        inputbox:SetPoint(\&quot;BOTTOMLEFT\&quot;, chatFrame, \&quot;TOPLEFT\&quot;, 0, 20)\n  1221\t        inputbox:SetPoint(\&quot;BOTTOMRIGHT\&quot;, chatFrame, \&quot;TOPRIGHT\&quot;, 0, 20)\n  1222\t    end\n  1223\t    \n  1224\t    -- 位置设定\n  1225\t    if WChat_Config.Position == nil then\n  1226\t        if WChat_Config.UseVertical then\n  1227\t            if WChat_Config.UseTopChatbar then\n  1228\t                WChatBar:SetPoint(\&quot;TOPRIGHT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX - 30, WChat_Config.ChatBarOffsetY + 25)\n  1229\t            else\n  1230\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPRIGHT\&quot;, WChat_Config.ChatBarOffsetX + 30, WChat_Config.ChatBarOffsetY + 25)\n  1231\t            end\n  1232\t        else\n  1233\t            if WChat_Config.UseTopChatbar then\n  1234\t                WChatBar:SetPoint(\&quot;BOTTOMLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY + 30)\n  1235\t            else\n  1236\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;BOTTOMLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY - 30)\n  1237\t            end\n  1238\t        end\n  1239\t    else\n  1240\t        local point = WChat_Config.Position.point\n  1241\t        local relativeTo = WChat_Config.Position.relativeTo\n...\n  1252\t    \n  1253\t    -- 动态创建按钮，根据配置和插件存在性\n  1254\t    local buttonIndex = 1\n  1255\t    for i = 1, #ChannelButtons do\n  1256\t        local buttonData = ChannelButtons[i]\n  1257\t        local shouldShow = true\n  1258\t\n  1259\t        -- 检查插件按钮是否应该显示（必须已加载才显示）\n  1260\t        if buttonData.name == \&quot;biaoge\&quot; then\n  1261\t            shouldShow = WChat_Config.ShowBiaoGeButton and IsAddOnLoaded(\&quot;BiaoGe\&quot;)\n  1262\t        elseif buttonData.name == \&quot;biaogeai\&quot; then\n  1263\t            shouldShow = WChat_Config.ShowBiaoGeAIButton and IsAddOnLoaded(\&quot;BiaoGeAI\&quot;)\n  1264\t        elseif buttonData.name == \&quot;atlas\&quot; then\n  1265\t            shouldShow = WChat_Config.ShowAtlasButton and IsAddOnLoaded(\&quot;AtlasLootClassic\&quot;)\n  1266\t        elseif buttonData.name == \&quot;meetinghorn\&quot; then\n  1267\t            shouldShow = WChat_Config.ShowMeetingHornButton and IsAddOnLoaded(\&quot;MeetingHorn\&quot;)\n  1268\t        end\n  1269\t\n  1270\t        if shouldShow then\n  1271\t            CreateChannelButton(buttonData, buttonIndex)\n  1272\t            buttonIndex = buttonIndex + 1\n  1273\t        end\n  1274\t    end\n...\n  1498\t\n  1499\t    -- 重新创建按钮\n  1500\t    local buttonIndex = 1\n  1501\t    for i = 1, #ChannelButtons do\n  1502\t        local buttonData = ChannelButtons[i]\n  1503\t        local shouldShow = true\n  1504\t\n  1505\t        -- 检查插件按钮是否应该显示（必须已加载才显示）\n  1506\t        if buttonData.name == \&quot;biaoge\&quot; then\n  1507\t            shouldShow = WChat_Config.ShowBiaoGeButton and IsAddOnLoaded(\&quot;BiaoGe\&quot;)\n  1508\t        elseif buttonData.name == \&quot;biaogeai\&quot; then\n  1509\t            shouldShow = WChat_Config.ShowBiaoGeAIButton and IsAddOnLoaded(\&quot;BiaoGeAI\&quot;)\n  1510\t        elseif buttonData.name == \&quot;atlas\&quot; then\n  1511\t            shouldShow = WChat_Config.ShowAtlasButton and IsAddOnLoaded(\&quot;AtlasLootClassic\&quot;)\n  1512\t        elseif buttonData.name == \&quot;meetinghorn\&quot; then\n  1513\t            shouldShow = WChat_Config.ShowMeetingHornButton and IsAddOnLoaded(\&quot;MeetingHorn\&quot;)\n  1514\t        end\n  1515\t\n  1516\t        if shouldShow then\n  1517\t            CreateChannelButton(buttonData, buttonIndex)\n  1518\t            buttonIndex = buttonIndex + 1\n  1519\t        end\n  1520\t    end\n...\nPath: WanTinyUI.lua\n...\n   627\t\n   628\t    -- 创建模块复选框\n   629\t    ControlFactory.CreateGrid(parent, MODULE_CONFIGS, 4, WanTinyUI.LAYOUT.CHECKBOX_WIDTH, currentY, function(parent, config, x, y)\n   630\t        local moduleName, displayName, description = config[1], config[2], config[3]\n   631\t        local checkBox = ControlFactory.CreateCheckbox(parent, displayName, x, y,\n   632\t            enabledModules[moduleName] ~= false,\n   633\t            function(self) enabledModules[moduleName] = self:GetChecked() end, description)\n   634\t        return checkBox\n   635\t    end)\n...\n   650\t\n   651\t    local function updateMinimap()\n   652\t        C_Timer.After(0.1, function()\n   653\t            if _G.WanTiny_RestoreAllCollectedButtons then _G.WanTiny_RestoreAllCollectedButtons() end\n   654\t            if WanTinyDB.Map.MiniButShouNa_YN == 1 and _G.WanTiny_ShowAllCollectedButtons then _G.WanTiny_ShowAllCollectedButtons() end\n   655\t        end)\n   656\t    end\n   657\t\n   658\t    local controlWidth, spacing = WanTinyUI.LAYOUT.SLIDER_WIDTH, WanTinyUI.LAYOUT.EXTRA_SPACING\n   659\t    local startX = LayoutUtils.GetCenteredStartXSafe(parent, controlWidth, 2, spacing)\n   660\t\n   661\t    -- 第1行：复选框和下拉框\n   662\t    local checkBox = CreateLabeledCheckButton(parent, \&quot;启用收纳\&quot;, startX, currentY,\n   663\t        WanTinyDB.Map.MiniButShouNa_YN==1,\n   664\t        function(self) WanTinyDB.Map.MiniButShouNa_YN = self:GetChecked() and 1 or 2; updateMinimap() end)\n...\n   816\t\n   817\t        local cfg = GetWanMenuConfig()\n   818\t        local checkboxStartX = LayoutUtils.GetCenteredStartXSafe(parent, WanTinyUI.LAYOUT.CHECKBOX_WIDTH, 3, 0)\n   819\t\n   820\t        -- 创建复选框（独立行）\n   821\t        for i, ctrl in ipairs(markControls) do\n   822\t            if ctrl.type == \&quot;checkbox\&quot; then\n   823\t                local x = checkboxStartX + (i-1) * WanTinyUI.LAYOUT.CHECKBOX_WIDTH\n   824\t                local value = cfg[ctrl.key] ~= false\n   825\t                CreateLabeledCheckButton(parent, ctrl.label, x, currentY, value, function(self) UpdateWanMenuConfig(ctrl.key, self:GetChecked()) end, ctrl.desc)\n   826\t            end\n   827\t        end\n   828\t        currentY = currentY - 35  -- 复选框占用1行\n   829\t\n   830\t        -- 创建滑块（独立行，两个滑块在同一行）\n   831\t        local sliderWidth = WanTinyUI.LAYOUT.SLIDER_WIDTH\n   832\t        local sliderSpacing = 30\n   833\t        local sliderStartX = LayoutUtils.GetCenteredStartXSafe(parent, sliderWidth * 2 + sliderSpacing, 1, 0)\n   834\t        local sliderIndex = 0\n...\n   866\t\n   867\t-- 【标签页4】创建WCombatTimes战斗计时器设置面板\n   868\tfunction WanTinyUI.CreateWCombatTimesPanel(parent)\n   869\t    if parent.wcombatPanelCreated then return end\n   870\t\n   871\t\n   872\t\n   873\t    parent.wcombatPanelCreated = true\n   874\t\n   875\t    -- 检查模块加载状态\n   876\t    local wcombatLoaded = checkModuleLoaded(\&quot;WCombatTimes\&quot;)\n   877\t\n   878\t    local ROW_HEIGHT = 35  -- 标准行高35px\n   879\t    local currentY = -10   -- 起始坐标\n   880\t\n   881\t    -- WCombatTimes 战斗计时器设置\n   882\t    ControlFactory.CreateTitle(parent, \&quot;|cffff6b6b战斗计时器设置|r\&quot;, currentY)\n   883\t    currentY = currentY - WanTinyUI.LAYOUT.TITLE_SPACING\n   884\t\n   885\t    if wcombatLoaded then\n   886\t        local config = _G.WCT.Config\n   887\t        local SetConfig = function(k,v) _G.WCT.SetConfig(k,v) end\n   888\t\n   889\t    local function CreateCheck(text, x, y, key, tip, callback)\n   890\t        return ControlFactory.CreateCheckbox(parent, text, x, y, config[key] ~= false,\n   891\t            callback or function(self) SetConfig(key, self:GetChecked()) end, tip)\n   892\t    end\n   893\t\n   894\t    local function CreateEditBox(key, x, y, tip, options)\n   895\t        options = options or {}\n   896\t        local hasLabel = options.label ~= nil\n   897\t        local containerHeight = hasLabel and ROW_HEIGHT * 2 or 20\n   898\t\n   899\t        local container = CreateFrame(\&quot;Frame\&quot;, nil, parent)\n   900\t        container:SetSize(options.width or 120, containerHeight)\n   901\t        container:SetPoint(\&quot;TOPLEFT\&quot;, parent, \&quot;TOPLEFT\&quot;, x, y - (hasLabel and 0 or 7))\n...\n   957\t\n   958\t    -- 创建复选框\n   959\t    local checkConfigs = {\n   960\t        {\&quot;|cff00ff00显示计时器|r\&quot;, \&quot;showMainFrame\&quot;, \&quot;启用或禁用主计时器框体\&quot;, function(self)\n   961\t            local checked = self:GetChecked()\n   962\t            SetConfig(\&quot;showMainFrame\&quot;, checked)\n   963\t            local frame = LayoutUtils.SafeGetGlobal(\&quot;WCombatTimesFrame\&quot;)\n   964\t            if frame and frame.Show and frame.Hide then\n   965\t                frame[checked and \&quot;Show\&quot; or \&quot;Hide\&quot;](frame)\n   966\t            end\n   967\t        end},\n   968\t        {\&quot;|cffff8000显示横幅|r\&quot;, \&quot;showBanner\&quot;, \&quot;进入/离开战斗时显示横幅提醒\&quot;},\n   969\t        {\&quot;|cff00bfff进战音效|r\&quot;, \&quot;playSoundOnEnter\&quot;, \&quot;进入战斗时播放声音提醒\&quot;},\n   970\t        {\&quot;|cff00bfff脱战音效|r\&quot;, \&quot;playSoundOnLeave\&quot;, \&quot;离开战斗时播放声音提醒\&quot;}\n   971\t    }\n   972\t\n   973\t    local checkBoxes = ControlFactory.CreateGrid(parent, checkConfigs, 4, WanTinyUI.LAYOUT.CHECKBOX_WIDTH, currentY, function(parent, cfg, x, y)\n   974\t        return CreateCheck(cfg[1], x, y, cfg[2], cfg[3], cfg[4])\n   975\t    end)\n   976\t    currentY = currentY - ROW_HEIGHT  -- 复选框占用1行\n   977\t\n   978\t    -- 创建输入框\n   979\t    local editConfigs = {\n   980\t        {\&quot;进战主文字\&quot;, \&quot;enterBannerTitle\&quot;},\n   981\t        {\&quot;进战次文字\&quot;, \&quot;enterBannerLabel\&quot;},\n   982\t        {\&quot;脱战主文字\&quot;, \&quot;leaveBannerTitle\&quot;}\n   983\t    }\n...\nPath: Moudel/WaPlus.lua\n...\n   181\t        local enableBtn = CreateFrame(\&quot;CheckButton\&quot;, nil, f, \&quot;ChatConfigCheckButtonTemplate\&quot;)\n   182\t        enableBtn:SetSize(20, 20)\n   183\t        enableBtn:SetPoint(\&quot;TOPLEFT\&quot;, 20, -35)\n   184\t        enableBtn.Text:SetText(\&quot;启用欢迎语\&quot;)\n   185\t        enableBtn.Text:ClearAllPoints()\n   186\t        enableBtn.Text:SetPoint(\&quot;LEFT\&quot;, enableBtn, \&quot;RIGHT\&quot;, 2, 2)\n   187\t        enableBtn:SetHitRectInsets(0, 0, 0, 0)\n   188\t        -- 频道单选按钮组（绝对定位，横向等距）\n   189\t        local channelConfig = {\n   190\t            labels = { {\&quot;|cFFE066FF密语|r\&quot;,\&quot;WHISPER\&quot;}, {\&quot;|cFFFFA500团队|r\&quot;,\&quot;RAID\&quot;}, {\&quot;|cFF66B3FF小队|r\&quot;,\&quot;PARTY\&quot;} },\n   191\t            btns = {}, baseX = 120, baseY = -35, spacing = 70\n   192\t        }\n   193\t        -- 启用/禁用频道选择（只控制可点，不再动态变灰，颜色由颜色码决定）\n   194\t        local function updateChannelEnable()\n   195\t            local enabled = enableBtn:GetChecked()\n   196\t            for _, b in ipairs(channelConfig.btns) do b:EnableMouse(enabled) end\n   197\t        end\n   198\t        enableBtn:SetScript(\&quot;OnClick\&quot;, function(self)\n   199\t            getWelcomeConfig().enabled = self:GetChecked()\n   200\t            updateChannelEnable()\n   201\t        end)\n   202\t        for i, v in ipairs(channelConfig.labels) do\n   203\t            local btn = CreateFrame(\&quot;CheckButton\&quot;, nil, f, \&quot;ChatConfigCheckButtonTemplate\&quot;)\n   204\t            btn:SetSize(20, 20)\n   205\t            btn:SetPoint(\&quot;TOPLEFT\&quot;, f, \&quot;TOPLEFT\&quot;, channelConfig.baseX + (i-1)*channelConfig.spacing, channelConfig.baseY)\n   206\t            btn.Text:ClearAllPoints(); btn.Text:SetPoint(\&quot;LEFT\&quot;, btn, \&quot;RIGHT\&quot;, 2, 2)\n   207\t            btn.Text:SetText(v[1])\n   208\t            btn:SetHitRectInsets(0, 0, 0, 0)\n   209\t            btn:SetScript(\&quot;OnClick\&quot;, function(self)\n...\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01TJFVuqQWwJUJv9YkS2wFC9&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;,&quot;folderRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-30T11:19:05.170Z&quot;,&quot;response_text&quot;:&quot;现在让我查看配置界面文件，找到垂直聊天条的配置界面：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;60dc19e5-3a78-4190-83b2-98de0abd20b6&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01N28mWw2CqfGZkm2ma8fCt9&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;WanTinyUI.lua\&quot;, \&quot;type\&quot;: \&quot;file\&quot;, \&quot;search_query_regex\&quot;: \&quot;WChat|\\u804a\\u5929\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;让我搜索WChat相关的配置界面：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;No matches found for regex pattern: WChat.*垂直|UseVertical|聊天条.*垂直 in WanTinyUI.lua&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_015G3qvKSiC6eYyqhveB6TUd&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;,&quot;folderRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-30T11:19:13.700Z&quot;,&quot;response_text&quot;:&quot;让我搜索WChat相关的配置界面：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;c49fb4aa-7dea-4689-9a4a-a14d0bc08b07&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01V9XpgBBvC6HSAc5FMZgrtj&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;WanTinyUI.lua\&quot;, \&quot;type\&quot;: \&quot;file\&quot;, \&quot;search_query_regex\&quot;: \&quot;CreateWChatPanel|WChatPanel|function.*WChat\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;让我查找WChat配置面板的创建函数：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Regex search results for pattern: WChat|聊天 in WanTinyUI.lua\nFound 1 matching lines:\n\n    662\t    local checkBox = CreateLabeledCheckButton(parent, \&quot;启用收纳\&quot;, startX, currentY,\n    663\t        WanTinyDB.Map.MiniButShouNa_YN==1,\n    664\t        function(self) WanTinyDB.Map.MiniButShouNa_YN = self:GetChecked() and 1 or 2; updateMinimap() end)\n    665\t\n    666\t    local dropDown = CreateLabeledDropDown(parent, \&quot;按钮位置：\&quot;, startX+controlWidth+spacing, currentY,\n&gt;   667\t        {[1]=\&quot;小地图\&quot;,[2]=\&quot;聊天框\&quot;,[3]=\&quot;自由\&quot;}, WanTinyDB.Map.MinimapPointMode,\n    668\t        function(k) WanTinyDB.Map.MinimapPointMode=k; if _G.WanTiny_MiniMapBut and _G.WanTiny_MiniMapBut.SetButtonStyle then _G.WanTiny_MiniMapBut:SetButtonStyle(k) end end)\n    669\t    currentY = currentY - 35  -- 第1行完成\n    670\t\n    671\t    -- 第2行：两个滑块\n    672\t    local slider1 = CreateLabeledSlider(parent, \&quot;每行按钮：\&quot;, startX, currentY, 3, 12, 1,\n\nTotal matches: 1\nTotal lines in file: 1186&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01N28mWw2CqfGZkm2ma8fCt9&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;,&quot;folderRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-30T11:19:18.242Z&quot;,&quot;response_text&quot;:&quot;让我查找WChat配置面板的创建函数：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;ab8240d1-deb9-4933-aad1-79851c382287&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_011aB3ujoRbKhJMwFu4mccfm&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;WanTinyUI.lua\&quot;, \&quot;type\&quot;: \&quot;file\&quot;, \&quot;search_query_regex\&quot;: \&quot;function.*CreatePanel|function.*Panel\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;让我搜索所有的面板创建函数：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;No matches found for regex pattern: CreateWChatPanel|WChatPanel|function.*WChat in WanTinyUI.lua&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01V9XpgBBvC6HSAc5FMZgrtj&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;,&quot;folderRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-30T11:19:23.595Z&quot;,&quot;response_text&quot;:&quot;让我搜索所有的面板创建函数：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;06b4cd34-bda3-4b0d-8db3-6f1d3bc9d5d8&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01XbfGq1QqT33jGNgDQZrEXR&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;WanTinyUI.lua\&quot;, \&quot;type\&quot;: \&quot;file\&quot;, \&quot;search_query_regex\&quot;: \&quot;\\u6807\\u7b7e\\u9875|tab|Tab\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;看起来WChat模块没有专门的配置面板。让我查看主配置界面的标签页定义：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Regex search results for pattern: function.*CreatePanel|function.*Panel in WanTinyUI.lua\nFound 4 matching lines:\n\n    613\t    {\&quot;WCombatTimes\&quot;, \&quot;|cffff6b6b战斗计时|r\&quot;, \&quot;显示战斗持续时间和战斗状态变化提醒，支持自定义横幅和声音\&quot;},\n    614\t\n    615\t}\n    616\t\n    617\t-- 【标签页1】创建模块开关设置面板\n&gt;   618\tfunction WanTinyUI.CreateSettingsPanel(parent)\n    619\t    _G.WanTinyDB = _G.WanTinyDB or {config = {enabledModules = {}}}\n    620\t    _G.WanTinyDB.Map = _G.WanTinyDB.Map or {MiniButShouNa_YN = 1, MinimapPointMode = 1, MiniButPerRow = 6, MiniButHideDelay = 1.5}\n    621\t    local enabledModules = _G.WanTinyDB.config.enabledModules\n    622\t\n    623\t    -- 模块开关标题\n...\n    709\t        slider.valueText:SetText(formatValue(setting.default))\n    710\t    end\n    711\tend\n    712\t\n    713\t-- 【标签页2】创建CVar设置面板\n&gt;   714\tfunction WanTinyUI.CreateCVarPanel(parent)\n    715\t    -- 检查模块加载状态，分别处理两个模块\n    716\t    local wcvarLoaded = checkModuleLoaded(\&quot;WCvar\&quot;)\n    717\t    local wanmenuLoaded = checkModuleLoaded(\&quot;WanMenu\&quot;)\n    718\t\n    719\t    local currentY = -10\n...\n    847\t        createModuleNotLoadedText(parent, \&quot;WanMenu\&quot;, \&quot;TOP\&quot;, parent, 0, currentY)\n    848\t    end\n    849\tend\n    850\t\n    851\t-- 【标签页3】快捷标记面板（简化版）\n&gt;   852\tfunction WanTinyUI.CreateWanMenuPanel(parent)\n    853\t    if not checkModuleLoaded(\&quot;WanMenu\&quot;) then\n    854\t        createModuleNotLoadedText(parent, \&quot;WanMenu\&quot;)\n    855\t        return\n    856\t    end\n    857\t    \n...\n    863\t    desc:SetWidth(500)\n    864\t    desc:SetNonSpaceWrap(true)\n    865\tend\n    866\t\n    867\t-- 【标签页4】创建WCombatTimes战斗计时器设置面板\n&gt;   868\tfunction WanTinyUI.CreateWCombatTimesPanel(parent)\n    869\t    if parent.wcombatPanelCreated then return end\n    870\t\n    871\t\n    872\t\n    873\t    parent.wcombatPanelCreated = true\n\nTotal matches: 4\nTotal lines in file: 1186&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_011aB3ujoRbKhJMwFu4mccfm&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;,&quot;folderRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-30T11:19:28.610Z&quot;,&quot;response_text&quot;:&quot;看起来WChat模块没有专门的配置面板。让我查看主配置界面的标签页定义：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;5c8f1a6a-ade0-4753-b306-bd23a7e71b17&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01X7kLgAaFYXjF3bW2r5M7W1&quot;,&quot;tool_name&quot;:&quot;str-replace-editor&quot;,&quot;input_json&quot;:&quot;{\&quot;command\&quot;: \&quot;str_replace\&quot;, \&quot;path\&quot;: \&quot;WanTinyUI.lua\&quot;, \&quot;instruction_reminder\&quot;: \&quot;ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH.\&quot;, \&quot;old_str_1\&quot;: \&quot;    -- \\u6807\\u7b7e\\u9875\\u914d\\u7f6e\\u8868\\uff1a\\u5b9a\\u4e49\\u6bcf\\u4e2a\\u6807\\u7b7e\\u9875\\u7684\\u5185\\u5bb9\\u521b\\u5efa\\u65b9\\u5f0f\\n    local tabConfigs = {\\n        -- \\u6807\\u7b7e\\u98751: \\u6a21\\u5757\\u5f00\\u5173\\u9762\\u677f - \\u5305\\u542b17\\u4e2a\\u529f\\u80fd\\u6a21\\u5757\\u7684\\u542f\\u7528/\\u7981\\u7528\\u5f00\\u5173\\n        {createFunc = WanTinyUI.CreateSettingsPanel, visible = true},\\n\\n        -- \\u6807\\u7b7e\\u98752: CVar\\u8bbe\\u7f6e\\u9762\\u677f - \\u6e38\\u620f\\u53c2\\u6570\\u8c03\\u6574\\u754c\\u9762\\n        {createFunc = WanTinyUI.CreateCVarPanel, visible = false},\\n\\n        -- \\u6807\\u7b7e\\u98753: \\u5feb\\u6377\\u6807\\u8bb0\\u8bbe\\u7f6e\\u9762\\u677f\\n        {createFunc = WanTinyUI.CreateWanMenuPanel, visible = false},\\n\\n        -- \\u6807\\u7b7e\\u98754: WCombatTimes\\u6218\\u6597\\u8ba1\\u65f6\\u5668\\u8bbe\\u7f6e\\u9762\\u677f\\n        {createFunc = WanTinyUI.CreateWCombatTimesPanel, visible = false}\\n    }\&quot;, \&quot;new_str_1\&quot;: \&quot;    -- \\u6807\\u7b7e\\u9875\\u914d\\u7f6e\\u8868\\uff1a\\u5b9a\\u4e49\\u6bcf\\u4e2a\\u6807\\u7b7e\\u9875\\u7684\\u5185\\u5bb9\\u521b\\u5efa\\u65b9\\u5f0f\\n    local tabConfigs = {\\n        -- \\u6807\\u7b7e\\u98751: \\u6a21\\u5757\\u5f00\\u5173\\u9762\\u677f - \\u5305\\u542b17\\u4e2a\\u529f\\u80fd\\u6a21\\u5757\\u7684\\u542f\\u7528/\\u7981\\u7528\\u5f00\\u5173\\n        {createFunc = WanTinyUI.CreateSettingsPanel, visible = true},\\n\\n        -- \\u6807\\u7b7e\\u98752: CVar\\u8bbe\\u7f6e\\u9762\\u677f - \\u6e38\\u620f\\u53c2\\u6570\\u8c03\\u6574\\u754c\\u9762\\n        {createFunc = WanTinyUI.CreateCVarPanel, visible = false},\\n\\n        -- \\u6807\\u7b7e\\u98753: WChat\\u804a\\u5929\\u589e\\u5f3a\\u8bbe\\u7f6e\\u9762\\u677f\\n        {createFunc = WanTinyUI.CreateWChatPanel, visible = false},\\n\\n        -- \\u6807\\u7b7e\\u98754: WCombatTimes\\u6218\\u6597\\u8ba1\\u65f6\\u5668\\u8bbe\\u7f6e\\u9762\\u677f\\n        {createFunc = WanTinyUI.CreateWCombatTimesPanel, visible = false}\\n    }\&quot;, \&quot;old_str_start_line_number_1\&quot;: 559, \&quot;old_str_end_line_number_1\&quot;: 572}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在我明白了问题所在。根据代码分析，我发现了以下几个问题：\n\n## 问题分析\n\n1. **垂直聊天条复选框勾选后需要RL才会变化**：\n   - WChat模块没有专门的配置界面，配置是通过代码中的 `WChat_Config` 变量控制的\n   - 当前没有UI界面来动态修改 `UseVertical` 设置\n\n2. **垂直聊天条的起始位置需要和水平的起始位置一样**：\n   - 在 `InitChatBar()` 函数中，垂直和水平布局使用了不同的定位逻辑\n\n3. **垂直和水平是两个不同的个体**：\n   - 当前代码在切换垂直/水平布局时没有重新创建聊天条，只是在初始化时根据配置决定布局\n\n## 解决方案\n\n我需要：\n1. 为WChat模块创建一个配置界面\n2. 修复垂直聊天条的定位问题\n3. 添加动态切换垂直/水平布局的功能\n\n让我开始实施修复：\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Regex search results for pattern: 标签页|tab|Tab in WanTinyUI.lua\nFound 82 matching lines:\n\n     27\t\n     28\t        -- 偏移量\n     29\t        TEXT_OFFSET_Y = -2,     -- 文字垂直偏移\n     30\t        CHECKBOX_TEXT_OFFSET_X = 27,  -- 复选框文字水平偏移\n     31\t\n&gt;    32\t        -- 标签页布局\n     33\t        TAB_PADDING = 5,        -- 标签页边距\n     34\t        TAB_SPACING = 1,        -- 标签页间距\n     35\t        TAB_BOTTOM_OFFSET = -28, -- 标签页底部偏移\n     36\t\n     37\t        -- 特殊布局常量\n     38\t        TITLE_SPACING = 20,     -- 标题和内容间距\n     39\t        SECTION_GAP = 25,       -- 区块间隙\n     40\t        EXTRA_SPACING = 15,     -- 额外间距\n...\n    102\tfunction LayoutUtils.SafeGetGlobal(name)\n    103\t    return _G[name]\n    104\tend\n    105\t\n    106\t-- 通用配置更新函数\n&gt;   107\tfunction LayoutUtils.UpdateConfig(configTable, key, value, callbackTable)\n    108\t    configTable[key] = value\n    109\t    if callbackTable and callbackTable[key] then\n    110\t        callbackTable[key](value)\n    111\t    end\n    112\tend\n    113\t\n    114\t-- ==================== 布局工具 ====================\n    115\t\n...\n    328\tend\n    329\t\n    330\t-- 通用控件工厂 - 统一所有控件创建\n    331\tlocal ControlFactory = {\n    332\t    CreateButton = function(parent, text, width, height, onClick, tooltip)\n&gt;   333\t        local btn = WanTinyUI.CreateTabButton(parent, text, width or 80, height or 25)\n    334\t        if onClick then btn:SetScript(\&quot;OnClick\&quot;, onClick) end\n    335\t        if tooltip then setTooltip(btn, tooltip.title or text, tooltip.desc, \&quot;ANCHOR_TOP\&quot;) end\n    336\t        return btn\n    337\t    end,\n    338\t\n...\n    428\t    return f\n    429\tend\n    430\t\n    431\t\n    432\t\n&gt;   433\t-- 创建标签页按钮\n    434\tfunction WanTinyUI.CreateTabButton(parent, text, width, height)\n    435\t    local r, g, b = unpack(WanTinyUI.GetClassRGB())\n    436\t\n    437\t    local bt = CreateFrame(\&quot;Button\&quot;, nil, parent, \&quot;BackdropTemplate\&quot;)\n    438\t    setStandardBackdrop(bt, 0.07)\n    439\t    \n...\n    478\t    -- 创建主窗口\n    479\t    local f = WanTinyUI.CreateMainFrame(\&quot;WanTinyUI_MainFrame\&quot;, \&quot;|cff00bfff&lt;WanTiny&gt;|r|cffff80ff晚妹的小合集|r\&quot;)\n    480\t    WanTinyUI.MainFrame = f\n    481\t    \n    482\t    -- 左侧标题\n&gt;   483\t    WanTinyUI.TabTitleLeft = f:CreateFontString(nil, \&quot;ARTWORK\&quot;)\n    484\t    WanTinyUI.TabTitleLeft:SetFont(STANDARD_TEXT_FONT, 20, \&quot;OUTLINE\&quot;)\n    485\t    WanTinyUI.TabTitleLeft:SetPoint(\&quot;TOPLEFT\&quot;, f, \&quot;TOPLEFT\&quot;, 15, -25)\n    486\t    WanTinyUI.TabTitleLeft:SetTextColor(unpack(WanTinyUI.RGB(\&quot;00BFFF\&quot;)))\n    487\t\n    488\t    -- 右侧版本号\n    489\t    WanTinyUI.TabTitleRight = f:CreateFontString(nil, \&quot;ARTWORK\&quot;, \&quot;GameFontNormal\&quot;)\n    490\t    setFont(WanTinyUI.TabTitleRight, \&quot;LARGE\&quot;)\n    491\t    WanTinyUI.TabTitleRight:SetPoint(\&quot;TOPRIGHT\&quot;, f, \&quot;TOPRIGHT\&quot;, -18, -30)\n    492\t    WanTinyUI.TabTitleRight:SetTextColor(1, 1, 1)\n    493\t    WanTinyUI.CreateInstructionButton()\n    494\t    WanTinyUI.CreateMainContent()\n    495\t    tinsert(UISpecialFrames, \&quot;WanTinyUI_MainFrame\&quot;)\n    496\tend\n    497\t\n...\n    524\t        GameTooltip:Hide()\n    525\t        t:SetTextColor(unpack(COLORS.GREEN))\n    526\t    end)\n    527\tend\n    528\t\n&gt;   529\t-- 创建主内容区域（包含所有标签页内容的容器）\n    530\tfunction WanTinyUI.CreateMainContent()\n    531\t    local mainFrame = WanTinyUI.MainFrame\n    532\t    \n    533\t    -- 创建内容容器框架\n    534\t    local contentFrame = CreateFrame(\&quot;Frame\&quot;, nil, mainFrame, \&quot;BackdropTemplate\&quot;)\n...\n    541\t    })\n    542\t    contentFrame:SetBackdropColor(0, 0, 0, 0)\n    543\t    contentFrame:SetBackdropBorderColor(0.3, 0.3, 0.3, 0.5)\n    544\t    WanTinyUI.contentFrame = contentFrame\n    545\t    \n&gt;   546\t    -- 创建所有标签页的内容面板\n    547\t    WanTinyUI.CreateTabContents()\n    548\t    -- 创建底部标签页切换按钮\n    549\t    WanTinyUI.CreateBottomTabs()\n    550\tend\n    551\t\n    552\t-- ==================== 标签页内容 ====================\n    553\t\n    554\t-- 创建所有标签页的内容面板\n    555\tfunction WanTinyUI.CreateTabContents()\n    556\t    local contentFrame = WanTinyUI.contentFrame\n    557\t    WanTinyUI.tabContents = {}\n    558\t    \n    559\t    -- 标签页配置表：定义每个标签页的内容创建方式\n    560\t    local tabConfigs = {\n    561\t        -- 标签页1: 模块开关面板 - 包含17个功能模块的启用/禁用开关\n    562\t        {createFunc = WanTinyUI.CreateSettingsPanel, visible = true},\n    563\t\n    564\t        -- 标签页2: CVar设置面板 - 游戏参数调整界面\n    565\t        {createFunc = WanTinyUI.CreateCVarPanel, visible = false},\n    566\t\n    567\t        -- 标签页3: 快捷标记设置面板\n    568\t        {createFunc = WanTinyUI.CreateWanMenuPanel, visible = false},\n    569\t\n    570\t        -- 标签页4: WCombatTimes战斗计时器设置面板\n    571\t        {createFunc = WanTinyUI.CreateWCombatTimesPanel, visible = false}\n    572\t    }\n    573\t    \n    574\t    -- 为每个标签页创建对应的内容面板\n    575\t    for i, config in ipairs(tabConfigs) do\n    576\t        local tab = CreateFrame(\&quot;Frame\&quot;, nil, contentFrame)\n    577\t        tab:SetAllPoints(contentFrame)\n    578\t        if not config.visible then tab:Hide() end\n    579\t        WanTinyUI.tabContents[i] = tab\n    580\t        \n    581\t        -- 根据配置创建具体内容\n    582\t        if config.createFunc then\n    583\t            config.createFunc(tab)  -- 调用专门的创建函数\n    584\t        elseif config.text then\n    585\t            -- 创建占位文本\n    586\t            local text = tab:CreateFontString()\n    587\t            text:SetPoint(\&quot;CENTER\&quot;)\n    588\t            setFont(text, \&quot;LARGE\&quot;)\n    589\t            text:SetTextColor(0.7, 0.7, 0.7)\n    590\t            text:SetText(config.text)\n    591\t        end\n    592\t    end\n    593\tend\n    594\t\n&gt;   595\t-- 【标签页1】模块配置数据：17个功能模块的详细信息\n    596\tlocal MODULE_CONFIGS = {\n    597\t    {\&quot;BuffTimers\&quot;, \&quot;|cff00bfff光环计时|r\&quot;, \&quot;显示自身和队友的增益/减益持续时间\&quot;},\n    598\t    {\&quot;idTip\&quot;, \&quot;|cffffd200ID提示|r\&quot;, \&quot;鼠标提示中显示NPC/法术/物品ID\&quot;},\n    599\t    {\&quot;Focuser\&quot;, \&quot;|cffb8860b快捷焦点|r\&quot;, \&quot;快捷设置焦点目标，Shift+左键设置/取消焦点\&quot;},\n    600\t    {\&quot;RaidInfoFrame\&quot;, \&quot;|cffc41f3b团队信息|r\&quot;, \&quot;显示团队副本CD在团队面板\&quot;},\n...\n    612\t    {\&quot;WanMenu\&quot;, \&quot;|cffff80ff快捷标记|r\&quot;, \&quot;提供团队标记、倒计时等快捷功能，支持自定义大小和位置\&quot;},\n    613\t    {\&quot;WCombatTimes\&quot;, \&quot;|cffff6b6b战斗计时|r\&quot;, \&quot;显示战斗持续时间和战斗状态变化提醒，支持自定义横幅和声音\&quot;},\n    614\t\n    615\t}\n    616\t\n&gt;   617\t-- 【标签页1】创建模块开关设置面板\n    618\tfunction WanTinyUI.CreateSettingsPanel(parent)\n    619\t    _G.WanTinyDB = _G.WanTinyDB or {config = {enabledModules = {}}}\n    620\t    _G.WanTinyDB.Map = _G.WanTinyDB.Map or {MiniButShouNa_YN = 1, MinimapPointMode = 1, MiniButPerRow = 6, MiniButHideDelay = 1.5}\n    621\t    local enabledModules = _G.WanTinyDB.config.enabledModules\n    622\t\n...\n    708\t        slider:SetValue(setting.default)\n    709\t        slider.valueText:SetText(formatValue(setting.default))\n    710\t    end\n    711\tend\n    712\t\n&gt;   713\t-- 【标签页2】创建CVar设置面板\n    714\tfunction WanTinyUI.CreateCVarPanel(parent)\n    715\t    -- 检查模块加载状态，分别处理两个模块\n    716\t    local wcvarLoaded = checkModuleLoaded(\&quot;WCvar\&quot;)\n    717\t    local wanmenuLoaded = checkModuleLoaded(\&quot;WanMenu\&quot;)\n    718\t\n...\n    846\t        -- WanMenu模块未加载时显示提示\n    847\t        createModuleNotLoadedText(parent, \&quot;WanMenu\&quot;, \&quot;TOP\&quot;, parent, 0, currentY)\n    848\t    end\n    849\tend\n    850\t\n&gt;   851\t-- 【标签页3】快捷标记面板（简化版）\n    852\tfunction WanTinyUI.CreateWanMenuPanel(parent)\n    853\t    if not checkModuleLoaded(\&quot;WanMenu\&quot;) then\n    854\t        createModuleNotLoadedText(parent, \&quot;WanMenu\&quot;)\n    855\t        return\n    856\t    end\n    857\t    \n    858\t    -- 简单的说明文字\n    859\t    local desc = parent:CreateFontString(nil, \&quot;ARTWORK\&quot;, \&quot;GameFontNormal\&quot;)\n    860\t    desc:SetPoint(\&quot;TOPLEFT\&quot;, parent, \&quot;TOPLEFT\&quot;, 20, -20)\n    861\t    desc:SetJustifyH(\&quot;LEFT\&quot;)\n&gt;   862\t    desc:SetText(\&quot;|cffff80ff快捷标记功能设置|r\\n\\n快捷标记的详细设置已移至 |cff00ff00CVAR设置|r 标签页下方。\\n\\n包含团队标记按钮、倒计时功能、显示选项等设置。\\n\\n当前标签页保留用于未来扩展功能。\&quot;)\n    863\t    desc:SetWidth(500)\n    864\t    desc:SetNonSpaceWrap(true)\n    865\tend\n    866\t\n    867\t-- 【标签页4】创建WCombatTimes战斗计时器设置面板\n    868\tfunction WanTinyUI.CreateWCombatTimesPanel(parent)\n    869\t    if parent.wcombatPanelCreated then return end\n    870\t\n    871\t\n    872\t\n...\n   1091\t\n   1092\t\n   1093\t\n   1094\tend\n   1095\t\n&gt;  1096\t-- 创建底部标签页切换按钮\n   1097\tfunction WanTinyUI.CreateBottomTabs()\n   1098\t    local mainFrame = WanTinyUI.MainFrame\n   1099\t    WanTinyUI.tabButtons = {}\n   1100\t    WanTinyUI.activeTab = 1\n   1101\t\n   1102\t    -- 标签页按钮布局\n   1103\t    local frameWidth = WanTinyUI.FRAME_WIDTH\n   1104\t    local leftPadding = WanTinyUI.LAYOUT.TAB_PADDING\n   1105\t    local rightPadding = WanTinyUI.LAYOUT.TAB_PADDING\n   1106\t    local availableWidth = frameWidth - leftPadding - rightPadding\n   1107\t    local spacing = WanTinyUI.LAYOUT.TAB_SPACING\n   1108\t\n   1109\t    -- 标签页按钮文字定义\n   1110\t    local tabTexts = {\&quot;模块开关\&quot;, \&quot;CVAR/标记\&quot;, \&quot;占位\&quot;, \&quot;战斗计时\&quot;}\n   1111\t\n   1112\t    -- 计算每个按钮的宽度（平均分配可用宽度）\n   1113\t    local totalSpacing = spacing * (table.getn(tabTexts) - 1)\n   1114\t    local buttonWidth = math.floor((availableWidth - totalSpacing) / table.getn(tabTexts))\n   1115\t\n   1116\t    local currentX = leftPadding\n   1117\t    local startY = WanTinyUI.LAYOUT.TAB_BOTTOM_OFFSET\n   1118\t\n   1119\t    for i, tabText in ipairs(tabTexts) do\n   1120\t        local bt = WanTinyUI.CreateTabButton(mainFrame, tabText, buttonWidth, 28)\n   1121\t\n   1122\t        -- 直接定位到底部\n   1123\t        bt:SetPoint(\&quot;BOTTOMLEFT\&quot;, mainFrame, \&quot;BOTTOMLEFT\&quot;, currentX, startY)\n   1124\t        currentX = currentX + buttonWidth + spacing\n   1125\t\n&gt;  1126\t        bt:SetScript(\&quot;OnClick\&quot;, function() WanTinyUI.SelectTab(i) end)\n   1127\t        WanTinyUI.tabButtons[i] = bt\n   1128\t    end\n   1129\t\n   1130\t    -- 默认选中第一个标签页\n   1131\t    WanTinyUI.SelectTab(1)\n   1132\tend\n   1133\tlocal TAB_TITLES = {\n   1134\t    left = {\&quot;WanTiny|cffff80ff晚妹的小合集|r\&quot;, \&quot;CVar/快捷标记\&quot;, \&quot;|cffff80ff占位|r\&quot;, \&quot;|cffff6b6b战斗计时|r\&quot;},\n   1135\t    right = {\&quot;|cff00bfffV 2.0.0|r\&quot;, \&quot;|cff00bfffV 2.0.0|r\&quot;, \&quot;|cff00bfffV 2.0.0|r\&quot;, \&quot;|cff00bfffV 2.0.0|r\&quot;}\n   1136\t}\n   1137\t\n   1138\t-- 标签页切换核心函数：控制标签页按钮状态和内容显示\n   1139\tfunction WanTinyUI.SelectTab(tabIndex)\n   1140\t    WanTinyUI.activeTab = tabIndex\n   1141\t    \n   1142\t    -- 更新标签页按钮的选中状态\n   1143\t    if WanTinyUI.tabButtons and type(WanTinyUI.tabButtons) == \&quot;table\&quot; then\n   1144\t        for i, bt in ipairs(WanTinyUI.tabButtons) do\n   1145\t            if bt and bt.SetSelected then\n   1146\t                bt:SetSelected(i == tabIndex)\n   1147\t            end\n   1148\t        end\n   1149\t    end\n   1150\t    \n   1151\t    -- 控制标签页内容的显示/隐藏\n   1152\t    if WanTinyUI.tabContents then\n   1153\t        for i, content in ipairs(WanTinyUI.tabContents) do\n   1154\t            content:SetShown(i == tabIndex)\n   1155\t        end\n   1156\t    end\n   1157\t\n   1158\t    -- 更新窗口标题文字\n   1159\t    if WanTinyUI.TabTitleLeft and WanTinyUI.TabTitleRight then\n   1160\t        WanTinyUI.TabTitleLeft:SetText(TAB_TITLES.left[tabIndex] or \&quot;\&quot;)\n   1161\t        WanTinyUI.TabTitleRight:SetText(TAB_TITLES.right[tabIndex] or \&quot;\&quot;)\n   1162\t    end\n   1163\tend\n   1164\t\n   1165\tfunction WanTinyUI.ToggleMainFrame()\n   1166\t    if not WanTinyUI.MainFrame then\n\nTotal matches: 82\nTotal lines in file: 1186&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01XbfGq1QqT33jGNgDQZrEXR&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;,&quot;folderRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-30T11:19:34.379Z&quot;,&quot;response_text&quot;:&quot;现在我明白了问题所在。根据代码分析，我发现了以下几个问题：\n\n## 问题分析\n\n1. **垂直聊天条复选框勾选后需要RL才会变化**：\n   - WChat模块没有专门的配置界面，配置是通过代码中的 `WChat_Config` 变量控制的\n   - 当前没有UI界面来动态修改 `UseVertical` 设置\n\n2. **垂直聊天条的起始位置需要和水平的起始位置一样**：\n   - 在 `InitChatBar()` 函数中，垂直和水平布局使用了不同的定位逻辑\n\n3. **垂直和水平是两个不同的个体**：\n   - 当前代码在切换垂直/水平布局时没有重新创建聊天条，只是在初始化时根据配置决定布局\n\n## 解决方案\n\n我需要：\n1. 为WChat模块创建一个配置界面\n2. 修复垂直聊天条的定位问题\n3. 添加动态切换垂直/水平布局的功能\n\n让我开始实施修复：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;bb3de588-2cf7-488f-a388-62112562706a&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01C7YrjEEtGyK6tFAt8YpNfE&quot;,&quot;tool_name&quot;:&quot;str-replace-editor&quot;,&quot;input_json&quot;:&quot;{\&quot;command\&quot;: \&quot;str_replace\&quot;, \&quot;path\&quot;: \&quot;WanTinyUI.lua\&quot;, \&quot;instruction_reminder\&quot;: \&quot;ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH.\&quot;, \&quot;old_str_1\&quot;: \&quot;    -- \\u6807\\u7b7e\\u9875\\u6309\\u94ae\\u6587\\u5b57\\u5b9a\\u4e49\\n    local tabTexts = {\\\&quot;\\u6a21\\u5757\\u5f00\\u5173\\\&quot;, \\\&quot;CVAR/\\u6807\\u8bb0\\\&quot;, \\\&quot;\\u5360\\u4f4d\\\&quot;, \\\&quot;\\u6218\\u6597\\u8ba1\\u65f6\\\&quot;}\&quot;, \&quot;new_str_1\&quot;: \&quot;    -- \\u6807\\u7b7e\\u9875\\u6309\\u94ae\\u6587\\u5b57\\u5b9a\\u4e49\\n    local tabTexts = {\\\&quot;\\u6a21\\u5757\\u5f00\\u5173\\\&quot;, \\\&quot;CVAR/\\u6807\\u8bb0\\\&quot;, \\\&quot;\\u804a\\u5929\\u589e\\u5f3a\\\&quot;, \\\&quot;\\u6218\\u6597\\u8ba1\\u65f6\\\&quot;}\&quot;, \&quot;old_str_start_line_number_1\&quot;: 1109, \&quot;old_str_end_line_number_1\&quot;: 1110}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Successfully edited the file WanTinyUI.lua.\nResult for str_replace for entry with index [1]:\nReplacement successful.\nnew_str starts at line 559 and ends at line 572.\n\nReview the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).\nEdit the file again if necessary.\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01X7kLgAaFYXjF3bW2r5M7W1&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;,&quot;folderRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-30T11:19:55.448Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;cancelled&quot;,&quot;request_id&quot;:&quot;5710eac5-2124-43e6-bfe9-ff68e6a47003&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[],&quot;seen_state&quot;:&quot;unseen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Successfully edited the file WanTinyUI.lua.\nResult for str_replace for entry with index [1]:\nReplacement successful.\nnew_str starts at line 1109 and ends at line 1110.\n\nReview the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).\nEdit the file again if necessary.\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01C7YrjEEtGyK6tFAt8YpNfE&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;,&quot;folderRoot&quot;:&quot;D:/游戏/World of Warcraft/_classic_/Interface/AddOns/WanTiny&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-30T11:20:03.383Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;request_id&quot;:&quot;386e1aae-ce17-4917-9e37-54c47c3d3619&quot;,&quot;status&quot;:&quot;cancelled&quot;,&quot;chatItemType&quot;:&quot;agentic-turn-delimiter&quot;},{&quot;request_id&quot;:&quot;baea50f4-49a9-4398-b27d-832cf0646aed&quot;,&quot;uuid&quot;:&quot;880bc486-fc0c-439f-9480-274724e1e05a&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:1753874170990,&quot;toTimestamp&quot;:1753874412329},{&quot;request_id&quot;:&quot;92fe3625-dd89-445a-9af3-68cbd3df48ec&quot;,&quot;uuid&quot;:&quot;e6afe4a9-ac98-4ded-b8ba-98f8f9f6718d&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:1753874412329,&quot;toTimestamp&quot;:1753874435890}],&quot;feedbackStates&quot;:{&quot;temp-fe-f38c5078-f51b-4e97-92b8-064827d42657&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-c06f12c1-caf5-4edb-8375-dc391ae81c2f&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-369a6c6a-362b-4e37-94f4-4b5f4d22db48&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-f4d0cea7-67f3-45b2-b417-b98a4db903f1&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-051d1576-325d-43c0-a745-c768c2f8dc53&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-57e7f2f4-b701-471d-b049-1d90230e8bfd&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-2b81ecfa-2213-44c9-bac4-fc43fb17b5fe&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-8bea955e-88bd-41f2-95f7-cd600545bb57&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-de827af8-8c75-49da-a78d-69bd63e03681&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-0cfeb891-7080-4b4d-a755-f6039205c23f&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-4acaad98-33b6-4a86-8c9e-dbbf36fba9a7&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-9397ec1b-6dd6-4313-92c3-20324e5fb5d2&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-5ecacaad-9548-413d-b903-2aac73dfed35&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-8c9c5450-97e9-4844-bb22-0def8ba7c56d&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-86ee06fa-74e0-4a44-992e-97a41eec88bb&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-ef091f97-1b21-4abf-a1ce-5a61e8ae144b&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-b5e70be7-05dd-46ec-a504-ef3f432f2aa8&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-dcb5aa6b-14aa-4d1d-9856-66b76088b5e6&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-d1c4c58a-c754-4c78-8012-33c06d265591&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-9038b0b1-e844-4427-b7a0-991822ef18bc&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-4c8b48dd-c319-4393-b80f-3dfabcff872e&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-25ca4f04-34ae-49ea-8f8f-4b94435289c2&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-00f20379-03d9-411d-8498-49a321568f5c&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-0844783a-6d75-40e2-8000-af2df230dbda&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{&quot;016547ec-004c-4320-bdce-602e25c15ebe;toolu_vrtx_01GkCr7DXebLFgXvcqtZuYAN&quot;:{&quot;requestId&quot;:&quot;016547ec-004c-4320-bdce-602e25c15ebe&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01GkCr7DXebLFgXvcqtZuYAN&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on Moudel/WChat.lua:\n     1\t-------------------------------WChat 聊天增强模块-------------------------------\n     2\t-- 独立聊天增强模块，移除ACE3依赖\n     3\t-- 作者: VGGFAC\n     4\t-- 版本: 2.0.0\n     5\t\n     6\tlocal addonName, addon = ...\n     7\t\n     8\t-- 模块配置\n     9\tlocal WChat = {}\n    10\tlocal WChat_Config = {\n    11\t    -- 默认配置\n    12\t    ShortChannel = true,        -- 频道标签精简\n    13\t    EnableEmoteInput = true,    -- 启用表情输入\n    14\t    EmoteIconSize = 16,         -- 表情图标大小\n    15\t    EmoteIconListSize = 20,     -- 表情列表图标大小\n    16\t    EmoteOffsetX = 0,           -- 表情面板X偏移\n    17\t    EmoteOffsetY = 30,          -- 表情面板Y偏移\n    18\t    UseVertical = false,        -- 聊天条垂直布局\n    19\t    UseTopChatbar = false,      -- 聊天条在上方\n    20\t    UseTopInput = false,        -- 输入框在上方\n    21\t    ChatBarOffsetX = 0,         -- 聊天条X偏移\n    22\t    ChatBarOffsetY = 0,         -- 聊天条Y偏移\n    23\t    DistanceVertical = -25,     -- 垂直间距\n    24\t    DistanceHorizontal = 25,    -- 水平间距\n    25\t    AlphaOnEnter = 1.0,         -- 鼠标悬停透明度\n    26\t    AlphaOnLeave = 0.7,         -- 鼠标离开透明度\n    27\t    Position = nil,             -- 聊天条位置\n    28\t    EnableTimestampCopy = true, -- 启用时间戳点击复制\n    29\t    TimestampFormat = \&quot;%H:%M:%S\&quot;, -- 时间戳格式\n    30\t    TimestampColor = { 1.0, 1.0, 1.0 }, -- 时间戳颜色\n    31\t    -- 插件按钮显示控制\n    32\t    ShowBiaoGeButton = true,    -- 显示BiaoGe按钮\n    33\t    ShowBiaoGeAIButton = true,  -- 显示BiaoGeAI按钮\n    34\t    ShowAtlasButton = true,     -- 显示Atlas按钮\n    35\t    ShowMeetingHornButton = true, -- 显示MeetingHorn按钮\n    36\t    -- 频道隐藏设置\n    37\t    HiddenChannels = {}\n    38\t}\n    39\t\n    40\t-- 频道配置映射\n    41\tlocal CHANNEL_CONFIG = {\n    42\t    MAPPINGS = {\n    43\t        chnGen = \&quot;综合\&quot;,\n    44\t        chnTrade = \&quot;交易\&quot;,\n    45\t        chnLFG = \&quot;寻求组队\&quot;,\n    46\t        world = \&quot;大脚世界频道\&quot;\n    47\t    }\n    48\t}\n    49\t\n    50\t-- 频道显示/隐藏管理函数\n    51\tlocal function IsChannelShown(channelName)\n    52\t    local channels = {GetChatWindowChannels(DEFAULT_CHAT_FRAME:GetID() or 1)}\n    53\t    for i = 1, #channels, 2 do\n    54\t        if channels[i] == channelName then return true end\n    55\t    end\n    56\t    return false\n    57\tend\n    58\t\n    59\tlocal function ToggleChannelShowHide(channelName)\n    60\t    local isShown = IsChannelShown(channelName)\n    61\t    if isShown then\n    62\t        ChatFrame_RemoveChannel(DEFAULT_CHAT_FRAME, channelName)\n    63\t        WChat_Config.HiddenChannels[channelName] = true\n    64\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 已隐藏频道: \&quot; .. channelName)\n    65\t    else\n    66\t        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)\n    67\t        WChat_Config.HiddenChannels[channelName] = nil\n    68\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 已显示频道: \&quot; .. channelName)\n    69\t    end\n    70\tend\n    71\t\n    72\tlocal function UpdateChannelXIcons()\n    73\t    if not WChatBar then return end\n    74\t\n    75\t    for _, child in pairs({WChatBar:GetChildren()}) do\n    76\t        if child.X and child.buttonName and CHANNEL_CONFIG.MAPPINGS[child.buttonName] then\n    77\t            local channelName = CHANNEL_CONFIG.MAPPINGS[child.buttonName]\n    78\t            child.X:SetShown(not IsChannelShown(channelName))\n    79\t        end\n    80\t    end\n    81\tend\n    82\t\n    83\t-- 获取语言\n    84\tlocal Language = GetLocale()\n    85\tif (Language == \&quot;zhTW\&quot;) then\n    86\t    --公会\n    87\t    CHAT_GUILD_GET = \&quot;|Hchannel:GUILD|h[公會]|h %s: \&quot;\n    88\t    CHAT_OFFICER_GET = \&quot;|Hchannel:OFFICER|h[官員]|h %s: \&quot;\n    89\t    \n    90\t    --团队\n    91\t    CHAT_RAID_GET = \&quot;|Hchannel:RAID|h[團隊]|h %s: \&quot;\n    92\t    CHAT_RAID_WARNING_GET = \&quot;[通知] %s: \&quot;\n    93\t    CHAT_RAID_LEADER_GET = \&quot;|Hchannel:RAID|h[團長]|h %s: \&quot;\n    94\t    \n    95\t    --队伍\n    96\t    CHAT_PARTY_GET = \&quot;|Hchannel:PARTY|h[隊伍]|h %s: \&quot;\n    97\t    CHAT_PARTY_LEADER_GET = \&quot;|Hchannel:PARTY|h[隊長]|h %s: \&quot;\n    98\t    CHAT_PARTY_GUIDE_GET = \&quot;|Hchannel:PARTY|h[向導]|h %s: \&quot;\n    99\t    \n   100\t    --战场\n   101\t    CHAT_BATTLEGROUND_GET = \&quot;|Hchannel:BATTLEGROUND|h[戰場]|h %s: \&quot;\n   102\t    CHAT_BATTLEGROUND_LEADER_GET = \&quot;|Hchannel:BATTLEGROUND|h[領袖]|h %s: \&quot;\n   103\t    \n   104\t    --说 / 喊\n   105\t    CHAT_SAY_GET = \&quot;%s: \&quot;\n   106\t    CHAT_YELL_GET = \&quot;%s: \&quot;\n   107\t    \n   108\t    --密语\n   109\t    CHAT_WHISPER_INFORM_GET = \&quot;發送給%s: \&quot;\n   110\t    CHAT_WHISPER_GET = \&quot;%s悄悄話: \&quot;\n   111\t    \n   112\t    --flags\n   113\t    CHAT_FLAG_AFK = \&quot;[暫離] \&quot;\n   114\t    CHAT_FLAG_DND = \&quot;[勿擾] \&quot;\n   115\t    CHAT_FLAG_GM = \&quot;[GM] \&quot;\n   116\telseif (Language == \&quot;zhCN\&quot;) then\n   117\t    --公会\n   118\t    CHAT_GUILD_GET = \&quot;|Hchannel:GUILD|h[公会]|h %s: \&quot;\n   119\t    CHAT_OFFICER_GET = \&quot;|Hchannel:OFFICER|h[官员]|h %s: \&quot;\n   120\t    \n   121\t    --团队\n   122\t    CHAT_RAID_GET = \&quot;|Hchannel:RAID|h[团队]|h %s: \&quot;\n   123\t    CHAT_RAID_WARNING_GET = \&quot;[通知] %s: \&quot;\n   124\t    CHAT_RAID_LEADER_GET = \&quot;|Hchannel:RAID|h[团长]|h %s: \&quot;\n   125\t    \n   126\t    --队伍\n   127\t    CHAT_PARTY_GET = \&quot;|Hchannel:PARTY|h[队伍]|h %s: \&quot;\n   128\t    CHAT_PARTY_LEADER_GET = \&quot;|Hchannel:PARTY|h[队长]|h %s: \&quot;\n   129\t    CHAT_PARTY_GUIDE_GET = \&quot;|Hchannel:PARTY|h[向导]:|h %s: \&quot;\n   130\t    \n   131\t    --战场\n   132\t    CHAT_BATTLEGROUND_GET = \&quot;|Hchannel:BATTLEGROUND|h[副本]|h %s: \&quot;\n   133\t    CHAT_BATTLEGROUND_LEADER_GET = \&quot;|Hchannel:BATTLEGROUND|h[领袖]|h %s: \&quot;\n   134\t    \n   135\t    --密语\n   136\t    CHAT_WHISPER_INFORM_GET = \&quot;发送给%s: \&quot;\n   137\t    CHAT_WHISPER_GET = \&quot;%s悄悄的说: \&quot;\n   138\t    CHAT_BN_WHISPER_INFORM_GET = \&quot;发送给%s: \&quot;\n   139\t    CHAT_BN_WHISPER_GET = \&quot;悄悄的说%s: \&quot;\n   140\t    \n   141\t    --说 / 喊\n   142\t    CHAT_SAY_GET = \&quot;%s: \&quot;\n   143\t    CHAT_YELL_GET = \&quot;%s: \&quot;\n   144\t    \n   145\t    --flags\n   146\t    CHAT_FLAG_AFK = \&quot;[暂离] \&quot;\n   147\t    CHAT_FLAG_DND = \&quot;[勿扰] \&quot;\n   148\t    CHAT_FLAG_GM = \&quot;[GM] \&quot;\n   149\telse\n   150\t    CHAT_GUILD_GET = \&quot;|Hchannel:GUILD|hG|h %s \&quot;\n   151\t    CHAT_OFFICER_GET = \&quot;|Hchannel:OFFICER|hO|h %s \&quot;\n   152\t    CHAT_RAID_GET = \&quot;|Hchannel:RAID|hR|h %s \&quot;\n   153\t    CHAT_RAID_WARNING_GET = \&quot;RW %s \&quot;\n   154\t    CHAT_RAID_LEADER_GET = \&quot;|Hchannel:RAID|hRL|h %s \&quot;\n   155\t    CHAT_PARTY_GET = \&quot;|Hchannel:PARTY|hP|h %s \&quot;\n   156\t    CHAT_PARTY_LEADER_GET = \&quot;|Hchannel:PARTY|hPL|h %s \&quot;\n   157\t    CHAT_PARTY_GUIDE_GET = \&quot;|Hchannel:PARTY|hPG|h %s \&quot;\n   158\t    CHAT_BATTLEGROUND_GET = \&quot;|Hchannel:BATTLEGROUND|hB|h %s \&quot;\n   159\t    CHAT_BATTLEGROUND_LEADER_GET = \&quot;|Hchannel:BATTLEGROUND|hBL|h %s \&quot;\n   160\t    CHAT_WHISPER_INFORM_GET = \&quot;to %s \&quot;\n   161\t    CHAT_WHISPER_GET = \&quot;from %s \&quot;\n   162\t    CHAT_BN_WHISPER_INFORM_GET = \&quot;to %s \&quot;\n   163\t    CHAT_BN_WHISPER_GET = \&quot;from %s \&quot;\n   164\t    CHAT_SAY_GET = \&quot;%s \&quot;\n   165\t    CHAT_YELL_GET = \&quot;%s \&quot;\n   166\t    CHAT_FLAG_AFK = \&quot;[AFK] \&quot;\n   167\t    CHAT_FLAG_DND = \&quot;[DND] \&quot;\n   168\t    CHAT_FLAG_GM = \&quot;[GM] \&quot;\n   169\tend\n   170\t\n   171\t--================================公共频道和自定义频道精简================================--\n   172\tlocal gsub = _G.string.gsub\n   173\tlocal newAddMsg = {}\n   174\tlocal chn = {\n   175\t    \&quot;%[%d+%. General.-%]\&quot;,\n   176\t    \&quot;%[%d+%. Trade.-%]\&quot;,\n   177\t    \&quot;%[%d+%. LocalDefense.-%]\&quot;,\n   178\t    \&quot;%[%d+%. LookingForGroup%]\&quot;,\n   179\t    \&quot;%[%d+%. WorldDefense%]\&quot;,\n   180\t    \&quot;%[%d+%. GuildRecruitment.-%]\&quot;,\n   181\t    \&quot;%[%d+%. BigFootChannel.-%]\&quot;,\n   182\t    \&quot;%[%d+%. CustomChannel.-%]\&quot; -- 自定义频道英文名随便填写\n   183\t}\n   184\t\n   185\tlocal rplc = {\n   186\t    \&quot;[GEN]\&quot;,\n   187\t    \&quot;[TR]\&quot;,\n   188\t    \&quot;[WD]\&quot;,\n   189\t    \&quot;[LD]\&quot;,\n   190\t    \&quot;[LFG]\&quot;,\n   191\t    \&quot;[GR]\&quot;,\n   192\t    \&quot;[BFC]\&quot;,\n   193\t    \&quot;[CL]\&quot; -- 英文缩写\n   194\t}\n   195\t\n   196\tif (Language == \&quot;zhCN\&quot;) then ---国服\n   197\t    rplc[1] = \&quot;[%1综]\&quot;\n   198\t    rplc[2] = \&quot;[%1交]\&quot;\n   199\t    rplc[3] = \&quot;[%1防]\&quot;\n   200\t    rplc[4] = \&quot;[%1组]\&quot;\n   201\t    rplc[5] = \&quot;[%1守]\&quot;\n   202\t    rplc[6] = \&quot;[%1招]\&quot;\n   203\t    rplc[7] = \&quot;[%1世]\&quot;\n   204\t    rplc[8] = \&quot;[%1自定义]\&quot; -- 自定义频道缩写请自行修改\n   205\telseif (Language == \&quot;zhTW\&quot;) then ---台服\n   206\t    rplc[1] = \&quot;[%1綜合]\&quot;\n   207\t    rplc[2] = \&quot;[%1貿易]\&quot;\n   208\t    rplc[3] = \&quot;[%1防務]\&quot;\n   209\t    rplc[4] = \&quot;[%1組隊]\&quot;\n   210\t    rplc[5] = \&quot;[%1守備]\&quot;\n   211\t    rplc[6] = \&quot;[%1招募]\&quot;\n   212\t    rplc[7] = \&quot;[%1世界]\&quot;\n   213\t    rplc[8] = \&quot;[%1自定义]\&quot; -- 自定义频道缩写请自行修改\n   214\tend\n   215\t\n   216\tif Language == \&quot;zhCN\&quot; then\n   217\t    ---------------------------------------- 国服简体中文 ---------------------------------------------\n   218\t    chn[1] = \&quot;%[%d+%. 综合.-%]\&quot;\n   219\t    chn[2] = \&quot;%[%d+%. 交易.-%]\&quot;\n   220\t    chn[3] = \&quot;%[%d+%. 本地防务.-%]\&quot;\n   221\t    chn[4] = \&quot;%[%d+%. 寻求组队%]\&quot;\n   222\t    chn[5] = \&quot;%[%d+%. 世界防务%]\&quot;\n   223\t    chn[6] = \&quot;%[%d+%. 公会招募.-%]\&quot;\n   224\t    chn[7] = \&quot;%[%d+%. 大脚世界频道.-%]\&quot;\n   225\t    chn[8] = \&quot;%[%d+%. 自定义频道.-%]\&quot; -- 请修改频道名对应你游戏里的频道\n   226\telseif Language == \&quot;zhTW\&quot; then\n   227\t    ---------------------------------------- 台服繁体中文 ---------------------------------------------\n   228\t    chn[1] = \&quot;%[%d+%. 綜合.-%]\&quot;\n   229\t    chn[2] = \&quot;%[%d+%. 貿易.-%]\&quot;\n   230\t    chn[3] = \&quot;%[%d+%. 本地防務.-%]\&quot;\n   231\t    chn[4] = \&quot;%[%d+%. 尋求組隊%]\&quot;\n   232\t    chn[5] = \&quot;%[%d+%. 世界防務%]\&quot;\n   233\t    chn[6] = \&quot;%[%d+%. 公會招募.-%]\&quot;\n   234\t    chn[7] = \&quot;%[%d+%. 大脚世界频道.-%]\&quot;\n   235\t    chn[8] = \&quot;%[%d+%. 自定义频道.-%]\&quot; -- 请修改频道名对应你游戏里的频道\n   236\tend\n   237\t\n   238\tlocal rules = {\n   239\t        --!!不要改\n   240\t        {pat = \&quot;|c%x+|HChatCopy|h.-|h|r\&quot;, repl = \&quot;\&quot;},\n   241\t        {pat = \&quot;|c%x%x%x%x%x%x%x%x(.-)|r\&quot;, repl = \&quot;%1\&quot;},\n   242\t        --左鍵\n   243\t        {pat = \&quot;|Hchannel:.-|h.-|h\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   244\t        {pat = \&quot;|Hplayer:.-|h.-|h\&quot; .. \&quot;:\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   245\t        {pat = \&quot;|Hplayer:.-|h.-|h\&quot; .. \&quot;：\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   246\t        {pat = \&quot;|HBNplayer:.-|h.-|h\&quot; .. \&quot;:\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   247\t        {pat = \&quot;|HBNplayer:.-|h.-|h\&quot; .. \&quot;：\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   248\t        --右鍵\n   249\t        {pat = \&quot;|Hchannel:.-|h(.-)|h\&quot;, repl = \&quot;%1\&quot;, button = \&quot;RightButton\&quot;},\n   250\t        {pat = \&quot;|Hplayer:.-|h(.-)|h\&quot;, repl = \&quot;%1\&quot;, button = \&quot;RightButton\&quot;},\n   251\t        {pat = \&quot;|HBNplayer:.-|h(.-)|h\&quot;, repl = \&quot;%1\&quot;, button = \&quot;RightButton\&quot;},\n   252\t        --!!不要改\n   253\t        {pat = \&quot;|H.-|h(.-)|h\&quot;, repl = \&quot;%1\&quot;},\n   254\t        {pat = \&quot;|TInterface\\\\TargetingFrame\\\\UI%-RaidTargetingIcon_(%d):0|t\&quot;, repl = \&quot;{rt%1}\&quot;},\n   255\t        {pat = \&quot;|T.-|t\&quot;, repl = \&quot;\&quot;},\n   256\t        {pat = \&quot;^%s+\&quot;, repl = \&quot;\&quot;}\n   257\t}\n   258\t\n   259\t-- 文字修改函数\n   260\tfunction WChat:AddMessage(text, ...)\n   261\t    -- 频道标签精简\n   262\t    if (type(text) ~= \&quot;string\&quot;) then\n   263\t        text = tostring(text)\n   264\t    end\n   265\t    for i = 1, 8 do -- 对应上面几个频道(如果有9个频道就for i = 1, 9 do)\n   266\t        text = text:gsub(chn[i], rplc[i])\n   267\t    end\n   268\t    text = text:gsub(\&quot;%[(%d0?)%. .-%]\&quot;, \&quot;%1.\&quot;)\n   269\t    return self.DefaultAddMessage(self, text, ...)\n   270\tend\n   271\t\n   272\t-- 初始化频道信息精简模块\n   273\tfunction WChat:InitChannel()\n   274\t    if WChat_Config.ShortChannel then\n   275\t        for i = 1, (NUM_CHAT_WINDOWS or 10) do\n   276\t            if i ~= 2 then\n   277\t                local cf = _G['ChatFrame' .. i]\n   278\t                cf.DefaultAddMessage = cf.AddMessage\n   279\t                cf.AddMessage = self.AddMessage\n   280\t            end\n   281\t        end\n   282\t    end\n   283\tend\n   284\t-------------------------------聊天复制------------------------------------\n   285\t\n   286\tlocal lines = {}\n   287\t\n   288\tlocal chatCopyFrame = CreateFrame(\&quot;Frame\&quot;, \&quot;ChatCopyFrame\&quot;, UIParent, BackdropTemplateMixin and \&quot;BackdropTemplate\&quot; or nil)\n   289\tchatCopyFrame:SetPoint(\&quot;CENTER\&quot;, UIParent, \&quot;CENTER\&quot;)\n   290\tchatCopyFrame:SetSize(700, 400)\n   291\tchatCopyFrame:Hide()\n   292\tchatCopyFrame:SetFrameStrata(\&quot;DIALOG\&quot;)\n   293\tchatCopyFrame.close = CreateFrame(\&quot;Button\&quot;, nil, chatCopyFrame, \&quot;UIPanelCloseButton\&quot;)\n   294\tchatCopyFrame.close:SetPoint(\&quot;TOPRIGHT\&quot;, chatCopyFrame, \&quot;TOPRIGHT\&quot;)\n   295\tchatCopyFrame:SetBackdrop({\n   296\t    bgFile = \&quot;Interface/DialogFrame/UI-DialogBox-Background\&quot;,\n   297\t    edgeFile = \&quot;Interface/DialogFrame/UI-DialogBox-Border\&quot;,\n   298\t    tile = true,\n   299\t    tileSize = 16,\n   300\t    edgeSize = 16,\n   301\t    insets = {left = 4, right = 4, top = 4, bottom = 4}\n   302\t})\n   303\t\n   304\tlocal scrollArea = CreateFrame(\&quot;ScrollFrame\&quot;, \&quot;ChatCopyScrollFrame\&quot;, chatCopyFrame, \&quot;UIPanelScrollFrameTemplate\&quot;)\n   305\tscrollArea:SetPoint(\&quot;TOPLEFT\&quot;, chatCopyFrame, \&quot;TOPLEFT\&quot;, 10, -30)\n   306\tscrollArea:SetPoint(\&quot;BOTTOMRIGHT\&quot;, chatCopyFrame, \&quot;BOTTOMRIGHT\&quot;, -30, 10)\n   307\t\n   308\tlocal editBox = CreateFrame(\&quot;EditBox\&quot;, nil, chatCopyFrame)\n   309\teditBox:SetMultiLine(true)\n   310\teditBox:SetMaxLetters(99999)\n   311\teditBox:EnableMouse(true)\n   312\teditBox:SetAutoFocus(false)\n   313\teditBox:SetFontObject(ChatFontNormal)\n   314\teditBox:SetWidth(scrollArea:GetWidth())\n   315\teditBox:SetHeight(270)\n   316\teditBox:SetScript(\&quot;OnEscapePressed\&quot;, function(f)f:GetParent():GetParent():Hide()f:SetText(\&quot;\&quot;) end)\n   317\tscrollArea:SetScrollChild(editBox)\n   318\t\n   319\tfunction WChat:CopyFunc()\n   320\t    local cf = SELECTED_CHAT_FRAME\n   321\t    local _, size = cf:GetFont()\n   322\t    FCF_SetChatWindowFontSize(cf, cf, .01)\n   323\t    local ct = 1\n   324\t    for i = select(\&quot;#\&quot;, cf.FontStringContainer:GetRegions()), 1, -1 do\n   325\t        local region = select(i, cf.FontStringContainer:GetRegions())\n   326\t        if region:GetObjectType() == \&quot;FontString\&quot; then\n   327\t            if region:GetText() ~= nil then\n   328\t                lines[ct] = tostring(region:GetText())\n   329\t                ct = ct + 1\n   330\t            end\n   331\t        end\n   332\t    end\n   333\t    local lineCt = ct - 1\n   334\t    local text = table.concat(lines, \&quot;\\n\&quot;, 1, lineCt)\n   335\t    FCF_SetChatWindowFontSize(cf, cf, size)\n   336\t    chatCopyFrame:Show()\n   337\t    editBox:SetText(text)\n   338\t    editBox:HighlightText(0)\n   339\t    wipe(lines)\n   340\tend\n   341\t----------------------------聊天表情相关代码-----------------------------\n   342\t\n   343\t-- 表情选择器框架\n   344\tlocal EmoteTableFrame\n   345\t\n   346\t-- 表情解析规则\n   347\tlocal fmtstring\n   348\t\n   349\t-- 自定义表情开始的序号\n   350\tlocal customEmoteStartIndex = 9\n   351\t\n   352\tlocal emotes = {\n   353\t        --原版暴雪提供的8个图标\n   354\t        {\&quot;{rt1}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_1]=]},\n   355\t        {\&quot;{rt2}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_2]=]},\n   356\t        {\&quot;{rt3}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_3]=]},\n   357\t        {\&quot;{rt4}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_4]=]},\n   358\t        {\&quot;{rt5}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_5]=]},\n   359\t        {\&quot;{rt6}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_6]=]},\n   360\t        {\&quot;{rt7}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_7]=]},\n   361\t        {\&quot;{rt8}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_8]=]},\n   362\t        --自定义表情\n   363\t        {\&quot;{天使}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Angel]=]},\n   364\t        {\&quot;{生气}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Angry]=]},\n   365\t        {\&quot;{大笑}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Biglaugh]=]},\n   366\t        {\&quot;{鼓掌}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Clap]=]},\n   367\t        {\&quot;{酷}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Cool]=]},\n   368\t        {\&quot;{哭}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Cry]=]},\n   369\t        {\&quot;{可爱}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Cutie]=]},\n   370\t        {\&quot;{鄙视}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Despise]=]},\n   371\t        {\&quot;{美梦}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Dreamsmile]=]},\n   372\t        {\&quot;{尴尬}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Embarrass]=]},\n   373\t        {\&quot;{邪恶}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Evil]=]},\n   374\t        {\&quot;{兴奋}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Excited]=]},\n   375\t        {\&quot;{晕}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Faint]=]},\n   376\t        {\&quot;{打架}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Fight]=]},\n   377\t        {\&quot;{流感}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Flu]=]},\n   378\t        {\&quot;{呆}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Freeze]=]},\n   379\t        {\&quot;{皱眉}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Frown]=]},\n   380\t        {\&quot;{致敬}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Greet]=]},\n   381\t        {\&quot;{鬼脸}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Grimace]=]},\n   382\t        {\&quot;{龇牙}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Growl]=]},\n   383\t        {\&quot;{开心}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Happy]=]},\n   384\t        {\&quot;{心}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Heart]=]},\n   385\t        {\&quot;{恐惧}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Horror]=]},\n   386\t        {\&quot;{生病}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Ill]=]},\n   387\t        {\&quot;{无辜}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Innocent]=]},\n   388\t        {\&quot;{功夫}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Kongfu]=]},\n   389\t        {\&quot;{花痴}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Love]=]},\n   390\t        {\&quot;{邮件}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Mail]=]},\n   391\t        {\&quot;{化妆}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Makeup]=]},\n   392\t        -- {\&quot;{马里奥}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Mario]=]},\n   393\t        {\&quot;{沉思}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Meditate]=]},\n   394\t        {\&quot;{可怜}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Miserable]=]},\n   395\t        {\&quot;{好}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Okay]=]},\n   396\t        {\&quot;{漂亮}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Pretty]=]},\n   397\t        {\&quot;{吐}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Puke]=]},\n   398\t        {\&quot;{握手}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Shake]=]},\n   399\t        {\&quot;{喊}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Shout]=]},\n   400\t        {\&quot;{闭嘴}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Shuuuu]=]},\n   401\t        {\&quot;{害羞}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Shy]=]},\n   402\t        {\&quot;{睡觉}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Sleep]=]},\n   403\t        {\&quot;{微笑}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Smile]=]},\n   404\t        {\&quot;{吃惊}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Suprise]=]},\n   405\t        {\&quot;{失败}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Surrender]=]},\n   406\t        {\&quot;{流汗}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Sweat]=]},\n   407\t        {\&quot;{流泪}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Tear]=]},\n   408\t        {\&quot;{悲剧}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Tears]=]},\n   409\t        {\&quot;{想}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Think]=]},\n   410\t        {\&quot;{偷笑}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Titter]=]},\n   411\t        {\&quot;{猥琐}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Ugly]=]},\n   412\t        {\&quot;{胜利}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Victory]=]},\n   413\t        {\&quot;{雷锋}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Volunteer]=]},\n   414\t        {\&quot;{委屈}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Wronged]=]}\n   415\t}\n   416\t\n   417\tlocal function ChatEmoteFilter(self, event, msg, ...)\n   418\t    if (WChat_Config and WChat_Config.EnableEmoteInput) then\n   419\t        for i = customEmoteStartIndex, #emotes do\n   420\t            if msg:find(emotes[i][1]) then\n   421\t                msg = msg:gsub(emotes[i][1], format(fmtstring, emotes[i][2]), 1)\n   422\t            end\n   423\t        end\n   424\t    end\n   425\t    return false, msg, ...\n   426\tend\n   427\t\n   428\tlocal function EmoteIconMouseUp(frame, button)\n   429\t    if (button == \&quot;LeftButton\&quot;) then\n   430\t        local chatFrame = GetCVar(\&quot;chatStyle\&quot;) == \&quot;im\&quot; and SELECTED_CHAT_FRAME or DEFAULT_CHAT_FRAME\n   431\t        local eb = chatFrame and chatFrame.editBox\n   432\t        if (eb) then\n   433\t            eb:Insert(frame.text)\n   434\t            eb:Show();\n   435\t            eb:SetFocus()\n   436\t        end\n   437\t    end\n   438\t    WChat:ToggleEmoteTable()\n   439\tend\n   440\t\n   441\tfunction WChat:InitEmoteTableFrame()\n   442\t    fmtstring = format(\&quot;\\124T%%s:%d\\124t\&quot;, max(floor(select(2, SELECTED_CHAT_FRAME:GetFont())), WChat_Config.EmoteIconSize))\n   443\t    \n   444\t    EmoteTableFrame = CreateFrame(\&quot;Frame\&quot;, \&quot;EmoteTableFrame\&quot;, UIParent, BackdropTemplateMixin and \&quot;BackdropTemplate\&quot; or nil)\n   445\t    EmoteTableFrame:SetMovable(true)\n   446\t    EmoteTableFrame:RegisterForDrag(\&quot;LeftButton\&quot;)\n   447\t    EmoteTableFrame:SetScript(\&quot;OnDragStart\&quot;, EmoteTableFrame.StartMoving)\n   448\t    EmoteTableFrame:SetScript(\&quot;OnDragStop\&quot;, EmoteTableFrame.StopMovingOrSizing)\n   449\t    EmoteTableFrame:EnableMouse(true)\n   450\t    EmoteTableFrame:SetWidth((WChat_Config.EmoteIconListSize + 6) * 12 + 10)\n   451\t    EmoteTableFrame:SetHeight((WChat_Config.EmoteIconListSize + 6) * 5 + 10)\n   452\t    EmoteTableFrame:SetPoint(\&quot;BOTTOM\&quot;, ChatFrame1EditBox, WChat_Config.EmoteOffsetX, WChat_Config.EmoteOffsetY)\n   453\t    EmoteTableFrame:SetBackdrop({\n   454\t        bgFile = \&quot;Interface\\\\Buttons\\\\WHITE8x8\&quot;,\n   455\t        edgeFile = \&quot;Interface\\\\Tooltips\\\\UI-Tooltip-Border\&quot;,\n   456\t        tile = true,\n   457\t        tileSize = 16,\n   458\t        edgeSize = 16,\n   459\t        insets = {left = 3, right = 3, top = 3, bottom = 3}\n   460\t    })\n   461\t    EmoteTableFrame:SetBackdropColor(0.05, 0.05, 0.05, 0.8)\n   462\t    EmoteTableFrame:SetBackdropBorderColor(0.3, 0.3, 0.3)\n   463\t    -- 表情选择框出现位置 默认30,30\n   464\t    EmoteTableFrame:Hide()\n   465\t    EmoteTableFrame:SetFrameStrata(\&quot;DIALOG\&quot;)\n   466\t    local icon, row, col\n   467\t    row = 1\n   468\t    col = 1\n   469\t    for i = 1, #emotes do\n   470\t        text = emotes[i][1]\n   471\t        texture = emotes[i][2]\n   472\t        icon = CreateFrame(\&quot;Frame\&quot;, format(\&quot;IconButton%d\&quot;, i), EmoteTableFrame)\n   473\t        icon:SetWidth(WChat_Config.EmoteIconListSize + 6)\n   474\t        icon:SetHeight(WChat_Config.EmoteIconListSize + 6)\n   475\t        icon.text = text\n   476\t        icon.texture = icon:CreateTexture(nil, \&quot;ARTWORK\&quot;)\n   477\t        icon.texture:SetTexture(texture)\n   478\t        icon.texture:SetAllPoints(icon)\n   479\t        icon:Show()\n   480\t        icon:SetPoint(\n   481\t            \&quot;TOPLEFT\&quot;,\n   482\t            5 + (col - 1) * (WChat_Config.EmoteIconListSize + 6),\n   483\t            -5 - (row - 1) * (WChat_Config.EmoteIconListSize + 6)\n   484\t        )\n   485\t        icon:SetScript(\&quot;OnMouseUp\&quot;, EmoteIconMouseUp)\n   486\t        icon:EnableMouse(true)\n   487\t        col = col + 1\n   488\t        if (col &gt; 12) then\n   489\t            row = row + 1\n   490\t            col = 1\n   491\t        end\n   492\t    end\n   493\t    \n   494\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_CHANNEL\&quot;, ChatEmoteFilter)-- 公共频道\n   495\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_SAY\&quot;, ChatEmoteFilter)-- 说\n   496\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_YELL\&quot;, ChatEmoteFilter)-- 大喊\n   497\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_RAID\&quot;, ChatEmoteFilter)-- 团队\n   498\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_RAID_LEADER\&quot;, ChatEmoteFilter)-- 团队领袖\n   499\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_PARTY\&quot;, ChatEmoteFilter)-- 队伍\n   500\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_PARTY_LEADER\&quot;, ChatEmoteFilter)-- 队伍领袖\n   501\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_GUILD\&quot;, ChatEmoteFilter)-- 公会\n   502\t    \n   503\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_AFK\&quot;, ChatEmoteFilter)-- AFK玩家自动回复\n   504\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_DND\&quot;, ChatEmoteFilter)-- 切勿打扰自动回复\n   505\t    \n   506\t    -- 副本和副本领袖\n   507\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_INSTANCE_CHAT\&quot;, ChatEmoteFilter)\n   508\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_INSTANCE_CHAT_LEADER\&quot;, ChatEmoteFilter)\n   509\t    -- 解析战网私聊\n   510\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_WHISPER\&quot;, ChatEmoteFilter)\n   511\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_WHISPER_INFORM\&quot;, ChatEmoteFilter)\n   512\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_BN_WHISPER\&quot;, ChatEmoteFilter)\n   513\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_BN_WHISPER_INFORM\&quot;, ChatEmoteFilter)\n   514\t    -- 解析社区聊天内容\n   515\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_COMMUNITIES_CHANNEL\&quot;, ChatEmoteFilter)\n   516\tend\n   517\t\n   518\tfunction WChat:ToggleEmoteTable()\n   519\t    if (EmoteTableFrame:IsShown()) then\n   520\t        EmoteTableFrame:Hide()\n   521\t    else\n   522\t        EmoteTableFrame:Show()\n   523\t    end\n   524\tend\n   525\t-----------------------------------频道和密语对象的快速切换\n   526\t\n   527\tfunction ChatEdit_CustomTabPressed(...)\n   528\t    return ChatEdit_CustomTabPressed_Inner(...)\n   529\tend\n   530\t\n   531\tlocal cycles = {\n   532\t        -- \&quot;说\&quot;\n   533\t        {\n   534\t            chatType = \&quot;SAY\&quot;,\n   535\t            use = function(self, editbox)\n   536\t                return 1\n   537\t            end\n   538\t        },\n   539\t        --大喊\n   540\t        {\n   541\t            chatType = \&quot;YELL\&quot;,\n   542\t            use = function(self, editbox)\n   543\t                return 1\n   544\t            end\n   545\t        },\n   546\t        --小队\n   547\t        {\n   548\t            chatType = \&quot;PARTY\&quot;,\n   549\t            use = function(self, editbox)\n   550\t                return IsInGroup()\n   551\t            end\n   552\t        },\n   553\t        --团队\n   554\t        {\n   555\t            chatType = \&quot;RAID\&quot;,\n   556\t            use = function(self, editbox)\n   557\t                return IsInRaid()\n   558\t            end\n   559\t        },\n   560\t        --实时聊天\n   561\t        {\n   562\t            chatType = \&quot;INSTANCE_CHAT\&quot;,\n   563\t            use = function(self, editbox)\n   564\t                return select(2, IsInInstance()) == \&quot;pvp\&quot;\n   565\t            end\n   566\t        },\n   567\t        --公会\n   568\t        {\n   569\t            chatType = \&quot;GUILD\&quot;,\n   570\t            use = function(self, editbox)\n   571\t                return IsInGuild()\n   572\t            end\n   573\t        },\n   574\t        --频道\n   575\t        {\n   576\t            chatType = \&quot;CHANNEL\&quot;,\n   577\t            use = function(self, editbox, currChatType)\n   578\t                local currNum\n   579\t                if currChatType ~= \&quot;CHANNEL\&quot; then\n   580\t                    currNum = IsShiftKeyDown() and 21 or 0\n   581\t                else\n   582\t                    currNum = editbox:GetAttribute(\&quot;channelTarget\&quot;)\n   583\t                end\n   584\t                local h, r, step = currNum + 1, 20, 1\n   585\t                if IsShiftKeyDown() then\n   586\t                    h, r, step = currNum - 1, 1, -1\n   587\t                end\n   588\t                for i = h, r, step do\n   589\t                    local channelNum, channelName = GetChannelName(i)\n   590\t                    if channelNum &gt; 0 and channelName:find(\&quot;大脚世界频道\&quot;) then\n   591\t                        --print(channelName); --DEBUG\n   592\t                        editbox:SetAttribute(\&quot;channelTarget\&quot;, i)\n   593\t                        return true\n   594\t                    end\n   595\t                end\n   596\t            end\n   597\t        },\n   598\t        {\n   599\t            chatType = \&quot;SAY\&quot;,\n   600\t            use = function(self, editbox)\n   601\t                return 1\n   602\t            end\n   603\t        }\n   604\t}\n   605\t\n   606\tlocal chatTypeBeforeSwitch, tellTargetBeforeSwitch --记录在频道和密语之间切换时的状态\n   607\tfunction ChatEdit_CustomTabPressed_Inner(self)\n   608\t    if strsub(tostring(self:GetText()), 1, 1) == \&quot;/\&quot; then\n   609\t        return\n   610\t    end\n   611\t    local currChatType = self:GetAttribute(\&quot;chatType\&quot;)\n   612\t    if (IsControlKeyDown()) then\n   613\t        if (currChatType == \&quot;WHISPER\&quot; or currChatType == \&quot;BN_WHISPER\&quot;) then\n   614\t            --记录之前的密语对象，以便后续切回\n   615\t            self:SetAttribute(\&quot;chatType\&quot;, chatTypeBeforeSwitch or \&quot;SAY\&quot;)\n   616\t            ChatEdit_UpdateHeader(self)\n   617\t            chatTypeBeforeSwitch = \&quot;WHISPER\&quot;\n   618\t            tellTargetBeforeSwitch = self:GetAttribute(\&quot;tellTarget\&quot;)\n   619\t            return --这里和下面不同，这里可以不返回true\n   620\t        else\n   621\t            local newTarget, newTargetType = ChatEdit_GetNextTellTarget()\n   622\t            if tellTargetBeforeSwitch or (newTarget and newTarget ~= \&quot;\&quot;) then\n   623\t                self:SetAttribute(\&quot;chatType\&quot;, tellTargetBeforeSwitch and chatTypeBeforeSwitch or newTargetType)\n   624\t                self:SetAttribute(\&quot;tellTarget\&quot;, tellTargetBeforeSwitch or newTarget)\n   625\t                ChatEdit_UpdateHeader(self)\n   626\t                chatTypeBeforeSwitch = currChatType\n   627\t                tellTargetBeforeSwitch = nil\n   628\t                return true --这里必须返回true，否则会被暴雪默认的再切换一次密语对象\n   629\t            end\n   630\t        end\n   631\t    end\n   632\t    \n   633\t    --对于说然后SHIFT的情况，因为没有return，所以第一层循环会一直遍历到最后的SAY\n   634\t    for i, curr in ipairs(cycles) do\n   635\t        if curr.chatType == currChatType then\n   636\t            local h, r, step = i + 1, #cycles, 1\n   637\t            if IsShiftKeyDown() then\n   638\t                h, r, step = i - 1, 1, -1\n   639\t            end\n   640\t            if currChatType == \&quot;CHANNEL\&quot; then\n   641\t                h = i\n   642\t            end --频道仍然要测试一下\n   643\t            for j = h, r, step do\n   644\t                if cycles[j]:use(self, currChatType) then\n   645\t                    self:SetAttribute(\&quot;chatType\&quot;, cycles[j].chatType)\n   646\t                    ChatEdit_UpdateHeader(self)\n   647\t                    return\n   648\t                end\n   649\t            end\n   650\t        end\n   651\t    end\n   652\tend\n   653\t\n   654\t\n   655\t\n   656\t-----------------------------点击时间复制 (alaCHAT完整实现)\n   657\t-- 基于alaCHAT的完整时间戳点击复制功能\n   658\t\n   659\tlocal hooksecurefunc = hooksecurefunc;\n   660\tlocal date = date;\n   661\tlocal format, gsub, strmatch = string.format, string.gsub, string.match;\n   662\tlocal GetCVar, SetCVar = GetCVar, SetCVar;\n   663\tlocal ChatEdit_ChooseBoxForSend, ChatEdit_ActivateChat = ChatEdit_ChooseBoxForSend, ChatEdit_ActivateChat;\n   664\tlocal ItemRefTooltip = ItemRefTooltip;\n   665\tlocal _G = _G;\n   666\t\n   667\tlocal __copy = {};\n   668\tlocal _db = {};\n   669\t\n   670\tlocal __TAG = \&quot;\&quot;;\n   671\tlocal __FMT = \&quot;\&quot;;\n   672\tlocal __CLR = { 1.0, 1.0, 1.0, };\n   673\t\n   674\t-- 设置时间戳 (alaCHAT原版)\n   675\tlocal function SetTimeStamp()\n   676\t    __TAG = format(\&quot;|cff%.2x%.2x%.2x|Haccopy:-1|h%s|h|r\&quot;, __CLR[1] * 255, __CLR[2] * 255, __CLR[3] * 255, (__FMT == nil or __FMT == \&quot;\&quot; or __FMT == \&quot;none\&quot;) and \&quot;*\&quot; or __FMT);\n   677\t    if GetCVar(\&quot;showTimestamps\&quot;) ~= __TAG then\n   678\t        SetCVar(\&quot;showTimestamps\&quot;, __TAG);\n   679\t        _G.CHAT_TIMESTAMP_FORMAT = __TAG;\n   680\t    end\n   681\tend\n   682\t\n   683\t-- 初始化时间戳点击复制功能 (alaCHAT原版)\n   684\tlocal B_Initialized = false;\n   685\tlocal function Init()\n   686\t    B_Initialized = true;\n   687\t    local _ItemRefTooltip_SetHyperlink = ItemRefTooltip.SetHyperlink;\n   688\t    function ItemRefTooltip:SetHyperlink(link, ...)\n   689\t        if link == \&quot;accopy:-1\&quot; then\n   690\t            local focus = GetMouseFocus and GetMouseFocus() or GetMouseFoci and GetMouseFoci()[1];\n   691\t            if not focus:IsObjectType(\&quot;FontString\&quot;) then\n   692\t                focus = focus:GetParent();\n   693\t                if not focus:IsObjectType(\&quot;FontString\&quot;) then\n   694\t                    return;\n   695\t                end\n   696\t            end\n   697\t            local tx = focus:GetText();\n   698\t            if tx == nil or tx == \&quot;\&quot; then\n   699\t                return;\n   700\t            end\n   701\t            -- 清理文本格式 (alaCHAT原版处理)\n   702\t            tx = gsub(tx, \&quot;|H.-|h\&quot;, \&quot;\&quot;);\n   703\t            tx = gsub(tx, \&quot;|c%x%x%x%x%x%x%x%x\&quot;, \&quot;\&quot;);\n   704\t            tx = gsub(tx, \&quot;|[Hhr]\&quot;, \&quot;\&quot;);\n   705\t            local editBox = ChatEdit_ChooseBoxForSend();\n   706\t            if not editBox:HasFocus() then\n   707\t                ChatEdit_ActivateChat(editBox);\n   708\t            end\n   709\t            editBox:SetText(tx);\n   710\t            return;\n   711\t        end\n   712\t        return _ItemRefTooltip_SetHyperlink(self, link, ...);\n   713\t    end\n   714\t    -- 监听时间戳设置变化\n   715\t    if InterfaceOptionsSocialPanelTimestamps ~= nil and InterfaceOptionsSocialPanelTimestamps.SetValue ~= nil then\n   716\t        hooksecurefunc(InterfaceOptionsSocialPanelTimestamps, \&quot;SetValue\&quot;, function(self, value)\n   717\t            _db.toggle = false;\n   718\t        end);\n   719\t    end\n   720\tend\n   721\t\n   722\t-- alaCHAT模块功能实现\n   723\tfunction __copy.color(value, loading)\n   724\t    if not loading and _db.toggle then\n   725\t        if value ~= nil and( __CLR[1] ~= value[1] or __CLR[2] ~= value[2] or __CLR[3] ~= value[3]) then\n   726\t            __CLR = { value[1], value[2], value[3], };\n   727\t            SetTimeStamp();\n   728\t        end\n   729\t    end\n   730\tend\n   731\t\n   732\tfunction __copy.format(value, loading)\n   733\t    if not loading and _db.toggle then\n   734\t        if value ~= nil and __FMT ~= value then\n   735\t            __FMT = value;\n   736\t            SetTimeStamp();\n   737\t        end\n   738\t    end\n   739\tend\n   740\t\n   741\tfunction __copy.toggle(value, loading)\n   742\t    if value then\n   743\t        if not B_Initialized then\n   744\t            Init();\n   745\t        end\n   746\t        local c = _db.color;\n   747\t        local f = _db.format;\n   748\t        __FMT = f;\n   749\t        __CLR = { c[1], c[2], c[3], };\n   750\t        SetTimeStamp();\n   751\t    elseif loading then\n   752\t        local fmt = GetCVar(\&quot;showTimestamps\&quot;);\n   753\t        if fmt ~= \&quot;none\&quot; then\n   754\t            local fmt2 = strmatch(fmt, \&quot;|h(.+)|h\&quot;);\n   755\t            if fmt2 ~= nil then\n   756\t                SetCVar(\&quot;showTimestamps\&quot;, fmt2);\n   757\t                _G.CHAT_TIMESTAMP_FORMAT = fmt2;\n   758\t            end\n   759\t        end\n   760\t    else\n   761\t        if __FMT == \&quot;none\&quot; or __FMT == \&quot;\&quot; or __FMT == nil then\n   762\t            if GetCVar(\&quot;showTimestamps\&quot;) ~= \&quot;none\&quot; then\n   763\t                SetCVar(\&quot;showTimestamps\&quot;, \&quot;none\&quot;);\n   764\t                _G.CHAT_TIMESTAMP_FORMAT = nil;\n   765\t            end\n   766\t        else\n   767\t            if GetCVar(\&quot;showTimestamps\&quot;) ~= __FMT then\n   768\t                SetCVar(\&quot;showTimestamps\&quot;, __FMT);\n   769\t                _G.CHAT_TIMESTAMP_FORMAT = __FMT;\n   770\t            end\n   771\t        end\n   772\t    end\n   773\tend\n   774\t\n   775\t-- WChat集成的时间戳复制功能\n   776\tfunction WChat:InitTimestampCopy()\n   777\t    if WChat_Config.EnableTimestampCopy then\n   778\t        -- 初始化_db配置\n   779\t        _db = {\n   780\t            toggle = true,\n   781\t            format = WChat_Config.TimestampFormat or \&quot;%H:%M:%S\&quot;,\n   782\t            color = WChat_Config.TimestampColor or { 1.0, 1.0, 1.0 }\n   783\t        };\n   784\t\n   785\t        -- 启用功能\n   786\t        __copy.toggle(true, false);\n   787\t    end\n   788\tend\n   789\t\n   790\t-----------------------------聊天条\n   791\t\n   792\t--[[=========================== 变量区 ==========================]]\n   793\t-- 是否可移动的标记\n   794\tlocal IsMovable = false -- 没事干别动这个，你改成ture那么进入游戏后聊天条就是可以移动的\n   795\t--[[=============================== END ==============================]]\n   796\tlocal chatFrame = SELECTED_DOCK_FRAME -- 聊天框架\n   797\tlocal inputbox = chatFrame.editBox -- 输入框\n   798\t\n   799\tCOLORSCHEME_BORDER = {0.3, 0.3, 0.3, 1}\n   800\t-- 边框颜色\n   801\t-- 主框架初始化\n   802\tlocal ChatBar = CreateFrame(\&quot;Frame\&quot;, nil, UIParent, BackdropTemplateMixin and \&quot;BackdropTemplate\&quot; or nil)\n   803\tWChatBar = ChatBar\n   804\t\n   805\tlocal function ChannelSay_OnClick()\n   806\t    ChatFrame_OpenChat(\&quot;/s \&quot; .. inputbox:GetText(), chatFrame)\n   807\tend\n   808\t\n   809\tlocal function ChannelYell_OnClick()\n   810\t    ChatFrame_OpenChat(\&quot;/y \&quot; .. inputbox:GetText(), chatFrame)\n   811\tend\n   812\t\n   813\tlocal function ChannelParty_OnClick()\n   814\t    ChatFrame_OpenChat(\&quot;/p \&quot; .. inputbox:GetText(), chatFrame)\n   815\tend\n   816\t\n   817\tlocal function ChannelGuild_OnClick()\n   818\t    ChatFrame_OpenChat(\&quot;/g \&quot; .. inputbox:GetText(), chatFrame)\n   819\tend\n   820\t\n   821\tlocal function ChannelRaid_OnClick()\n   822\t    ChatFrame_OpenChat(\&quot;/raid \&quot; .. inputbox:GetText(), chatFrame)\n   823\tend\n   824\t\n   825\t-- 综合频道点击处理\n   826\tlocal function ChannelGen_OnClick(self, button)\n   827\t    if button == \&quot;RightButton\&quot; then\n   828\t        -- 右键：切换频道显示/隐藏\n   829\t        ToggleChannelShowHide(\&quot;综合\&quot;)\n   830\t        C_Timer.After(0.1, UpdateChannelXIcons)\n   831\t    else\n   832\t        -- 左键：如果频道未显示则先显示，然后发言\n   833\t        if not IsChannelShown(\&quot;综合\&quot;) then\n   834\t            ToggleChannelShowHide(\&quot;综合\&quot;)\n   835\t            C_Timer.After(0.1, UpdateChannelXIcons)\n   836\t        end\n   837\t        local channel, _, _ = GetChannelName(\&quot;综合\&quot;)\n   838\t        if channel then\n   839\t            ChatFrame_OpenChat(\&quot;/\&quot; .. channel .. \&quot; \&quot; .. inputbox:GetText(), chatFrame)\n   840\t        end\n   841\t    end\n   842\tend\n   843\t\n   844\t-- 交易频道点击处理\n   845\tlocal function ChannelTrade_OnClick(self, button)\n   846\t    if button == \&quot;RightButton\&quot; then\n   847\t        -- 右键：切换频道显示/隐藏\n   848\t        ToggleChannelShowHide(\&quot;交易\&quot;)\n   849\t        C_Timer.After(0.1, UpdateChannelXIcons)\n   850\t    else\n   851\t        -- 左键：如果频道未显示则先显示，然后发言\n   852\t        if not IsChannelShown(\&quot;交易\&quot;) then\n   853\t            ToggleChannelShowHide(\&quot;交易\&quot;)\n   854\t            C_Timer.After(0.1, UpdateChannelXIcons)\n   855\t        end\n   856\t        local channel, _, _ = GetChannelName(\&quot;交易\&quot;)\n   857\t        if channel then\n   858\t            ChatFrame_OpenChat(\&quot;/\&quot; .. channel .. \&quot; \&quot; .. inputbox:GetText(), chatFrame)\n   859\t        end\n   860\t    end\n   861\tend\n   862\t\n   863\t-- 寻求组队频道点击处理\n   864\tlocal function ChannelLFG_OnClick(self, button)\n   865\t    if button == \&quot;RightButton\&quot; then\n   866\t        -- 右键：切换频道显示/隐藏\n   867\t        ToggleChannelShowHide(\&quot;寻求组队\&quot;)\n   868\t        C_Timer.After(0.1, UpdateChannelXIcons)\n   869\t    else\n   870\t        -- 左键：如果频道未显示则先显示，然后发言\n   871\t        if not IsChannelShown(\&quot;寻求组队\&quot;) then\n   872\t            ToggleChannelShowHide(\&quot;寻求组队\&quot;)\n   873\t            C_Timer.After(0.1, UpdateChannelXIcons)\n   874\t        end\n   875\t        local channel, _, _ = GetChannelName(\&quot;寻求组队\&quot;)\n   876\t        if channel then\n   877\t            ChatFrame_OpenChat(\&quot;/\&quot; .. channel .. \&quot; \&quot; .. inputbox:GetText(), chatFrame)\n   878\t        end\n   879\t    end\n   880\tend\n   881\t\n   882\tlocal function ChannelBG_OnClick(self, button)\n   883\t    -- 副本频道按钮，只有左键功能\n   884\t    ChatFrame_OpenChat(\&quot;/bg \&quot; .. inputbox:GetText(), chatFrame)\n   885\tend\n   886\t\n   887\t-- function Channel01_OnClick()\n   888\t--     ChatFrame_OpenChat(\&quot;/1 \&quot;, chatFrame)\n   889\t-- end\n   890\tlocal function ChatEmote_OnClick()\n   891\t    WChat:ToggleEmoteTable()\n   892\tend\n   893\t\n   894\tlocal function ChannelWorld_OnClick(self, button)\n   895\t    if button == \&quot;RightButton\&quot; then\n   896\t        -- 右键：切换频道显示/隐藏\n   897\t        ToggleChannelShowHide(\&quot;大脚世界频道\&quot;)\n   898\t        C_Timer.After(0.1, UpdateChannelXIcons)\n   899\t    else\n   900\t        -- 左键：发言或加入频道后发言\n   901\t        local _, channelName, _ = GetChannelName(\&quot;大脚世界频道\&quot;)\n   902\t        if channelName == nil then\n   903\t            JoinPermanentChannel(\&quot;大脚世界频道\&quot;, nil, 1, 1)\n   904\t            ChatFrame_RemoveMessageGroup(chatFrame, \&quot;CHANNEL\&quot;)\n   905\t            ChatFrame_AddChannel(chatFrame, \&quot;大脚世界频道\&quot;)\n   906\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cff00d200已加入大脚世界频道|r\&quot;)\n   907\t            -- 确保频道显示\n   908\t            C_Timer.After(0.2, function()\n   909\t                if not IsChannelShown(\&quot;大脚世界频道\&quot;) then\n   910\t                    ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, \&quot;大脚世界频道\&quot;)\n   911\t                    WChat_Config.HiddenChannels[\&quot;大脚世界频道\&quot;] = nil\n   912\t                end\n   913\t                UpdateChannelXIcons()\n   914\t            end)\n   915\t        else\n   916\t            -- 如果频道未显示则先显示\n   917\t            if not IsChannelShown(\&quot;大脚世界频道\&quot;) then\n   918\t                ToggleChannelShowHide(\&quot;大脚世界频道\&quot;)\n   919\t                C_Timer.After(0.1, UpdateChannelXIcons)\n   920\t            end\n   921\t        end\n   922\t\n   923\t        local channel, _, _ = GetChannelName(\&quot;大脚世界频道\&quot;)\n   924\t        if channel then\n   925\t            ChatFrame_OpenChat(\&quot;/\&quot; .. channel .. \&quot; \&quot; .. inputbox:GetText(), chatFrame)\n   926\t        end\n   927\t    end\n   928\tend\n   929\t\n   930\tlocal function Roll_OnClick()\n   931\t    RandomRoll(1, 100)\n   932\tend\n   933\t\n   934\tlocal function Report_OnClick(self, button)\n   935\t    local statText = WChat:StatReport()\n   936\t    if button == \&quot;RightButton\&quot; then\n   937\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cff00d200我的属性：|r\&quot; .. statText)\n   938\t    else\n   939\t        local editBox = ChatEdit_ChooseBoxForSend()\n   940\t        if not editBox:HasFocus() then\n   941\t            ChatEdit_ActivateChat(editBox)\n   942\t        end\n   943\t        editBox:Insert(statText)\n   944\t    end\n   945\tend\n   946\t\n   947\tlocal function ChatCopy_OnClick()\n   948\t    WChat:CopyFunc()\n   949\tend\n   950\t\n   951\t-- 插件配置表\n   952\tlocal AddonConfigs = {\n   953\t    BiaoGe = {\n   954\t        addonName = \&quot;BiaoGe\&quot;,\n   955\t        displayName = \&quot;BiaoGe金团\&quot;,\n   956\t        globalVar = \&quot;BG\&quot;,\n   957\t        mainFrameKey = \&quot;MainFrame\&quot;,\n   958\t        slashCmd = nil,\n   959\t        tooltip = function()\n   960\t            return \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开BiaoGe金团|r\&quot;\n   961\t        end\n   962\t    },\n   963\t    BiaoGeAI = {\n   964\t        addonName = \&quot;BiaoGeAI\&quot;,\n   965\t        displayName = \&quot;BiaoGeAI\&quot;,\n   966\t        globalVar = \&quot;BGAI\&quot;,\n   967\t        mainFrameKey = \&quot;MainFrame\&quot;,\n   968\t        slashCmd = nil,\n   969\t        tooltip = function()\n   970\t            return \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开BiaoGeAI|r\&quot;\n   971\t        end\n   972\t    },\n   973\t    AtlasLootClassic = {\n   974\t        addonName = \&quot;AtlasLootClassic\&quot;,\n   975\t        displayName = \&quot;Atlas掉落\&quot;,\n   976\t        globalVar = nil,\n   977\t        mainFrameKey = nil,\n   978\t        slashCmd = \&quot;ATLASLOOT\&quot;,\n   979\t        tooltip = function()\n   980\t            return \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开Atlas掉落查询|r\&quot;\n   981\t        end\n   982\t    },\n   983\t    MeetingHorn = {\n   984\t        addonName = \&quot;MeetingHorn\&quot;,\n   985\t        displayName = \&quot;MeetingHorn集合石\&quot;,\n   986\t        globalVar = nil,\n   987\t        mainFrameKey = nil,\n   988\t        slashCmd = \&quot;MEETINGHORN\&quot;,\n   989\t        tooltip = function()\n   990\t            local tooltip = \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开集结号|r\&quot;\n   991\t\n   992\t            local addon = LibStub and LibStub('AceAddon-3.0'):GetAddon('MeetingHorn', true)\n   993\t            if not addon then return tooltip end\n   994\t\n   995\t            local lfg = addon:GetModule('LFG', true)\n   996\t            if not lfg then return tooltip end\n   997\t\n   998\t            local icon1 = \&quot;|TInterface\\\\AddOns\\\\MeetingHorn\\\\Media\\\\DataBroker:16:16:0:0:64:32:0:32:0:32|t\&quot;\n   999\t            local icon2 = \&quot;|TInterface\\\\AddOns\\\\MeetingHorn\\\\Media\\\\DataBroker:16:16:0:0:64:32:32:64:0:32|t\&quot;\n  1000\t            tooltip = tooltip .. \&quot;\\n\&quot; .. icon2 .. \&quot;|cffFFD700活动数量: \&quot; .. lfg:GetActivityCount() .. \&quot;|r\&quot;\n  1001\t\n  1002\t            local count = lfg:GetCurrentActivity() and lfg:GetApplicantCount() or lfg:GetApplicationCount()\n  1003\t            local label = lfg:GetCurrentActivity() and \&quot;申请者数量\&quot; or \&quot;申请数量\&quot;\n  1004\t            return tooltip .. \&quot;\\n\&quot; .. icon1 .. \&quot;|cffFFD700\&quot; .. label .. \&quot;: \&quot; .. count .. \&quot;|r\&quot;\n  1005\t        end\n  1006\t    }\n  1007\t}\n  1008\t\n  1009\t-- 通用插件按钮点击处理函数\n  1010\tlocal function CreateAddonClickHandler(configKey)\n  1011\t    return function()\n  1012\t        local config = AddonConfigs[configKey]\n  1013\t        if not config then return end\n  1014\t\n  1015\t        -- 检查并加载插件\n  1016\t        if not IsAddOnLoaded(config.addonName) then\n  1017\t            local loaded, reason = LoadAddOn(config.addonName)\n  1018\t            if not loaded then\n  1019\t                print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cffff0000\&quot; .. config.displayName .. \&quot;插件加载失败: \&quot; .. (reason or \&quot;未知原因\&quot;) .. \&quot;|r\&quot;)\n  1020\t                return\n  1021\t            end\n  1022\t            -- 刷新聊天条按钮\n  1023\t            WChat:RefreshChatBarButtons()\n  1024\t        end\n  1025\t\n  1026\t        -- 尝试打开插件界面\n  1027\t        if config.globalVar and config.mainFrameKey then\n  1028\t            -- 使用MainFrame方式\n  1029\t            local addon = _G[config.globalVar]\n  1030\t            if addon and addon[config.mainFrameKey] then\n  1031\t                addon[config.mainFrameKey]:SetShown(not addon[config.mainFrameKey]:IsVisible())\n  1032\t            end\n  1033\t        elseif config.slashCmd then\n  1034\t            -- 使用斜杠命令方式\n  1035\t            if configKey == \&quot;MeetingHorn\&quot; then\n  1036\t                -- MeetingHorn特殊处理\n  1037\t                local meetingHornAddon = LibStub and LibStub('AceAddon-3.0'):GetAddon('MeetingHorn', true)\n  1038\t                if meetingHornAddon and meetingHornAddon.Toggle then\n  1039\t                    meetingHornAddon:Toggle()\n  1040\t                elseif SlashCmdList[config.slashCmd] then\n  1041\t                    SlashCmdList[config.slashCmd](\&quot;\&quot;)\n  1042\t                end\n  1043\t            else\n  1044\t                -- 其他插件使用斜杠命令\n  1045\t                if SlashCmdList[config.slashCmd] then\n  1046\t                    SlashCmdList[config.slashCmd](\&quot;\&quot;)\n  1047\t                end\n  1048\t            end\n  1049\t        end\n  1050\t    end\n  1051\tend\n  1052\t\n  1053\t-- 创建具体的点击处理函数\n  1054\tlocal Gold_OnClick = CreateAddonClickHandler(\&quot;BiaoGe\&quot;)\n  1055\tlocal AI_OnClick = CreateAddonClickHandler(\&quot;BiaoGeAI\&quot;)\n  1056\tlocal Atlas_OnClick = CreateAddonClickHandler(\&quot;AtlasLootClassic\&quot;)\n  1057\tlocal MeetingHorn_OnClick = CreateAddonClickHandler(\&quot;MeetingHorn\&quot;)\n  1058\t\n  1059\tlocal function Movelock_OnClick(self, button)\n  1060\t    if button == \&quot;LeftButton\&quot; then\n  1061\t        if IsMovable then\n  1062\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cffd20000锁定聊天条|r\&quot;)\n  1063\t            IsMovable = false\n  1064\t            WChatBar:SetBackdrop(nil)\n  1065\t\n  1066\t            local point, relativeTo, relativePoint, xOfs, yOfs = WChatBar:GetPoint()\n  1067\t\n  1068\t            if relativeTo then\n  1069\t                WChat_Config.Position = {point = point, relativeTo = relativeTo:GetName(), relativePoint = relativePoint, xOfs = xOfs, yOfs = yOfs}\n  1070\t            else\n  1071\t                WChat_Config.Position = {point = point, relativeTo = nil, relativePoint = relativePoint, xOfs = xOfs, yOfs = yOfs}\n  1072\t            end\n  1073\t        else\n  1074\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cff00d200解锁聊天条|r\&quot;)\n  1075\t            IsMovable = true\n  1076\t            WChatBar:SetBackdrop(\n  1077\t                {\n  1078\t                    bgFile = \&quot;Interface/DialogFrame/UI-DialogBox-Background\&quot;,\n  1079\t                    edgeFile = \&quot;Interface/DialogFrame/UI-DialogBox-Border\&quot;,\n  1080\t                    tile = true,\n  1081\t                    tileSize = 16,\n  1082\t                    edgeSize = 16,\n  1083\t                    insets = {left = 4, right = 4, top = 4, bottom = 4}\n  1084\t                }\n  1085\t        )\n  1086\t        end\n  1087\t        WChatBar:EnableMouse(IsMovable)\n  1088\t    elseif button == \&quot;MiddleButton\&quot; then\n  1089\t        if IsMovable == false then\n  1090\t            return\n  1091\t        end\n  1092\t        WChatBar:ClearAllPoints()\n  1093\t        if WChat_Config.UseVertical then\n  1094\t            if WChat_Config.UseTopChatbar then\n  1095\t                WChatBar:SetPoint(\&quot;TOPRIGHT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX - 30, WChat_Config.ChatBarOffsetY + 25)\n  1096\t            else\n  1097\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPRIGHT\&quot;, WChat_Config.ChatBarOffsetX + 30, WChat_Config.ChatBarOffsetY + 25)\n  1098\t            end\n  1099\t        else\n  1100\t            if WChat_Config.UseTopChatbar then\n  1101\t                WChatBar:SetPoint(\&quot;BOTTOMLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY + 30)\n  1102\t            else\n  1103\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;BOTTOMLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY - 30)\n  1104\t            end\n  1105\t        end\n  1106\t    end\n  1107\tend\n  1108\t\n  1109\tlocal ChannelButtons = {\n  1110\t    {name = \&quot;say\&quot;, text = \&quot;说\&quot;, color = {1.00, 1.00, 1.00}, callback = ChannelSay_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到说话频道|r\&quot; end},\n  1111\t    {name = \&quot;yell\&quot;, text = \&quot;喊\&quot;, color = {1.00, 0.25, 0.25}, callback = ChannelYell_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到大喊频道|r\&quot; end},\n  1112\t    {name = \&quot;party\&quot;, text = \&quot;队\&quot;, color = {0.66, 0.66, 1.00}, callback = ChannelParty_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到队伍频道|r\&quot; end},\n  1113\t    {name = \&quot;guild\&quot;, text = \&quot;会\&quot;, color = {0.25, 1.00, 0.25}, callback = ChannelGuild_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到公会频道|r\&quot; end},\n  1114\t    {name = \&quot;raid\&quot;, text = \&quot;团\&quot;, color = {1.00, 0.50, 0.00}, callback = ChannelRaid_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到团队频道|r\&quot; end},\n  1115\t    {name = \&quot;LFT\&quot;, text = \&quot;副\&quot;, color = {1.00, 0.50, 0.00}, callback = ChannelBG_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到副本频道|r\&quot; end},\n  1116\t    {name = \&quot;chnGen\&quot;, text = \&quot;综\&quot;, color = {0.82, 0.70, 0.55}, callback = ChannelGen_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到综合频道|r\\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r\&quot; end},\n  1117\t    {name = \&quot;chnTrade\&quot;, text = \&quot;交\&quot;, color = {1.00, 0.82, 0.00}, callback = ChannelTrade_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到交易频道|r\\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r\&quot; end},\n  1118\t    {name = \&quot;chnLFG\&quot;, text = \&quot;组\&quot;, color = {0.50, 1.00, 0.50}, callback = ChannelLFG_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到寻求组队频道|r\\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r\&quot; end},\n  1119\t    {name = \&quot;world\&quot;, text = \&quot;世\&quot;, color = {0.78, 1.00, 0.59}, callback = ChannelWorld_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到世界频道|r\\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r\&quot; end},\n  1120\t    {name = \&quot;emote\&quot;, text = \&quot;表\&quot;, color = {1.00, 0.50, 1.00}, callback = ChatEmote_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开表情选择器|r\&quot; end},\n  1121\t    {name = \&quot;roll\&quot;, text = \&quot;骰\&quot;, color = {1.00, 1.00, 0.00}, callback = Roll_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff投掷骰子|r\&quot; end},\n  1122\t    {name = \&quot;report\&quot;, text = \&quot;报\&quot;, color = {0.80, 0.30, 0.30}, callback = Report_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff发送属性报告|r\\n|cff00ffff鼠标右键|r-|cffff80ff在聊天框显示属性|r\&quot; end},\n  1123\t    {name = \&quot;movelock\&quot;, text = \&quot;锁\&quot;, color = {0.20, 0.20, 0.80}, callback = Movelock_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff锁定/解锁聊天条位置|r\&quot; end},\n  1124\t    {name = \&quot;chatcopy\&quot;, text = \&quot;复\&quot;, color = {0.20, 0.60, 0.80}, callback = ChatCopy_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff复制聊天内容|r\&quot; end},\n  1125\t    -- 插件按钮\n  1126\t    {name = \&quot;biaoge\&quot;, text = \&quot;金\&quot;, color = {1.00, 0.84, 0.00}, callback = Gold_OnClick, tooltip = AddonConfigs.BiaoGe.tooltip},\n  1127\t    {name = \&quot;biaogeai\&quot;, text = \&quot;AI\&quot;, color = {0.00, 0.80, 1.00}, callback = AI_OnClick, tooltip = AddonConfigs.BiaoGeAI.tooltip},\n  1128\t    {name = \&quot;atlas\&quot;, text = \&quot;掉\&quot;, color = {0.80, 0.20, 0.80}, callback = Atlas_OnClick, tooltip = AddonConfigs.AtlasLootClassic.tooltip},\n  1129\t    {name = \&quot;meetinghorn\&quot;, text = \&quot;集\&quot;, color = {0.20, 0.80, 0.20}, callback = MeetingHorn_OnClick, tooltip = AddonConfigs.MeetingHorn.tooltip}\n  1130\t}\n  1131\t\n  1132\tlocal function CreateChannelButton(data, index)\n  1133\t    local frame = CreateFrame(\&quot;Button\&quot;, \&quot;frameName\&quot;, WChatBar)\n  1134\t    frame:SetWidth(22)\n  1135\t    -- 按钮宽度\n  1136\t    frame:SetHeight(22)\n  1137\t    -- 按钮高度\n  1138\t    frame:SetAlpha(WChat_Config.AlphaOnLeave)\n  1139\t    \n  1140\t    frame:SetFrameLevel(1)\n  1141\t    \n  1142\t    frame:SetScript(\n  1143\t        \&quot;OnEnter\&quot;,\n  1144\t        function(self)\n  1145\t            self:SetAlpha(WChat_Config.AlphaOnEnter)\n  1146\t            -- 显示鼠标提示\n  1147\t            if data.tooltip then\n  1148\t                GameTooltip:SetOwner(self, \&quot;ANCHOR_TOP\&quot;)\n  1149\t                GameTooltip:SetText(data.tooltip(), nil, nil, nil, nil, true)\n  1150\t                GameTooltip:Show()\n  1151\t            end\n  1152\t        end\n  1153\t    )\n  1154\t    frame:SetScript(\n  1155\t        \&quot;OnLeave\&quot;,\n  1156\t        function(self)\n  1157\t            self:SetAlpha(WChat_Config.AlphaOnLeave)\n  1158\t            -- 隐藏鼠标提示\n  1159\t            GameTooltip:Hide()\n  1160\t        end\n  1161\t    )\n  1162\t    if WChat_Config.UseVertical then\n  1163\t        frame:SetPoint(\&quot;TOP\&quot;, WChatBar, \&quot;TOP\&quot;, 0, (1 - index) * WChat_Config.DistanceVertical)\n  1164\t    else\n  1165\t        frame:SetPoint(\&quot;LEFT\&quot;, WChatBar, \&quot;LEFT\&quot;, 10 + (index - 1) * WChat_Config.DistanceHorizontal, 0)\n  1166\t    end\n  1167\t    \n  1168\t    frame:RegisterForClicks(\&quot;AnyUp\&quot;)\n  1169\t    frame:SetScript(\&quot;OnClick\&quot;, data.callback)\n  1170\t    -- 显示的文字\n  1171\t    frameText = frame:CreateFontString(data.name .. \&quot;Text\&quot;, \&quot;OVERLAY\&quot;)\n  1172\t    -- 字体设置\n  1173\t    frameText:SetFont(STANDARD_TEXT_FONT, 15, \&quot;OUTLINE\&quot;)\n  1174\t    \n  1175\t    frameText:SetJustifyH(\&quot;CENTER\&quot;)\n  1176\t    frameText:SetWidth(26)\n  1177\t    frameText:SetHeight(26)\n  1178\t    frameText:SetText(data.text)\n  1179\t    frameText:SetPoint(\&quot;CENTER\&quot;, 0, 0)\n  1180\t    \n  1181\t    -- 文字按钮的颜色\n  1182\t    frameText:SetTextColor(data.color[1], data.color[2], data.color[3])\n  1183\t\n  1184\t    -- 创建频道屏蔽X图标\n  1185\t    if CHANNEL_CONFIG.MAPPINGS[data.name] then\n  1186\t        -- 创建文字X图标（更可靠）\n  1187\t        frame.X = frame:CreateFontString(nil, \&quot;OVERLAY\&quot;, \&quot;GameFontNormal\&quot;)\n  1188\t        frame.X:SetText(\&quot;X\&quot;)\n  1189\t        frame.X:SetTextColor(1, 0, 0, 0.8) -- 红色X\n  1190\t        frame.X:SetFont(\&quot;Fonts\\\\FRIZQT__.TTF\&quot;, 16, \&quot;OUTLINE\&quot;)\n  1191\t        frame.X:SetPoint(\&quot;CENTER\&quot;, frame, \&quot;CENTER\&quot;, 0, 0)\n  1192\t        frame.X:Hide() -- 默认隐藏\n  1193\t        print(\&quot;创建文字X图标: \&quot; .. data.name)\n  1194\t    end\n  1195\t\n  1196\t    -- 设置按钮名称用于识别\n  1197\t    frame.buttonName = data.name\n  1198\tend\n  1199\t\n  1200\tfunction WChat:InitChatBar()\n  1201\t\n  1202\t    WChatBar:SetFrameLevel(0)\n  1203\t\n  1204\t    -- 使用竖直布局\n  1205\t    if WChat_Config.UseVertical then\n  1206\t        -- 主框体宽度\n  1207\t        WChatBar:SetWidth(30)\n  1208\t        -- 主框体高度\n  1209\t        WChatBar:SetHeight(#ChannelButtons * WChat_Config.DistanceVertical + 10)\n  1210\t    else\n  1211\t        -- 主框体宽度\n  1212\t        WChatBar:SetWidth(#ChannelButtons * WChat_Config.DistanceHorizontal + 10)\n  1213\t        -- 主框体高度\n  1214\t        WChatBar:SetHeight(30)\n  1215\t    end\n  1216\t\n  1217\t    -- 上方聊天输入框\n  1218\t    if WChat_Config.UseTopInput then\n  1219\t        inputbox:ClearAllPoints()\n  1220\t        inputbox:SetPoint(\&quot;BOTTOMLEFT\&quot;, chatFrame, \&quot;TOPLEFT\&quot;, 0, 20)\n  1221\t        inputbox:SetPoint(\&quot;BOTTOMRIGHT\&quot;, chatFrame, \&quot;TOPRIGHT\&quot;, 0, 20)\n  1222\t    end\n  1223\t    \n  1224\t    -- 位置设定\n  1225\t    if WChat_Config.Position == nil then\n  1226\t        if WChat_Config.UseVertical then\n  1227\t            if WChat_Config.UseTopChatbar then\n  1228\t                WChatBar:SetPoint(\&quot;TOPRIGHT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX - 30, WChat_Config.ChatBarOffsetY + 25)\n  1229\t            else\n  1230\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPRIGHT\&quot;, WChat_Config.ChatBarOffsetX + 30, WChat_Config.ChatBarOffsetY + 25)\n  1231\t            end\n  1232\t        else\n  1233\t            if WChat_Config.UseTopChatbar then\n  1234\t                WChatBar:SetPoint(\&quot;BOTTOMLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY + 30)\n  1235\t            else\n  1236\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;BOTTOMLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY - 30)\n  1237\t            end\n  1238\t        end\n  1239\t    else\n  1240\t        local point = WChat_Config.Position.point\n  1241\t        local relativeTo = WChat_Config.Position.relativeTo\n  1242\t        local relativePoint = WChat_Config.Position.relativePoint\n  1243\t        local xOfs = WChat_Config.Position.xOfs\n  1244\t        local yOfs = WChat_Config.Position.yOfs\n  1245\t        WChatBar:SetPoint(point, relativeTo, relativePoint, xOfs, yOfs)\n  1246\t    end\n  1247\t\n  1248\t    WChatBar:SetMovable(true)\n  1249\t    WChatBar:RegisterForDrag(\&quot;LeftButton\&quot;)\n  1250\t    WChatBar:SetScript(\&quot;OnDragStart\&quot;, WChatBar.StartMoving)\n  1251\t    WChatBar:SetScript(\&quot;OnDragStop\&quot;, WChatBar.StopMovingOrSizing)\n  1252\t    \n  1253\t    -- 动态创建按钮，根据配置和插件存在性\n  1254\t    local buttonIndex = 1\n  1255\t    for i = 1, #ChannelButtons do\n  1256\t        local buttonData = ChannelButtons[i]\n  1257\t        local shouldShow = true\n  1258\t\n  1259\t        -- 检查插件按钮是否应该显示（必须已加载才显示）\n  1260\t        if buttonData.name == \&quot;biaoge\&quot; then\n  1261\t            shouldShow = WChat_Config.ShowBiaoGeButton and IsAddOnLoaded(\&quot;BiaoGe\&quot;)\n  1262\t        elseif buttonData.name == \&quot;biaogeai\&quot; then\n  1263\t            shouldShow = WChat_Config.ShowBiaoGeAIButton and IsAddOnLoaded(\&quot;BiaoGeAI\&quot;)\n  1264\t        elseif buttonData.name == \&quot;atlas\&quot; then\n  1265\t            shouldShow = WChat_Config.ShowAtlasButton and IsAddOnLoaded(\&quot;AtlasLootClassic\&quot;)\n  1266\t        elseif buttonData.name == \&quot;meetinghorn\&quot; then\n  1267\t            shouldShow = WChat_Config.ShowMeetingHornButton and IsAddOnLoaded(\&quot;MeetingHorn\&quot;)\n  1268\t        end\n  1269\t\n  1270\t        if shouldShow then\n  1271\t            CreateChannelButton(buttonData, buttonIndex)\n  1272\t            buttonIndex = buttonIndex + 1\n  1273\t        end\n  1274\t    end\n  1275\t\n  1276\t    -- 延迟更新频道X图标状态\n  1277\t    C_Timer.After(0.5, UpdateChannelXIcons)\n  1278\t\n  1279\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 聊天条加载完毕\&quot;)\n  1280\tend\n  1281\t\n  1282\t-- 添加StatReport函数\n  1283\tfunction WChat:StatReport()\n  1284\t    -- 属性报告功能 (参考alaChat实现)\n  1285\t    local function GetItemLevel()\n  1286\t        local slots = { 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15 }\n  1287\t        local playerClass = UnitClassBase('player')\n  1288\t        if playerClass ~= \&quot;DRUID\&quot; and playerClass ~= \&quot;PALADIN\&quot; and playerClass ~= \&quot;SHAMAN\&quot; then\n  1289\t            slots[#slots + 1] = 18 -- 远程武器槽\n  1290\t        end\n  1291\t        slots[#slots + 1] = 16 -- 主手\n  1292\t        slots[#slots + 1] = 17 -- 副手\n  1293\t\n  1294\t        local total = 0\n  1295\t        local num = 0\n  1296\t        for index = 1, #slots do\n  1297\t            local slot = slots[index]\n  1298\t            local item = GetInventoryItemLink('player', slot)\n  1299\t            if item ~= nil and item ~= \&quot;\&quot; then\n  1300\t                local _, _, _, level, _, _, _, _, loc = GetItemInfo(item)\n  1301\t                if level ~= nil then\n  1302\t                    total = total + level\n  1303\t                    num = num + 1\n  1304\t                end\n  1305\t                if slot == 16 and loc == \&quot;INVTYPE_2HWEAPON\&quot; then\n  1306\t                    break -- 双手武器不计算副手\n  1307\t                end\n  1308\t            end\n  1309\t        end\n  1310\t        if num == 0 then return nil end\n  1311\t        local lvl = total / num + 0.05\n  1312\t        return math.floor(lvl * 10) / 10\n  1313\t    end\n  1314\t\n  1315\t    local function GetTalentDesc()\n  1316\t        local _, n1, _, _, p1 = GetTalentTabInfo(1)\n  1317\t        local _, n2, _, _, p2 = GetTalentTabInfo(2)\n  1318\t        local _, n3, _, _, p3 = GetTalentTabInfo(3)\n  1319\t        if p1 == p2 or p2 == p3 or p1 == p3 then\n  1320\t            return \&quot;天赋 (\&quot; .. p1 .. \&quot;/\&quot; .. p2 .. \&quot;/\&quot; .. p3 .. \&quot;)\&quot;\n  1321\t        elseif p1 &gt; p2 and p1 &gt; p3 then\n  1322\t            return \&quot;天赋 \&quot; .. n1 .. \&quot;(\&quot; .. p1 .. \&quot;/\&quot; .. p2 .. \&quot;/\&quot; .. p3 .. \&quot;)\&quot;\n  1323\t        elseif p2 &gt; p1 and p2 &gt; p3 then\n  1324\t            return \&quot;天赋 \&quot; .. n2 .. \&quot;(\&quot; .. p1 .. \&quot;/\&quot; .. p2 .. \&quot;/\&quot; .. p3 .. \&quot;)\&quot;\n  1325\t        else\n  1326\t            return \&quot;天赋 \&quot; .. n3 .. \&quot;(\&quot; .. p1 .. \&quot;/\&quot; .. p2 .. \&quot;/\&quot; .. p3 .. \&quot;)\&quot;\n  1327\t        end\n  1328\t    end\n  1329\t\n  1330\t    local function GetStatReport()\n  1331\t        local&lt;response clipped&gt;&lt;NOTE&gt;To save on context only part of this file has been shown to you.&lt;/NOTE&gt;\nTotal lines in file: 1605\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;b4f85aeb-2f38-4bed-aecc-bc7a21668cda;toolu_vrtx_01KEKjxL9K2Wp7Xf6QsqGDDF&quot;:{&quot;requestId&quot;:&quot;b4f85aeb-2f38-4bed-aecc-bc7a21668cda&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01KEKjxL9K2Wp7Xf6QsqGDDF&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on Moudel/WChat.lua:\n  1330\t    local function GetStatReport()\n  1331\t        local ilv = GetItemLevel()\n  1332\t        local class, file = UnitClass('player')\n  1333\t        local health = UnitHealthMax('player')\n  1334\t        local mana = UnitPowerMax('player', 0)\n  1335\t\n  1336\t        -- 基础信息\n  1337\t        local report = class .. \&quot;, \&quot; .. \&quot;等级\&quot; .. UnitLevel('player') .. \&quot;, \&quot; .. GetTalentDesc()\n  1338\t        if ilv then\n  1339\t            report = report .. \&quot;, 装等 \&quot; .. ilv\n  1340\t        end\n  1341\t        report = report .. \&quot;, 生命 \&quot; .. health\n  1342\t        if mana &gt; 0 then\n  1343\t            report = report .. \&quot;, 法力 \&quot; .. mana\n  1344\t        end\n  1345\t\n  1346\t        -- 根据职业添加特定属性\n  1347\t        if file == \&quot;WARRIOR\&quot; then\n  1348\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1349\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1350\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1351\t            if p3 &gt;= p1 and p3 &gt;= p2 then\n  1352\t                -- 防护战士\n  1353\t                local _, armor = UnitArmor('player')\n  1354\t                local pChance = GetParryChance()\n  1355\t                local dChance = GetDodgeChance()\n  1356\t                local bChance = GetBlockChance()\n  1357\t                local block = GetShieldBlock()\n  1358\t                report = report .. \&quot;, 护甲 \&quot; .. armor\n  1359\t                report = report .. \&quot;, 招架 \&quot; .. string.format(\&quot;%.1f\&quot;, pChance) .. \&quot;%\&quot;\n  1360\t                report = report .. \&quot;, 躲闪 \&quot; .. string.format(\&quot;%.1f\&quot;, dChance) .. \&quot;%\&quot;\n  1361\t                report = report .. \&quot;, 格挡 \&quot; .. string.format(\&quot;%.1f\&quot;, bChance) .. \&quot;% (\&quot; .. block .. \&quot;)\&quot;\n  1362\t            else\n  1363\t                -- 输出战士\n  1364\t                local apBase, apPos, apNeg = UnitAttackPower('player')\n  1365\t                local critChance = GetCritChance()\n  1366\t                report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1367\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1368\t            end\n  1369\t        elseif file == \&quot;HUNTER\&quot; then\n  1370\t            local rapBase, rapPos, rapNeg = UnitRangedAttackPower('player')\n  1371\t            local critChance = GetRangedCritChance()\n  1372\t            report = report .. \&quot;, 远攻 \&quot; .. (rapBase + rapPos + rapNeg)\n  1373\t            report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1374\t        elseif file == \&quot;MAGE\&quot; or file == \&quot;WARLOCK\&quot; then\n  1375\t            local sp = GetSpellBonusDamage(2)\n  1376\t            local critChance = GetSpellCritChance(2)\n  1377\t            report = report .. \&quot;, 法强 \&quot; .. sp\n  1378\t            report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1379\t        elseif file == \&quot;PRIEST\&quot; then\n  1380\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1381\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1382\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1383\t            if p3 &gt;= p1 and p3 &gt;= p2 then\n  1384\t                -- 暗牧\n  1385\t                local sp = GetSpellBonusDamage(2)\n  1386\t                local critChance = GetSpellCritChance(2)\n  1387\t                report = report .. \&quot;, 法强 \&quot; .. sp\n  1388\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1389\t            else\n  1390\t                -- 治疗牧师\n  1391\t                local heal = GetSpellBonusHealing()\n  1392\t                local critChance = GetSpellCritChance(2)\n  1393\t                report = report .. \&quot;, 治疗 \&quot; .. heal\n  1394\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1395\t            end\n  1396\t        elseif file == \&quot;PALADIN\&quot; then\n  1397\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1398\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1399\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1400\t            if p1 &gt;= p2 and p1 &gt;= p3 then\n  1401\t                -- 神圣骑士\n  1402\t                local heal = GetSpellBonusHealing()\n  1403\t                local critChance = GetSpellCritChance(2)\n  1404\t                report = report .. \&quot;, 治疗 \&quot; .. heal\n  1405\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1406\t            elseif p2 &gt;= p1 and p2 &gt;= p3 then\n  1407\t                -- 防护骑士\n  1408\t                local _, armor = UnitArmor('player')\n  1409\t                local pChance = GetParryChance()\n  1410\t                local dChance = GetDodgeChance()\n  1411\t                local bChance = GetBlockChance()\n  1412\t                local block = GetShieldBlock()\n  1413\t                report = report .. \&quot;, 护甲 \&quot; .. armor\n  1414\t                report = report .. \&quot;, 招架 \&quot; .. string.format(\&quot;%.1f\&quot;, pChance) .. \&quot;%\&quot;\n  1415\t                report = report .. \&quot;, 躲闪 \&quot; .. string.format(\&quot;%.1f\&quot;, dChance) .. \&quot;%\&quot;\n  1416\t                report = report .. \&quot;, 格挡 \&quot; .. string.format(\&quot;%.1f\&quot;, bChance) .. \&quot;% (\&quot; .. block .. \&quot;)\&quot;\n  1417\t            else\n  1418\t                -- 惩戒骑士\n  1419\t                local apBase, apPos, apNeg = UnitAttackPower('player')\n  1420\t                local critChance = GetCritChance()\n  1421\t                report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1422\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1423\t            end\n  1424\t        elseif file == \&quot;ROGUE\&quot; then\n  1425\t            local apBase, apPos, apNeg = UnitAttackPower('player')\n  1426\t            local critChance = GetCritChance()\n  1427\t            report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1428\t            report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1429\t        elseif file == \&quot;DRUID\&quot; then\n  1430\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1431\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1432\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1433\t            if p1 &gt; p2 and p1 &gt;= p3 then\n  1434\t                -- 平衡德鲁伊\n  1435\t                local sp = GetSpellBonusDamage(4) -- 自然法术\n  1436\t                local critChance = GetSpellCritChance(4)\n  1437\t                report = report .. \&quot;, 法强 \&quot; .. sp\n  1438\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1439\t            elseif p3 &gt;= p1 and p3 &gt;= p2 then\n  1440\t                -- 恢复德鲁伊\n  1441\t                local heal = GetSpellBonusHealing()\n  1442\t                local critChance = GetSpellCritChance(4)\n  1443\t                report = report .. \&quot;, 治疗 \&quot; .. heal\n  1444\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1445\t            else\n  1446\t                -- 野性德鲁伊 (需要根据形态判断)\n  1447\t                local apBase, apPos, apNeg = UnitAttackPower('player')\n  1448\t                local critChance = GetCritChance()\n  1449\t                report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1450\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1451\t            end\n  1452\t        elseif file == \&quot;SHAMAN\&quot; then\n  1453\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1454\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1455\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1456\t            if p1 &gt; p2 and p1 &gt;= p3 then\n  1457\t                -- 元素萨满\n  1458\t                local sp = GetSpellBonusDamage(4) -- 自然法术\n  1459\t                local critChance = GetSpellCritChance(4)\n  1460\t                report = report .. \&quot;, 法强 \&quot; .. sp\n  1461\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1462\t            elseif p2 &gt;= p1 and p2 &gt;= p3 then\n  1463\t                -- 增强萨满\n  1464\t                local apBase, apPos, apNeg = UnitAttackPower('player')\n  1465\t                local critChance = GetCritChance()\n  1466\t                report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1467\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1468\t            else\n  1469\t                -- 恢复萨满\n  1470\t                local heal = GetSpellBonusHealing()\n  1471\t                local critChance = GetSpellCritChance(4)\n  1472\t                report = report .. \&quot;, 治疗 \&quot; .. heal\n  1473\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1474\t            end\n  1475\t        end\n  1476\t\n  1477\t        return report\n  1478\t    end\n  1479\t\n  1480\t\n  1481\t    -- 使用新的详细属性报告\n  1482\t    return GetStatReport()\n  1483\tend\n  1484\t\n  1485\t\n  1486\t\n  1487\t-- 刷新聊天条按钮（当插件加载状态改变时调用）\n  1488\tfunction WChat:RefreshChatBarButtons()\n  1489\t    if not WChatBar then return end\n  1490\t\n  1491\t    -- 隐藏所有现有按钮\n  1492\t    for i = 1, WChatBar:GetNumChildren() do\n  1493\t        local child = select(i, WChatBar:GetChildren())\n  1494\t        if child and child:GetObjectType() == \&quot;Button\&quot; then\n  1495\t            child:Hide()\n  1496\t        end\n  1497\t    end\n  1498\t\n  1499\t    -- 重新创建按钮\n  1500\t    local buttonIndex = 1\n  1501\t    for i = 1, #ChannelButtons do\n  1502\t        local buttonData = ChannelButtons[i]\n  1503\t        local shouldShow = true\n  1504\t\n  1505\t        -- 检查插件按钮是否应该显示（必须已加载才显示）\n  1506\t        if buttonData.name == \&quot;biaoge\&quot; then\n  1507\t            shouldShow = WChat_Config.ShowBiaoGeButton and IsAddOnLoaded(\&quot;BiaoGe\&quot;)\n  1508\t        elseif buttonData.name == \&quot;biaogeai\&quot; then\n  1509\t            shouldShow = WChat_Config.ShowBiaoGeAIButton and IsAddOnLoaded(\&quot;BiaoGeAI\&quot;)\n  1510\t        elseif buttonData.name == \&quot;atlas\&quot; then\n  1511\t            shouldShow = WChat_Config.ShowAtlasButton and IsAddOnLoaded(\&quot;AtlasLootClassic\&quot;)\n  1512\t        elseif buttonData.name == \&quot;meetinghorn\&quot; then\n  1513\t            shouldShow = WChat_Config.ShowMeetingHornButton and IsAddOnLoaded(\&quot;MeetingHorn\&quot;)\n  1514\t        end\n  1515\t\n  1516\t        if shouldShow then\n  1517\t            CreateChannelButton(buttonData, buttonIndex)\n  1518\t            buttonIndex = buttonIndex + 1\n  1519\t        end\n  1520\t    end\n  1521\t\n  1522\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 聊天条按钮已刷新\&quot;)\n  1523\tend\n  1524\t\n  1525\t-- 模块初始化函数\n  1526\tfunction WChat:Initialize()\n  1527\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 开始初始化聊天增强模块...\&quot;)\n  1528\t\n  1529\t    -- 逐步初始化各个子模块，便于排查问题\n  1530\t    local success, err = pcall(function()\n  1531\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化频道功能...\&quot;)\n  1532\t        self:InitChannel()\n  1533\t\n  1534\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化表情功能...\&quot;)\n  1535\t        self:InitEmoteTableFrame()\n  1536\t\n  1537\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化聊天条...\&quot;)\n  1538\t        self:InitChatBar()\n  1539\t\n  1540\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化时间戳复制...\&quot;)\n  1541\t        self:InitTimestampCopy()\n  1542\t    end)\n  1543\t\n  1544\t    if success then\n  1545\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 聊天增强模块加载完成\&quot;)\n  1546\t    else\n  1547\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 模块初始化失败: \&quot; .. tostring(err))\n  1548\t    end\n  1549\tend\n  1550\t\n  1551\t-- 配置保存和加载\n  1552\tfunction WChat:LoadConfig()\n  1553\t    if WChatDB then\n  1554\t        for key, value in pairs(WChatDB) do\n  1555\t            if WChat_Config[key] ~= nil then\n  1556\t                WChat_Config[key] = value\n  1557\t            end\n  1558\t        end\n  1559\t    end\n  1560\tend\n  1561\t\n  1562\tfunction WChat:SaveConfig()\n  1563\t    WChatDB = {}\n  1564\t    for key, value in pairs(WChat_Config) do\n  1565\t        WChatDB[key] = value\n  1566\t    end\n  1567\tend\n  1568\t\n  1569\t-- 防止重复初始化\n  1570\tlocal WChat_Initialized = false\n  1571\t\n  1572\t-- 事件处理\n  1573\tlocal WChatFrame = CreateFrame(\&quot;Frame\&quot;)\n  1574\tWChatFrame:RegisterEvent(\&quot;PLAYER_LOGOUT\&quot;)\n  1575\tWChatFrame:SetScript(\&quot;OnEvent\&quot;, function(self, event, addonName)\n  1576\t    if event == \&quot;PLAYER_LOGOUT\&quot; then\n  1577\t        WChat:SaveConfig()\n  1578\t    end\n  1579\tend)\n  1580\t\n  1581\t-- 注册到WanTiny模块系统\n  1582\tif WanTiny_RegisterModule then\n  1583\t    WanTiny_RegisterModule(\&quot;WChat\&quot;, function()\n  1584\t        if not WChat_Initialized then\n  1585\t            WChat_Initialized = true\n  1586\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r WChat模块开始注册...\&quot;)\n  1587\t\n  1588\t            local success, err = pcall(function()\n  1589\t                WChat:LoadConfig()\n  1590\t                WChat:Initialize()\n  1591\t            end)\n  1592\t            if not success then\n  1593\t                print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 模块加载失败: \&quot; .. tostring(err))\n  1594\t            end\n  1595\t        end\n  1596\t    end)\n  1597\telse\n  1598\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r WanTiny_RegisterModule 函数不存在\&quot;)\n  1599\tend\n  1600\t\n  1601\t\n  1602\t\n  1603\t\n  1604\t\n  1605\t\nTotal lines in file: 1605\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;32e43343-b077-4e70-b182-472708e23716;toolu_vrtx_01GemkpEH8VbEFL63Mgf3NhB&quot;:{&quot;requestId&quot;:&quot;32e43343-b077-4e70-b182-472708e23716&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01GemkpEH8VbEFL63Mgf3NhB&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: 333.lua\n...\n  1096\t\n  1097\t    -- 添加战斗计时器设置和聊天增强设置之间的间距\n  1098\t    currentY = currentY - WanTinyUI.LAYOUT.SECTION_GAP\n  1099\t\n  1100\t    -- WChat 聊天增强设置\n  1101\t    ControlFactory.CreateTitle(parent, \&quot;|cff00ff00WChat 聊天增强设置|r\&quot;, currentY)\n  1102\t    currentY = currentY - WanTinyUI.LAYOUT.TITLE_SPACING\n  1103\t\n  1104\t    if wchatLoaded then\n  1105\t\n  1106\t    local wchatConfig = _G.WChatClassicDB\n  1107\t    if not wchatConfig then return end\n  1108\t\n  1109\t    -- 保存UI控件引用，用于恢复默认时更新\n  1110\t    local uiControls = {}\n  1111\t\n  1112\t    -- 创建WChat复选框\n  1113\t    local wchatCheckConfigs = {\n  1114\t        {\&quot;|cff00ff00启用上方输入|r\&quot;, \&quot;UseTopInput\&quot;, \&quot;在聊天框上方显示输入框\&quot;},\n  1115\t        {\&quot;|cffff8000垂直聊天条|r\&quot;, \&quot;UseVertical\&quot;, \&quot;将聊天按钮垂直排列\&quot;, function() if _G.WChatUpdateSliderVisibility then C_Timer.After(0.1, _G.WChatUpdateSliderVisibility) end end},\n  1116\t        {\&quot;|cffffb347启用频道缩写|r\&quot;, \&quot;EnableChannelShortNames\&quot;, \&quot;将频道名称显示为缩写形式\&quot;},\n  1117\t        {\&quot;|cff8080ff锁定聊天条|r\&quot;, \&quot;LockChatBar\&quot;, \&quot;解锁后可移动聊天条位置\&quot;}\n  1118\t    }\n  1119\t\n  1120\t    ControlFactory.CreateGrid(parent, wchatCheckConfigs, 4, WanTinyUI.LAYOUT.CHECKBOX_WIDTH, currentY, function(parent, cfg, x, y)\n  1121\t        local checkbox = ControlFactory.CreateCheckbox(parent, cfg[1], x, y, wchatConfig[cfg[2]], function(self)\n  1122\t            wchatConfig[cfg[2]] = self:GetChecked()\n  1123\t            if _G.WChat and _G.WChat.OnConfigChanged and _G.WChat.OnConfigChanged[cfg[2]] then\n  1124\t                _G.WChat.OnConfigChanged[cfg[2]](wchatConfig[cfg[2]])\n  1125\t            end\n  1126\t            if cfg[4] then cfg[4]() end\n  1127\t        end, cfg[3])\n  1128\t        uiControls[cfg[2] .. \&quot;Check\&quot;] = checkbox\n  1129\t        return checkbox\n  1130\t    end)\n  1131\t    currentY = currentY - ROW_HEIGHT  -- 复选框占用1行\n...\n  1208\t\n  1209\t    -- 创建动态间距滑块（根据垂直聊天条状态切换）\n  1210\t    local distanceSlider\n  1211\t    local distanceSliderContainer\n  1212\t    local function updateDistanceSlider()\n  1213\t        if not distanceSlider or not distanceSliderContainer then return end\n  1214\t        local isVertical = wchatConfig.UseVertical\n  1215\t        local label = isVertical and \&quot;|cff00ff00垂直间距|r\&quot; or \&quot;|cff00ff00水平间距|r\&quot;\n  1216\t        local key = isVertical and \&quot;DistanceVertical\&quot; or \&quot;DistanceHorizontal\&quot;\n  1217\t        local tooltip = isVertical and \&quot;调整垂直排列时按钮之间的间距\&quot; or \&quot;调整水平排列时按钮之间的间距\&quot;\n  1218\t        local cleanLabel = label:gsub(\&quot;|c%x%x%x%x%x%x%x%x\&quot;, \&quot;\&quot;):gsub(\&quot;|r\&quot;, \&quot;\&quot;)\n  1219\t\n  1220\t        -- 获取当前实际配置值，如果不存在则使用默认值\n  1221\t        local currentValue = wchatConfig[key]\n  1222\t        if currentValue == nil then\n  1223\t            currentValue = 24  -- 统一使用24作为默认值，与WChat.DefaultConfig一致\n  1224\t            wchatConfig[key] = currentValue  -- 设置默认值到配置中\n  1225\t        end\n...\n  1252\t\n  1253\t    -- 统一的2列滑块布局起始X坐标\n  1254\t    local sliderStartX = LayoutUtils.GetCenteredStartXSafe(parent, WanTinyUI.LAYOUT.SLIDER_WIDTH, 2, 0)\n  1255\t\n  1256\t    -- 创建动态间距滑块（第一行左侧）\n  1257\t    local x, y = LayoutUtils.GetGridPosition(1, 2, WanTinyUI.LAYOUT.SLIDER_WIDTH, ROW_HEIGHT, sliderStartX, currentY, 0)\n  1258\t\n  1259\t    -- 获取初始配置值\n  1260\t    local initialKey = wchatConfig.UseVertical and \&quot;DistanceVertical\&quot; or \&quot;DistanceHorizontal\&quot;\n  1261\t    local initialValue = wchatConfig[initialKey] or 24  -- 统一使用24作为默认值\n  1262\t    local initialLabel = wchatConfig.UseVertical and \&quot;|cff00ff00垂直间距|r\&quot; or \&quot;|cff00ff00水平间距|r\&quot;\n  1263\t    local initialTooltip = wchatConfig.UseVertical and \&quot;调整垂直排列时按钮之间的间距\&quot; or \&quot;调整水平排列时按钮之间的间距\&quot;\n  1264\t\n  1265\t    distanceSlider = CreateLabeledSlider(parent, initialLabel, x, y, 10, 50, 1, initialValue,\n  1266\t        function(self, value)\n  1267\t            local key = self.currentKey or \&quot;DistanceHorizontal\&quot;\n  1268\t            wchatConfig[key] = value\n  1269\t            local wc = LayoutUtils.SafeGetGlobal(\&quot;WChat\&quot;)\n  1270\t            if wc and wc.OnConfigChanged and wc.OnConfigChanged[key] then\n  1271\t                wc.OnConfigChanged[key](value)\n  1272\t            end\n  1273\t        end, nil, initialTooltip)\n  1274\t\n  1275\t    distanceSlider.currentKey = initialKey\n  1276\t    distanceSliderContainer = distanceSlider:GetParent()  -- 保存容器引用\n  1277\t    uiControls.distanceSlider = distanceSlider\n  1278\t\n  1279\t    -- 标签引用已经在CreateLabeledSlider中设置了\n  1280\t\n  1281\t    -- WChat配置更新回调（避免重复获取）\n  1282\t    local wchatCallbacks = LayoutUtils.SafeGetGlobal(\&quot;WChat\&quot;) and LayoutUtils.SafeGetGlobal(\&quot;WChat\&quot;).OnConfigChanged\n...\n  1297\t\n  1298\t    -- 第二行右侧：预览表情滑块\n  1299\t    local x3, y3 = LayoutUtils.GetGridPosition(2, 2, WanTinyUI.LAYOUT.SLIDER_WIDTH, ROW_HEIGHT, sliderStartX, currentY - ROW_HEIGHT, 0)\n  1300\t    local emoteSlider = ControlFactory.CreateSlider(parent, \&quot;|cffff69b4预览表情|r\&quot;, x3, y3, 20, 40, 1, wchatConfig.EmoteIconListSize or 30,\n  1301\t        function(self, value) LayoutUtils.UpdateConfig(wchatConfig, \&quot;EmoteIconListSize\&quot;, value, wchatCallbacks) end, nil, \&quot;表情选择面板中图标的预览大小\&quot;)\n  1302\t    uiControls.emoteSlider = emoteSlider\n  1303\t\n  1304\t    currentY = currentY - ROW_HEIGHT * 2  -- 2行完成\n  1305\t\n  1306\t    -- 设置全局更新函数供复选框调用\n  1307\t    _G.WChatUpdateSliderVisibility = updateDistanceSlider\n  1308\t    updateDistanceSlider()  -- 初始化\n...\nPath: 222.lua\n...\n   258\t\n   259\t-- 批量创建配置处理器\n   260\tlocal configHandlers = {\n   261\t    UseVertical = function(v) if WChat.InitChatBar then WChat:InitChatBar(); if WChat.ChatBar then WChat.ChatBar:Show() end end end,\n   262\t    UseTopInput = function(v)\n   263\t        if _G.ElvUI then return end\n   264\t        local editBox = DEFAULT_CHAT_FRAME.editBox\n   265\t        if editBox then\n   266\t            editBox:ClearAllPoints()\n   267\t            local point = v and \&quot;BOTTOMLEFT\&quot; or \&quot;TOPLEFT\&quot;\n   268\t            local relativePoint = v and \&quot;TOPLEFT\&quot; or \&quot;BOTTOMLEFT\&quot;\n   269\t            local yOffset = v and 20 or -5\n   270\t            editBox:SetPoint(point, DEFAULT_CHAT_FRAME, relativePoint, 0, yOffset)\n   271\t            editBox:SetPoint(point:gsub(\&quot;LEFT\&quot;, \&quot;RIGHT\&quot;), DEFAULT_CHAT_FRAME, relativePoint:gsub(\&quot;LEFT\&quot;, \&quot;RIGHT\&quot;), 0, yOffset)\n   272\t        end\n   273\t    end,\n   274\t    EmoteIconListSize = function() if WChat.InitEmoteTableFrame then WChat:InitEmoteTableFrame() end end,\n   275\t    LockChatBar = function(v)\n...\n   884\t        end\n   885\t    end\n   886\t\n   887\t    -- 创建频道屏蔽X图标\n   888\t    if CHANNEL_CONFIG.MAPPINGS[data.name] then\n   889\t        frame.X = frame:CreateTexture(nil, \&quot;OVERLAY\&quot;)\n   890\t        frame.X:SetTexture(\&quot;interface/common/voicechat-muted.blp\&quot;)\n   891\t        frame.X:SetSize(math.max(12, buttonSize * 0.6), math.max(12, buttonSize * 0.6))\n   892\t        frame.X:SetAlpha(0.7)\n   893\t        frame.X:SetPoint(\&quot;CENTER\&quot;)\n   894\t        frame.X:SetDrawLayer(\&quot;OVERLAY\&quot;, 7)\n   895\t        frame.X:Hide()\n   896\t    end\n   897\t\n   898\t    frame.buttonName = data.name\n   899\t\n   900\t    local isVertical = config.UseVertical\n   901\t    local point, x, y = isVertical and \&quot;TOP\&quot; or \&quot;LEFT\&quot;,\n   902\t        isVertical and 0 or (10 + (index - 1) * config.DistanceHorizontal),\n   903\t        isVertical and ((1 - index) * config.DistanceVertical) or 0\n   904\t    frame:SetPoint(point, ChatBar, point, x, y)\n...\n   920\t\n   921\tfunction WChat:InitChatBar()\n   922\t    local config = GetConfig()\n   923\t    ChatBar:SetFrameLevel(0)\n   924\t\n   925\t    -- 清理现有按钮\n   926\t    Utils.ProcessChannels({ChatBar:GetChildren()}, function(child)\n   927\t        if child.buttonName then child:Hide(); child:SetParent(nil) end\n   928\t    end)\n   929\t    WChat.emoteButtonFrame, BGButtonFrame, WChat.BGButtonFrame, ChatBar.MinimizeBtn = nil, nil, nil, nil\n   930\t\n   931\t    local buttonSize, padding, isVertical = config.ButtonSize or 22, 8, config.UseVertical\n   932\t    local width = isVertical and (buttonSize + padding) or (#ChannelButtons * config.DistanceHorizontal + 10)\n   933\t    local height = isVertical and (#ChannelButtons * config.DistanceVertical + 10) or (buttonSize + padding)\n   934\t    ChatBar:SetSize(width, height)\n...\n   954\t\n   955\t    SetupChatBarDragging(ChatBar, not config.LockChatBar)\n   956\t    ChatBarUtils.setBackdrop(not config.LockChatBar)\n   957\t\n   958\t    for i = 1, #ChannelButtons do CreateChannelButton(ChannelButtons[i], i, config) end\n   959\t    -- 移除频道组，但不自动添加任何频道，让用户自己决定\n   960\t    ChatFrame_RemoveMessageGroup(DEFAULT_CHAT_FRAME, \&quot;CHANNEL\&quot;)\n   961\t\n   962\t    -- 注册事件\n   963\t    ChatBar:UnregisterAllEvents()\n   964\t    Utils.RegisterEvents(ChatBar, {\&quot;PLAYER_ENTERING_WORLD\&quot;, \&quot;ZONE_CHANGED_NEW_AREA\&quot;, \&quot;UPDATE_CHAT_WINDOWS\&quot;, \&quot;CHAT_MSG_WHISPER\&quot;, \&quot;CHAT_MSG_BN_WHISPER\&quot;})\n   965\t\n   966\t    ChatBar:SetScript(\&quot;OnEvent\&quot;, function(self, event)\n   967\t        if event == \&quot;PLAYER_ENTERING_WORLD\&quot; or event == \&quot;ZONE_CHANGED_NEW_AREA\&quot; then\n   968\t            UpdateBGButtonText()\n   969\t            -- 确保频道默认显示，但尊重用户的屏蔽设置\n   970\t            C_Timer.After(1, function()\n   971\t                Utils.ProcessDefaultChannels(true)  -- 完整版本，考虑屏蔽设置\n   972\t            end)\n   973\t        elseif event == \&quot;UPDATE_CHAT_WINDOWS\&quot; then\n   974\t            UpdateChannelXIcons()\n   975\t        elseif event == \&quot;CHAT_MSG_WHISPER\&quot; or event == \&quot;CHAT_MSG_BN_WHISPER\&quot; then\n   976\t            C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, UpdateWhisperButtonFlash)\n   977\t        end\n   978\t    end)\n   979\t\n   980\t    C_Timer.After(1, function() UpdateBGButtonText(); UpdateMinimizeButtonText(); UpdateChannelXIcons(); UpdateWhisperButtonFlash() end)\n   981\t    if not WChat.chatBarInitialized then WChat.chatBarInitialized = true end\n   982\tend\n...\nPath: Moudel/WChat.lua\n...\n     7\t\n     8\t-- 模块配置\n     9\tlocal WChat = {}\n    10\tlocal WChat_Config = {\n    11\t    -- 默认配置\n    12\t    ShortChannel = true,        -- 频道标签精简\n    13\t    EnableEmoteInput = true,    -- 启用表情输入\n    14\t    EmoteIconSize = 16,         -- 表情图标大小\n    15\t    EmoteIconListSize = 20,     -- 表情列表图标大小\n    16\t    EmoteOffsetX = 0,           -- 表情面板X偏移\n    17\t    EmoteOffsetY = 30,          -- 表情面板Y偏移\n    18\t    UseVertical = false,        -- 聊天条垂直布局\n    19\t    UseTopChatbar = false,      -- 聊天条在上方\n    20\t    UseTopInput = false,        -- 输入框在上方\n    21\t    ChatBarOffsetX = 0,         -- 聊天条X偏移\n    22\t    ChatBarOffsetY = 0,         -- 聊天条Y偏移\n    23\t    DistanceVertical = -25,     -- 垂直间距\n    24\t    DistanceHorizontal = 25,    -- 水平间距\n    25\t    AlphaOnEnter = 1.0,         -- 鼠标悬停透明度\n    26\t    AlphaOnLeave = 0.7,         -- 鼠标离开透明度\n    27\t    Position = nil,             -- 聊天条位置\n    28\t    EnableTimestampCopy = true, -- 启用时间戳点击复制\n    29\t    TimestampFormat = \&quot;%H:%M:%S\&quot;, -- 时间戳格式\n    30\t    TimestampColor = { 1.0, 1.0, 1.0 }, -- 时间戳颜色\n    31\t    -- 插件按钮显示控制\n    32\t    ShowBiaoGeButton = true,    -- 显示BiaoGe按钮\n    33\t    ShowBiaoGeAIButton = true,  -- 显示BiaoGeAI按钮\n    34\t    ShowAtlasButton = true,     -- 显示Atlas按钮\n    35\t    ShowMeetingHornButton = true, -- 显示MeetingHorn按钮\n    36\t    -- 频道隐藏设置\n    37\t    HiddenChannels = {}\n    38\t}\n    39\t\n    40\t-- 频道配置映射\n    41\tlocal CHANNEL_CONFIG = {\n    42\t    MAPPINGS = {\n    43\t        chnGen = \&quot;综合\&quot;,\n    44\t        chnTrade = \&quot;交易\&quot;,\n    45\t        chnLFG = \&quot;寻求组队\&quot;,\n    46\t        world = \&quot;大脚世界频道\&quot;\n    47\t    }\n    48\t}\n    49\t\n    50\t-- 频道显示/隐藏管理函数\n    51\tlocal function IsChannelShown(channelName)\n    52\t    local channels = {GetChatWindowChannels(DEFAULT_CHAT_FRAME:GetID() or 1)}\n    53\t    for i = 1, #channels, 2 do\n    54\t        if channels[i] == channelName then return true end\n    55\t    end\n    56\t    return false\n    57\tend\n...\n   271\t\n   272\t-- 初始化频道信息精简模块\n   273\tfunction WChat:InitChannel()\n   274\t    if WChat_Config.ShortChannel then\n   275\t        for i = 1, (NUM_CHAT_WINDOWS or 10) do\n   276\t            if i ~= 2 then\n   277\t                local cf = _G['ChatFrame' .. i]\n   278\t                cf.DefaultAddMessage = cf.AddMessage\n   279\t                cf.AddMessage = self.AddMessage\n   280\t            end\n   281\t        end\n   282\t    end\n   283\tend\n   284\t-------------------------------聊天复制------------------------------------\n   285\t\n   286\tlocal lines = {}\n...\n   774\t\n   775\t-- WChat集成的时间戳复制功能\n   776\tfunction WChat:InitTimestampCopy()\n   777\t    if WChat_Config.EnableTimestampCopy then\n   778\t        -- 初始化_db配置\n   779\t        _db = {\n   780\t            toggle = true,\n   781\t            format = WChat_Config.TimestampFormat or \&quot;%H:%M:%S\&quot;,\n   782\t            color = WChat_Config.TimestampColor or { 1.0, 1.0, 1.0 }\n   783\t        };\n   784\t\n   785\t        -- 启用功能\n   786\t        __copy.toggle(true, false);\n   787\t    end\n   788\tend\n   789\t\n   790\t-----------------------------聊天条\n   791\t\n   792\t--[[=========================== 变量区 ==========================]]\n   793\t-- 是否可移动的标记\n   794\tlocal IsMovable = false -- 没事干别动这个，你改成ture那么进入游戏后聊天条就是可以移动的\n   795\t--[[=============================== END ==============================]]\n   796\tlocal chatFrame = SELECTED_DOCK_FRAME -- 聊天框架\n   797\tlocal inputbox = chatFrame.editBox -- 输入框\n   798\t\n   799\tCOLORSCHEME_BORDER = {0.3, 0.3, 0.3, 1}\n   800\t-- 边框颜色\n   801\t-- 主框架初始化\n   802\tlocal ChatBar = CreateFrame(\&quot;Frame\&quot;, nil, UIParent, BackdropTemplateMixin and \&quot;BackdropTemplate\&quot; or nil)\n   803\tWChatBar = ChatBar\n   804\t\n   805\tlocal function ChannelSay_OnClick()\n   806\t    ChatFrame_OpenChat(\&quot;/s \&quot; .. inputbox:GetText(), chatFrame)\n   807\tend\n   808\t\n   809\tlocal function ChannelYell_OnClick()\n   810\t    ChatFrame_OpenChat(\&quot;/y \&quot; .. inputbox:GetText(), chatFrame)\n   811\tend\n...\n  1086\t        end\n  1087\t        WChatBar:EnableMouse(IsMovable)\n  1088\t    elseif button == \&quot;MiddleButton\&quot; then\n  1089\t        if IsMovable == false then\n  1090\t            return\n  1091\t        end\n  1092\t        WChatBar:ClearAllPoints()\n  1093\t        if WChat_Config.UseVertical then\n  1094\t            if WChat_Config.UseTopChatbar then\n  1095\t                WChatBar:SetPoint(\&quot;TOPRIGHT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX - 30, WChat_Config.ChatBarOffsetY + 25)\n  1096\t            else\n  1097\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPRIGHT\&quot;, WChat_Config.ChatBarOffsetX + 30, WChat_Config.ChatBarOffsetY + 25)\n  1098\t            end\n  1099\t        else\n  1100\t            if WChat_Config.UseTopChatbar then\n  1101\t                WChatBar:SetPoint(\&quot;BOTTOMLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY + 30)\n  1102\t            else\n  1103\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;BOTTOMLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY - 30)\n  1104\t            end\n  1105\t        end\n  1106\t    end\n  1107\tend\n...\n  1162\t    if WChat_Config.UseVertical then\n  1163\t        frame:SetPoint(\&quot;TOP\&quot;, WChatBar, \&quot;TOP\&quot;, 0, (1 - index) * WChat_Config.DistanceVertical)\n  1164\t    else\n  1165\t        frame:SetPoint(\&quot;LEFT\&quot;, WChatBar, \&quot;LEFT\&quot;, 10 + (index - 1) * WChat_Config.DistanceHorizontal, 0)\n  1166\t    end\n  1167\t    \n  1168\t    frame:RegisterForClicks(\&quot;AnyUp\&quot;)\n  1169\t    frame:SetScript(\&quot;OnClick\&quot;, data.callback)\n  1170\t    -- 显示的文字\n  1171\t    frameText = frame:CreateFontString(data.name .. \&quot;Text\&quot;, \&quot;OVERLAY\&quot;)\n  1172\t    -- 字体设置\n  1173\t    frameText:SetFont(STANDARD_TEXT_FONT, 15, \&quot;OUTLINE\&quot;)\n  1174\t    \n  1175\t    frameText:SetJustifyH(\&quot;CENTER\&quot;)\n  1176\t    frameText:SetWidth(26)\n  1177\t    frameText:SetHeight(26)\n  1178\t    frameText:SetText(data.text)\n  1179\t    frameText:SetPoint(\&quot;CENTER\&quot;, 0, 0)\n  1180\t    \n  1181\t    -- 文字按钮的颜色\n  1182\t    frameText:SetTextColor(data.color[1], data.color[2], data.color[3])\n...\n  1199\t\n  1200\tfunction WChat:InitChatBar()\n  1201\t\n  1202\t    WChatBar:SetFrameLevel(0)\n  1203\t\n  1204\t    -- 使用竖直布局\n  1205\t    if WChat_Config.UseVertical then\n  1206\t        -- 主框体宽度\n  1207\t        WChatBar:SetWidth(30)\n  1208\t        -- 主框体高度\n  1209\t        WChatBar:SetHeight(#ChannelButtons * WChat_Config.DistanceVertical + 10)\n  1210\t    else\n  1211\t        -- 主框体宽度\n  1212\t        WChatBar:SetWidth(#ChannelButtons * WChat_Config.DistanceHorizontal + 10)\n  1213\t        -- 主框体高度\n  1214\t        WChatBar:SetHeight(30)\n  1215\t    end\n  1216\t\n  1217\t    -- 上方聊天输入框\n  1218\t    if WChat_Config.UseTopInput then\n  1219\t        inputbox:ClearAllPoints()\n  1220\t        inputbox:SetPoint(\&quot;BOTTOMLEFT\&quot;, chatFrame, \&quot;TOPLEFT\&quot;, 0, 20)\n  1221\t        inputbox:SetPoint(\&quot;BOTTOMRIGHT\&quot;, chatFrame, \&quot;TOPRIGHT\&quot;, 0, 20)\n  1222\t    end\n...\n  1521\t\n  1522\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 聊天条按钮已刷新\&quot;)\n  1523\tend\n  1524\t\n  1525\t-- 模块初始化函数\n  1526\tfunction WChat:Initialize()\n  1527\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 开始初始化聊天增强模块...\&quot;)\n  1528\t\n  1529\t    -- 逐步初始化各个子模块，便于排查问题\n  1530\t    local success, err = pcall(function()\n  1531\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化频道功能...\&quot;)\n  1532\t        self:InitChannel()\n  1533\t\n  1534\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化表情功能...\&quot;)\n  1535\t        self:InitEmoteTableFrame()\n  1536\t\n  1537\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化聊天条...\&quot;)\n  1538\t        self:InitChatBar()\n  1539\t\n  1540\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化时间戳复制...\&quot;)\n  1541\t        self:InitTimestampCopy()\n  1542\t    end)\n  1543\t\n  1544\t    if success then\n  1545\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 聊天增强模块加载完成\&quot;)\n  1546\t    else\n  1547\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 模块初始化失败: \&quot; .. tostring(err))\n  1548\t    end\n  1549\tend\n  1550\t\n  1551\t-- 配置保存和加载\n  1552\tfunction WChat:LoadConfig()\n  1553\t    if WChatDB then\n  1554\t        for key, value in pairs(WChatDB) do\n  1555\t            if WChat_Config[key] ~= nil then\n  1556\t                WChat_Config[key] = value\n  1557\t            end\n  1558\t        end\n  1559\t    end\n  1560\tend\n  1561\t\n  1562\tfunction WChat:SaveConfig()\n  1563\t    WChatDB = {}\n  1564\t    for key, value in pairs(WChat_Config) do\n  1565\t        WChatDB[key] = value\n  1566\t    end\n  1567\tend\n  1568\t\n  1569\t-- 防止重复初始化\n  1570\tlocal WChat_Initialized = false\n  1571\t\n  1572\t-- 事件处理\n  1573\tlocal WChatFrame = CreateFrame(\&quot;Frame\&quot;)\n  1574\tWChatFrame:RegisterEvent(\&quot;PLAYER_LOGOUT\&quot;)\n  1575\tWChatFrame:SetScript(\&quot;OnEvent\&quot;, function(self, event, addonName)\n  1576\t    if event == \&quot;PLAYER_LOGOUT\&quot; then\n  1577\t        WChat:SaveConfig()\n  1578\t    end\n  1579\tend)\n  1580\t\n  1581\t-- 注册到WanTiny模块系统\n  1582\tif WanTiny_RegisterModule then\n  1583\t    WanTiny_RegisterModule(\&quot;WChat\&quot;, function()\n  1584\t        if not WChat_Initialized then\n  1585\t            WChat_Initialized = true\n  1586\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r WChat模块开始注册...\&quot;)\n  1587\t\n  1588\t            local success, err = pcall(function()\n  1589\t                WChat:LoadConfig()\n  1590\t                WChat:Initialize()\n  1591\t            end)\n  1592\t            if not success then\n  1593\t                print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 模块加载失败: \&quot; .. tostring(err))\n  1594\t            end\n  1595\t        end\n  1596\t    end)\n  1597\telse\n  1598\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r WanTiny_RegisterModule 函数不存在\&quot;)\n  1599\tend\n  1600\t\n  1601\t\n  1602\t\n  1603\t\n  1604\t\n...\nPath: WanTinyUI.lua\n...\n   816\t\n   817\t        local cfg = GetWanMenuConfig()\n   818\t        local checkboxStartX = LayoutUtils.GetCenteredStartXSafe(parent, WanTinyUI.LAYOUT.CHECKBOX_WIDTH, 3, 0)\n   819\t\n   820\t        -- 创建复选框（独立行）\n   821\t        for i, ctrl in ipairs(markControls) do\n   822\t            if ctrl.type == \&quot;checkbox\&quot; then\n   823\t                local x = checkboxStartX + (i-1) * WanTinyUI.LAYOUT.CHECKBOX_WIDTH\n   824\t                local value = cfg[ctrl.key] ~= false\n   825\t                CreateLabeledCheckButton(parent, ctrl.label, x, currentY, value, function(self) UpdateWanMenuConfig(ctrl.key, self:GetChecked()) end, ctrl.desc)\n   826\t            end\n   827\t        end\n   828\t        currentY = currentY - 35  -- 复选框占用1行\n   829\t\n   830\t        -- 创建滑块（独立行，两个滑块在同一行）\n   831\t        local sliderWidth = WanTinyUI.LAYOUT.SLIDER_WIDTH\n   832\t        local sliderSpacing = 30\n   833\t        local sliderStartX = LayoutUtils.GetCenteredStartXSafe(parent, sliderWidth * 2 + sliderSpacing, 1, 0)\n   834\t        local sliderIndex = 0\n...\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;68cc7321-0034-4db5-8b04-0910eb471ed9;toolu_vrtx_01ScQABFyyaDSwCNonGqCv25&quot;:{&quot;requestId&quot;:&quot;68cc7321-0034-4db5-8b04-0910eb471ed9&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01ScQABFyyaDSwCNonGqCv25&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on 222.lua:\n     1\t-- 统一配置和常量\n     2\tlocal CHANNEL_CONFIG = {\n     3\t    DEFAULT_CHANNELS = {\&quot;综合\&quot;, \&quot;交易\&quot;, \&quot;寻求组队\&quot;, \&quot;大脚世界频道\&quot;},\n     4\t    PATTERNS = {\n     5\t        \&quot;%[(%d+)%. 综合.-%]\&quot;, \&quot;%[(%d+)%. 交易.-%]\&quot;, \&quot;%[(%d+)%. 本地防务.-%]\&quot;,\n     6\t        \&quot;%[(%d+)%. 寻求组队%]\&quot;, \&quot;%[(%d+)%. 世界防务%]\&quot;, \&quot;%[(%d+)%. 公会招募.-%]\&quot;,\n     7\t        \&quot;%[(%d+)%. 大脚世界频道.-%]\&quot;\n     8\t    },\n     9\t    REPLACEMENTS = {\&quot;[%1.综]\&quot;, \&quot;[%1.交]\&quot;, \&quot;[%1.防]\&quot;, \&quot;[%1.组]\&quot;, \&quot;[%1.守]\&quot;, \&quot;[%1.招]\&quot;, \&quot;[%1.世]\&quot;},\n    10\t    MAPPINGS = {chnGen = \&quot;综合\&quot;, chnTrade = \&quot;交易\&quot;, chnLFG = \&quot;寻求组队\&quot;, world = \&quot;大脚世界频道\&quot;}\n    11\t}\n    12\t\n    13\t-- WChat主体和配置\n    14\tlocal WChat = {\n    15\t    DefaultConfig = {\n    16\t        UseTopInput = true, UseVertical = false, DistanceVertical = 24, DistanceHorizontal = 24,\n    17\t        AlphaOnLeave = 1.0, EmoteIconListSize = 30, ButtonSize = 22, EnableEmoteInput = true,\n    18\t        Position = nil, LockChatBar = true, HiddenChannels = {}, EnableChannelShortNames = true,\n    19\t        EnableTimestamp = true, TimestampColor = {r = 255, g = 20, b = 147}, TimestampFormat = \&quot;[%H:%M:%S]\&quot;\n    20\t    },\n    21\t    registeredFilters = {}\n    22\t}\n    23\t\n    24\t-- 基础工具函数（避免循环依赖）\n    25\tlocal function DeepCopy(t)\n    26\t    if type(t) ~= \&quot;table\&quot; then return t end\n    27\t    local c = {}\n    28\t    for k, v in pairs(t) do c[k] = DeepCopy(v) end\n    29\t    return c\n    30\tend\n    31\t\n    32\t-- 配置管理（简化版）\n    33\tlocal function GetConfig()\n    34\t    if not WChatClassicDB then WChatClassicDB = {} end\n    35\t    for key, defaultValue in pairs(WChat.DefaultConfig) do\n    36\t        if WChatClassicDB[key] == nil then\n    37\t            WChatClassicDB[key] = DeepCopy(defaultValue)\n    38\t        elseif key == \&quot;TimestampColor\&quot; and type(WChatClassicDB[key]) == \&quot;table\&quot; then\n    39\t            local c = WChatClassicDB[key]\n    40\t            if (c.r and c.g and c.b and c.r &lt;= 1) or (c.r == 255 and c.g == 182 and c.b == 193) then\n    41\t                WChatClassicDB[key] = DeepCopy(defaultValue)\n    42\t            end\n    43\t        elseif key == \&quot;TimestampFormat\&quot; and WChatClassicDB[key] == \&quot;%H:%M:%S\&quot; then\n    44\t            WChatClassicDB[key] = \&quot;[%H:%M:%S]\&quot;\n    45\t        end\n    46\t    end\n    47\t    return WChatClassicDB\n    48\tend\n    49\t\n    50\t-- 统一工具函数\n    51\tlocal Utils = {\n    52\t    GetConfigSafe = function() return pcall(GetConfig) and GetConfig() or nil end,\n    53\t    -- 统一的按钮鼠标事件设置\n    54\t    SetButtonHoverEvents = function(button, enterAlpha, leaveAlpha, tooltip)\n    55\t        button:SetScript(\&quot;OnEnter\&quot;, function(self)\n    56\t            if enterAlpha then self:SetAlpha(enterAlpha) end\n    57\t            if tooltip then\n    58\t                GameTooltip:SetOwner(self, \&quot;ANCHOR_RIGHT\&quot;)\n    59\t                GameTooltip:SetText(tooltip)\n    60\t                GameTooltip:Show()\n    61\t            end\n    62\t        end)\n    63\t        button:SetScript(\&quot;OnLeave\&quot;, function(self)\n    64\t            if leaveAlpha then self:SetAlpha(leaveAlpha) end\n    65\t            GameTooltip:Hide()\n    66\t        end)\n    67\t    end,\n    68\t    -- 统一的事件注册\n    69\t    RegisterEvents = function(frame, events)\n    70\t        for _, event in ipairs(events) do frame:RegisterEvent(event) end\n    71\t    end,\n    72\t    -- 统一的频道处理\n    73\t    ProcessChannels = function(channels, callback)\n    74\t        for _, channelName in ipairs(channels) do callback(channelName) end\n    75\t    end,\n    76\t    -- 统一的频道添加/移除处理（消除重复逻辑）\n    77\t    ProcessDefaultChannels = function(includeHiddenCheck)\n    78\t        local config = includeHiddenCheck and GetConfig() or nil\n    79\t        for _, channelName in ipairs(CHANNEL_CONFIG.DEFAULT_CHANNELS) do\n    80\t            local channelId = GetChannelName(channelName)\n    81\t            if channelId and channelId &gt; 0 then\n    82\t                if includeHiddenCheck then\n    83\t                    -- 完整版本：考虑用户屏蔽设置\n    84\t                    if not config.HiddenChannels[channelName] and not IsChannelShown(channelName) then\n    85\t                        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)\n    86\t                    elseif config.HiddenChannels[channelName] and IsChannelShown(channelName) then\n    87\t                        ChatFrame_RemoveChannel(DEFAULT_CHAT_FRAME, channelName)\n    88\t                    end\n    89\t                else\n    90\t                    -- 简化版本：只添加未显示的频道\n    91\t                    if not IsChannelShown(channelName) then\n    92\t                        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)\n    93\t                    end\n    94\t                end\n    95\t            end\n    96\t        end\n    97\t        UpdateChannelXIcons()\n    98\t    end,\n    99\t    -- 统一的字符串检查\n   100\t    HasMarkup = function(text, markup) return text and string.find(text, markup) end,\nTotal lines in file: 2474\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;82c12a8d-744e-4eaf-adf5-9ba9b71e8c4a;toolu_vrtx_01Fjm3kRXAeiRLgcmmPqTEkX&quot;:{&quot;requestId&quot;:&quot;82c12a8d-744e-4eaf-adf5-9ba9b71e8c4a&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01Fjm3kRXAeiRLgcmmPqTEkX&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on 222.lua:\n   250\tlocal function HandleConfigChange(key, value, customHandler)\n   251\t    GetConfig()[key] = value\n   252\t    if customHandler then\n   253\t        customHandler(value)\n   254\t    elseif WChat.InitChatBar then\n   255\t        WChat:InitChatBar()\n   256\t    end\n   257\tend\n   258\t\n   259\t-- 批量创建配置处理器\n   260\tlocal configHandlers = {\n   261\t    UseVertical = function(v) if WChat.InitChatBar then WChat:InitChatBar(); if WChat.ChatBar then WChat.ChatBar:Show() end end end,\n   262\t    UseTopInput = function(v)\n   263\t        if _G.ElvUI then return end\n   264\t        local editBox = DEFAULT_CHAT_FRAME.editBox\n   265\t        if editBox then\n   266\t            editBox:ClearAllPoints()\n   267\t            local point = v and \&quot;BOTTOMLEFT\&quot; or \&quot;TOPLEFT\&quot;\n   268\t            local relativePoint = v and \&quot;TOPLEFT\&quot; or \&quot;BOTTOMLEFT\&quot;\n   269\t            local yOffset = v and 20 or -5\n   270\t            editBox:SetPoint(point, DEFAULT_CHAT_FRAME, relativePoint, 0, yOffset)\n   271\t            editBox:SetPoint(point:gsub(\&quot;LEFT\&quot;, \&quot;RIGHT\&quot;), DEFAULT_CHAT_FRAME, relativePoint:gsub(\&quot;LEFT\&quot;, \&quot;RIGHT\&quot;), 0, yOffset)\n   272\t        end\n   273\t    end,\n   274\t    EmoteIconListSize = function() if WChat.InitEmoteTableFrame then WChat:InitEmoteTableFrame() end end,\n   275\t    LockChatBar = function(v)\n   276\t        if WChat.ChatBar then\n   277\t            SetupChatBarDragging(WChat.ChatBar, not v)\n   278\t            ChatBarUtils.setBackdrop(not v)\n   279\t        end\n   280\t    end,\n   281\t    ResetToDefaults = function()\n   282\t        WChatClassicDB = nil\n   283\t        GetConfig()\n   284\t        if WChat.InitChatBar then WChat:InitChatBar() end\n   285\t        if WChat.InitEmoteTableFrame then WChat:InitEmoteTableFrame() end\n   286\t        WChat.resetTimer = C_Timer.NewTimer(1, function()\n   287\t            Utils.ProcessDefaultChannels(false)  -- 简化版本，不考虑屏蔽设置\n   288\t        end)\n   289\t    end\n   290\t}\n   291\t\n   292\t-- 频道缩写和时间戳处理\n   293\tlocal ChannelShortener = {\n   294\t    ProcessMessage = function(self, text, ...)\n   295\t        local config = Utils.GetConfigSafe()\n   296\t        if not config then return self.OriginalAddMessage(self, text, ...) end\n   297\t        text = tostring(text or \&quot;\&quot;)\n   298\t\n   299\t        -- 频道缩写处理\n   300\t        if config.EnableChannelShortNames then\n   301\t            for i = 1, #CHANNEL_CONFIG.PATTERNS do\n   302\t                text = text:gsub(CHANNEL_CONFIG.PATTERNS[i], CHANNEL_CONFIG.REPLACEMENTS[i])\n   303\t            end\n   304\t            text = text:gsub(\&quot;%[(%d+)%. .-%]\&quot;, \&quot;%1.\&quot;)\n   305\t        end\n   306\t\n   307\t        -- 时间戳处理\n   308\t        if config.EnableTimestamp then\n   309\t            local timeColor = config.TimestampColor or {r = 255, g = 20, b = 147}\n   310\t            local r, g, b = timeColor.r &gt; 1 and timeColor.r or timeColor.r * 255,\n   311\t                           timeColor.g &gt; 1 and timeColor.g or timeColor.g * 255,\n   312\t                           timeColor.b &gt; 1 and timeColor.b or timeColor.b * 255\n   313\t            local colorCode = string.format(\&quot;ff%02x%02x%02x\&quot;,\n   314\t                math.min(255, math.max(0, math.floor(r))),\n   315\t                math.min(255, math.max(0, math.floor(g))),\n   316\t                math.min(255, math.max(0, math.floor(b))))\n   317\t\n   318\t            local timestamp = date(config.TimestampFormat or \&quot;[%H:%M:%S]\&quot;)\n   319\t            local messageId = text:gsub(\&quot;|[cr].-\&quot;, \&quot;\&quot;):gsub(\&quot;[|:]\&quot;, \&quot;\&quot;):sub(1, 50)\n   320\t            local clickableTimestamp = \&quot;|Hwchat:copy:\&quot; .. messageId .. \&quot;|h|c\&quot; .. colorCode .. timestamp .. \&quot;|r|h \&quot;\n   321\t\n   322\t            text = clickableTimestamp .. text\n   323\t\n   324\t            -- 消息缓存管理\n   325\t            if not WChat.messageCache then WChat.messageCache, WChat.messageCacheOrder = {}, {} end\n   326\t            if not WChat.messageCache[messageId] then\n   327\t                if #WChat.messageCacheOrder &gt;= 100 then\n   328\t                    WChat.messageCache[table.remove(WChat.messageCacheOrder, 1)] = nil\n   329\t                end\n   330\t                WChat.messageCache[messageId] = text:gsub(\&quot;|H.-|h\&quot;, \&quot;\&quot;):gsub(\&quot;|[cr].-\&quot;, \&quot;\&quot;)\n   331\t                table.insert(WChat.messageCacheOrder, messageId)\n   332\t            end\n   333\t        end\n   334\t\n   335\t        return self.OriginalAddMessage(self, text, ...)\n   336\t    end,\n   337\t\n   338\t    Initialize = function(self)\n   339\t        local config = GetConfig()\n   340\t        self:Cleanup()\n   341\t        if config.EnableChannelShortNames or config.EnableTimestamp then\n   342\t            Utils.ProcessChatFrames(function(chatFrame, i)\n   343\t                if chatFrame.AddMessage then\n   344\t                    chatFrame.OriginalAddMessage = chatFrame.AddMessage\n   345\t                    chatFrame.AddMessage = self.ProcessMessage\n   346\t                end\n   347\t            end, true)\n   348\t        end\n   349\t    end,\n   350\t\nTotal lines in file: 2474\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;74ab5244-ed90-49c1-9408-ecacfd6540a8;toolu_vrtx_01WNMcH3QqS8Q1D3K9TwP5NM&quot;:{&quot;requestId&quot;:&quot;74ab5244-ed90-49c1-9408-ecacfd6540a8&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01WNMcH3QqS8Q1D3K9TwP5NM&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on 222.lua:\n   920\t\n   921\tfunction WChat:InitChatBar()\n   922\t    local config = GetConfig()\n   923\t    ChatBar:SetFrameLevel(0)\n   924\t\n   925\t    -- 清理现有按钮\n   926\t    Utils.ProcessChannels({ChatBar:GetChildren()}, function(child)\n   927\t        if child.buttonName then child:Hide(); child:SetParent(nil) end\n   928\t    end)\n   929\t    WChat.emoteButtonFrame, BGButtonFrame, WChat.BGButtonFrame, ChatBar.MinimizeBtn = nil, nil, nil, nil\n   930\t\n   931\t    local buttonSize, padding, isVertical = config.ButtonSize or 22, 8, config.UseVertical\n   932\t    local width = isVertical and (buttonSize + padding) or (#ChannelButtons * config.DistanceHorizontal + 10)\n   933\t    local height = isVertical and (#ChannelButtons * config.DistanceVertical + 10) or (buttonSize + padding)\n   934\t    ChatBar:SetSize(width, height)\n   935\t\n   936\t    if not _G.ElvUI then\n   937\t        DEFAULT_CHAT_FRAME.editBox:ClearAllPoints()\n   938\t        if config.UseTopInput then\n   939\t            DEFAULT_CHAT_FRAME.editBox:SetPoint(\&quot;BOTTOMLEFT\&quot;, DEFAULT_CHAT_FRAME, \&quot;TOPLEFT\&quot;, 0, 20)\n   940\t            DEFAULT_CHAT_FRAME.editBox:SetPoint(\&quot;BOTTOMRIGHT\&quot;, DEFAULT_CHAT_FRAME, \&quot;TOPRIGHT\&quot;, 0, 20)\n   941\t        else\n   942\t            DEFAULT_CHAT_FRAME.editBox:SetPoint(\&quot;TOPLEFT\&quot;, DEFAULT_CHAT_FRAME, \&quot;BOTTOMLEFT\&quot;, 0, -5)\n   943\t            DEFAULT_CHAT_FRAME.editBox:SetPoint(\&quot;TOPRIGHT\&quot;, DEFAULT_CHAT_FRAME, \&quot;BOTTOMRIGHT\&quot;, 0, -5)\n   944\t        end\n   945\t    end\n   946\t\n   947\t    ChatBar:ClearAllPoints()\n   948\t    if config.Position and config.Position.point then\n   949\t        local pos = config.Position\n   950\t        ChatBar:SetPoint(pos.point, pos.relativeTo or UIParent, pos.relativePoint, pos.xOfs, pos.yOfs)\n   951\t    else\n   952\t        ChatBar:SetPoint(\&quot;TOPLEFT\&quot;, DEFAULT_CHAT_FRAME, \&quot;TOPLEFT\&quot;, 0, 90)\n   953\t    end\n   954\t\n   955\t    SetupChatBarDragging(ChatBar, not config.LockChatBar)\n   956\t    ChatBarUtils.setBackdrop(not config.LockChatBar)\n   957\t\n   958\t    for i = 1, #ChannelButtons do CreateChannelButton(ChannelButtons[i], i, config) end\n   959\t    -- 移除频道组，但不自动添加任何频道，让用户自己决定\n   960\t    ChatFrame_RemoveMessageGroup(DEFAULT_CHAT_FRAME, \&quot;CHANNEL\&quot;)\n   961\t\n   962\t    -- 注册事件\n   963\t    ChatBar:UnregisterAllEvents()\n   964\t    Utils.RegisterEvents(ChatBar, {\&quot;PLAYER_ENTERING_WORLD\&quot;, \&quot;ZONE_CHANGED_NEW_AREA\&quot;, \&quot;UPDATE_CHAT_WINDOWS\&quot;, \&quot;CHAT_MSG_WHISPER\&quot;, \&quot;CHAT_MSG_BN_WHISPER\&quot;})\n   965\t\n   966\t    ChatBar:SetScript(\&quot;OnEvent\&quot;, function(self, event)\n   967\t        if event == \&quot;PLAYER_ENTERING_WORLD\&quot; or event == \&quot;ZONE_CHANGED_NEW_AREA\&quot; then\n   968\t            UpdateBGButtonText()\n   969\t            -- 确保频道默认显示，但尊重用户的屏蔽设置\n   970\t            C_Timer.After(1, function()\n   971\t                Utils.ProcessDefaultChannels(true)  -- 完整版本，考虑屏蔽设置\n   972\t            end)\n   973\t        elseif event == \&quot;UPDATE_CHAT_WINDOWS\&quot; then\n   974\t            UpdateChannelXIcons()\n   975\t        elseif event == \&quot;CHAT_MSG_WHISPER\&quot; or event == \&quot;CHAT_MSG_BN_WHISPER\&quot; then\n   976\t            C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, UpdateWhisperButtonFlash)\n   977\t        end\n   978\t    end)\n   979\t\n   980\t    C_Timer.After(1, function() UpdateBGButtonText(); UpdateMinimizeButtonText(); UpdateChannelXIcons(); UpdateWhisperButtonFlash() end)\n   981\t    if not WChat.chatBarInitialized then WChat.chatBarInitialized = true end\n   982\tend\n   983\t\n   984\t-- 频道快速切换\n   985\tfunction ChatEdit_CustomTabPressed(...)\n   986\t    return ChatEdit_CustomTabPressed_Inner(...)\n   987\tend\n   988\t\n   989\tlocal cycles = {\n   990\t        -- \&quot;说\&quot;\n   991\t        {\n   992\t            chatType = \&quot;SAY\&quot;,\n   993\t            use = function(self, editbox)\n   994\t                return 1\n   995\t            end\n   996\t        },\n   997\t        --大喊\n   998\t        {\n   999\t            chatType = \&quot;YELL\&quot;,\n  1000\t            use = function(self, editbox)\n  1001\t                return 1\n  1002\t            end\n  1003\t        },\n  1004\t        --小队\n  1005\t        {\n  1006\t            chatType = \&quot;PARTY\&quot;,\n  1007\t            use = function(self, editbox)\n  1008\t                return IsInGroup()\n  1009\t            end\n  1010\t        },\n  1011\t        --团队\n  1012\t        {\n  1013\t            chatType = \&quot;RAID\&quot;,\n  1014\t            use = function(self, editbox)\n  1015\t                return IsInRaid()\n  1016\t            end\n  1017\t        },\n  1018\t        --实时聊天\n  1019\t        {\n  1020\t            chatType = \&quot;INSTANCE_CHAT\&quot;,\nTotal lines in file: 2474\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;71f8d698-b693-4ba0-91e6-2f6c9be95ec6;toolu_vrtx_01Qxonnt7jWuJxHqpRUhSmv1&quot;:{&quot;requestId&quot;:&quot;71f8d698-b693-4ba0-91e6-2f6c9be95ec6&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01Qxonnt7jWuJxHqpRUhSmv1&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on 222.lua:\n   850\t    if data.name == \&quot;meetinghorn\&quot; and type(tooltip) == \&quot;function\&quot; then\n   851\t        tooltip = tooltip()\n   852\t    end\n   853\t    Utils.SetButtonHoverEvents(frame, 0.5, GetConfig().AlphaOnLeave, tooltip)\n   854\t\n   855\t    -- 创建按钮文字\n   856\t    frame.text = frame:CreateFontString(nil, \&quot;ARTWORK\&quot;)\n   857\t    local fontSize = math.max(10, math.min(20, buttonSize * 0.68))\n   858\t    frame.text:SetFont(STANDARD_TEXT_FONT, fontSize, \&quot;OUTLINE\&quot;)\n   859\t    frame.text:SetPoint(\&quot;CENTER\&quot;, 0, 0)  -- 所有按钮统一居中对齐\n   860\t\n   861\t    -- 检查是否是需要特殊处理的纹理按钮\n   862\t    if (data.name == \&quot;meetinghorn\&quot; or data.name == \&quot;atlas\&quot;) and not Utils.HasMarkup(data.text, \&quot;|T\&quot;) then\n   863\t        -- 直接使用纹理路径\n   864\t        local texturePath = data.text\n   865\t        -- 创建纹理对象\n   866\t        frame.texture = frame:CreateTexture(nil, \&quot;ARTWORK\&quot;)\n   867\t        frame.texture:SetTexture(texturePath)\n   868\t        -- 使用与按钮相同的尺寸，确保图标足够大\n   869\t        frame.texture:SetSize(buttonSize, buttonSize)\n   870\t        frame.texture:SetPoint(\&quot;CENTER\&quot;, 0, 0)\n   871\t        -- 根据按钮类型设置颜色\n   872\t        if data.name == \&quot;meetinghorn\&quot; then\n   873\t            frame.texture:SetVertexColor(0.00, 1.00, 1.00)  -- 青色\n   874\t        elseif data.name == \&quot;atlas\&quot; then\n   875\t            frame.texture:SetVertexColor(0.20, 0.80, 0.20)  -- 绿色\n   876\t        end\n   877\t        -- 隐藏文本\n   878\t        frame.text:SetText(\&quot;\&quot;)\n   879\t    else\n   880\t        frame.text:SetText(data.text)\n   881\t        -- 只有当color存在且text不包含颜色标记时才设置颜色\n   882\t        if data.color and not Utils.HasMarkup(data.text, \&quot;|c\&quot;) then\n   883\t            frame.text:SetTextColor(unpack(data.color))\n   884\t        end\n   885\t    end\n   886\t\n   887\t    -- 创建频道屏蔽X图标\n   888\t    if CHANNEL_CONFIG.MAPPINGS[data.name] then\n   889\t        frame.X = frame:CreateTexture(nil, \&quot;OVERLAY\&quot;)\n   890\t        frame.X:SetTexture(\&quot;interface/common/voicechat-muted.blp\&quot;)\n   891\t        frame.X:SetSize(math.max(12, buttonSize * 0.6), math.max(12, buttonSize * 0.6))\n   892\t        frame.X:SetAlpha(0.7)\n   893\t        frame.X:SetPoint(\&quot;CENTER\&quot;)\n   894\t        frame.X:SetDrawLayer(\&quot;OVERLAY\&quot;, 7)\n   895\t        frame.X:Hide()\n   896\t    end\n   897\t\n   898\t    frame.buttonName = data.name\n   899\t\n   900\t    local isVertical = config.UseVertical\n   901\t    local point, x, y = isVertical and \&quot;TOP\&quot; or \&quot;LEFT\&quot;,\n   902\t        isVertical and 0 or (10 + (index - 1) * config.DistanceHorizontal),\n   903\t        isVertical and ((1 - index) * config.DistanceVertical) or 0\n   904\t    frame:SetPoint(point, ChatBar, point, x, y)\n   905\t\n   906\t    if data.name == \&quot;bg\&quot; then\n   907\t        BGButtonFrame = frame\n   908\t        WChat.BGButtonFrame = frame\n   909\t        UpdateBGButtonText()\n   910\t    elseif data.name == \&quot;minimize\&quot; then\n   911\t        ChatBar.MinimizeBtn = frame\n   912\t        C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, UpdateMinimizeButtonText)\n   913\t    elseif data.name == \&quot;whisper\&quot; then\n   914\t        whisperButtonFrame = frame\n   915\t        WChat.whisperButtonFrame = frame\n   916\t    elseif data.name == \&quot;emote\&quot; then\n   917\t        WChat.emoteButtonFrame = frame\n   918\t    end\n   919\tend\n   920\t\nTotal lines in file: 2474\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;fa2283e3-2bf0-46a3-9155-3a3f3a3b779d;toolu_vrtx_01858uiCT9mzJvyaGa1HwbAe&quot;:{&quot;requestId&quot;:&quot;fa2283e3-2bf0-46a3-9155-3a3f3a3b779d&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01858uiCT9mzJvyaGa1HwbAe&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on 222.lua:\n  2400\t    preferredIndex = 3,\n  2401\t}\n  2402\t\n  2403\t\n  2404\t\n  2405\t-- 主要接口函数\n  2406\tfunction WhisperRecord.ToggleWhisperRecordFrame()\n  2407\t    local WanMY = WhisperRecord.WanMY or WhisperRecord.CreateWhisperRecordFrame()\n  2408\t    if WanMY:IsShown() then WanMY:Hide() else WanMY:Show(); WhisperRecord.UpdateWhisperList() end\n  2409\tend\n  2410\t\n  2411\tfunction WhisperRecord.ShowWhisperRecordFrame()\n  2412\t    local WanMY = WhisperRecord.WanMY or WhisperRecord.CreateWhisperRecordFrame()\n  2413\t    WanMY:Show()\n  2414\t    WhisperRecord.UpdateWhisperList()\n  2415\tend\n  2416\t\n  2417\tfunction WhisperRecord.HideWhisperRecordFrame()\n  2418\t    local WanMY = WhisperRecord.WanMY\n  2419\t    if WanMY then WanMY:Hide(); WhisperRecord.ClearSelectedState() end\n  2420\tend\n  2421\t\n  2422\t-- 注册时间戳点击处理器\n  2423\tlocal function RegisterTimestampClickHandler()\n  2424\t    -- 注册自定义超链接处理器\n  2425\t    local originalSetItemRef = SetItemRef\n  2426\t    SetItemRef = function(link, text, button, chatFrame)\n  2427\t        if link and link:match(\&quot;^wchat:copy:\&quot;) then\n  2428\t            local messageId = link:match(\&quot;^wchat:copy:(.+)$\&quot;)\n  2429\t            if messageId and WChat.CopySingleMessage then\n  2430\t                WChat:CopySingleMessage(messageId)\n  2431\t                return\n  2432\t            end\n  2433\t        end\n  2434\t        -- 调用原始函数处理其他链接\n  2435\t        return originalSetItemRef(link, text, button, chatFrame)\n  2436\t    end\n  2437\tend\n  2438\t\n  2439\t-- 初始化时间戳点击功能\n  2440\tC_Timer.After(1, function()\n  2441\t    RegisterTimestampClickHandler()\n  2442\tend)\n  2443\t\n  2444\t-- 时间戳颜色选择器（使用WoW标准颜色选择器）\n  2445\tfunction WChat:ShowTimestampColorPicker()\n  2446\t    local config = GetConfig()\n  2447\t    local currentColor = config.TimestampColor or {r = 255, g = 20, b = 147}\n  2448\t\n  2449\t    -- 转换为0-1范围的颜色值\n  2450\t    local r, g, b = currentColor.r / 255, currentColor.g / 255, currentColor.b / 255\n  2451\t\n  2452\t    -- 使用WoW标准颜色选择器\n  2453\t    ColorPickerFrame:SetColorRGB(r, g, b)\n  2454\t    ColorPickerFrame.hasOpacity = false\n  2455\t    ColorPickerFrame.previousValues = {r, g, b}\n  2456\t\n  2457\t    -- 颜色更新函数\n  2458\t    local function updateColor(r, g, b)\n  2459\t        config.TimestampColor = {r = math.floor(r * 255 + 0.5), g = math.floor(g * 255 + 0.5), b = math.floor(b * 255 + 0.5)}\n  2460\t        if WChat.OnConfigChanged and WChat.OnConfigChanged.TimestampColor then\n  2461\t            WChat.OnConfigChanged.TimestampColor(config.TimestampColor)\n  2462\t        end\n  2463\t    end\n  2464\t\n  2465\t    -- 设置颜色选择器回调\n  2466\t    ColorPickerFrame.func = function() updateColor(ColorPickerFrame:GetColorRGB()) end\n  2467\t    ColorPickerFrame.swatchFunc = ColorPickerFrame.func\n  2468\t    ColorPickerFrame.cancelFunc = function(prev) if prev then updateColor(prev[1], prev[2], prev[3]) end end\n  2469\t\n  2470\t    -- 显示颜色选择器\n  2471\t    ColorPickerFrame:Show()\n  2472\tend\n  2473\t\n  2474\t\nTotal lines in file: 2474\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;aa16b97b-d514-4a5e-817b-44e670ae3af9;toolu_vrtx_01YAtyiy6aKnyifv6cwG7Uju&quot;:{&quot;requestId&quot;:&quot;aa16b97b-d514-4a5e-817b-44e670ae3af9&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01YAtyiy6aKnyifv6cwG7Uju&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on 222.lua:\n  2300\t                        playerData.hasUnread = false\n  2301\t                        PostDBOperation(false, true)  -- 不需要清理缓存，需要UI更新\n  2302\t                        -- 延迟更新列表，避免频繁刷新\n  2303\t                        C_Timer.After(0.05, function()\n  2304\t                            if WhisperRecord.WanMY and WhisperRecord.WanMY:IsShown() then\n  2305\t                                WhisperRecord.UpdateWhisperList()\n  2306\t                            end\n  2307\t                        end)\n  2308\t                    end\n  2309\t                end\n  2310\t            end)\n  2311\t\n  2312\t            listItem:SetScript(\&quot;OnLeave\&quot;, function(self)\n  2313\t                local WanMY = WhisperRecord.WanMY\n  2314\t                if WanMY and WanMY.chatContent:IsShown() then\n  2315\t                    if WanMY.chatContent.hideTimer then WanMY.chatContent.hideTimer:Cancel() end\n  2316\t                    WanMY.chatContent.hideTimer = C_Timer.NewTimer(0.15, function()\n  2317\t                        if WanMY.chatContent:IsShown() and not WanMY.chatContent:IsMouseOver() then\n  2318\t                            WanMY.chatContent:Hide()\n  2319\t                            WhisperRecord.ClearSelectedState()\n  2320\t                        end\n  2321\t                    end)\n  2322\t                end\n  2323\t            end)\n  2324\t\n  2325\t            listItem:SetScript(\&quot;OnClick\&quot;, function(self)\n  2326\t                if self.playerName then\n  2327\t                    local name = Utils.GetDisplayName(self.playerName, true)\n  2328\t                    if name then\n  2329\t                        local playerData = GetPlayerData(self.playerName)\n  2330\t                        if playerData and playerData.class == CONFIG.BNET_CLASS_ID then\n  2331\t                            local displayName = Utils.GetDisplayName(self.playerName)\n  2332\t                            ChatFrame_SendBNetTell(displayName)\n  2333\t                        else\n  2334\t                            ChatFrame_SendTell(name)\n  2335\t                        end\n  2336\t                    end\n  2337\t                end\n  2338\t            end)\n  2339\t\n  2340\t            listItem:SetScript(\&quot;OnMouseUp\&quot;, function(self, button)\n  2341\t                if not self.playerName then return end\n  2342\t                if button == \&quot;RightButton\&quot; then\n  2343\t                    local name = Utils.GetDisplayName(self.playerName, true)\n  2344\t                    if name and FriendsFrame_ShowDropdown then\n  2345\t                        FriendsFrame_ShowDropdown(name, 1)\n  2346\t                    end\n  2347\t                end\n  2348\t            end)\n  2349\t\n  2350\t            listItem:Show()\n  2351\t        end\n  2352\t    end\n  2353\tend\n  2354\t\n  2355\t\n  2356\t\n  2357\t-- 静态弹窗对话框\n  2358\tStaticPopupDialogs[\&quot;CHONGZHI_MIYUJILU\&quot;] = {\n  2359\t    text = \&quot;确定要清空所有的密语记录吗？\&quot;,\n  2360\t    button1 = \&quot;确定\&quot;,\n  2361\t    button2 = \&quot;取消\&quot;,\n  2362\t    OnAccept = function()\n  2363\t        GetWhisperGlobalDB().record = {{}, {}}\n  2364\t        PostDBOperation(true, true)  -- 需要清理缓存和UI更新\n  2365\t        -- 延迟更新UI，避免阻塞\n  2366\t        C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, function()\n  2367\t            if WhisperRecord.WanMY and WhisperRecord.WanMY:IsShown() then\n  2368\t                WhisperRecord.UpdateWhisperList()\n  2369\t            end\n  2370\t        end)\n  2371\t    end,\n  2372\t    timeout = 0,\n  2373\t    whileDead = true,\n  2374\t    hideOnEscape = true,\n  2375\t    preferredIndex = 3,\n  2376\t}\n  2377\t\n  2378\tStaticPopupDialogs[\&quot;DELETE_WHISPER_RECORD\&quot;] = {\n  2379\t    text = \&quot;确定要删除与 %s 的所有密语记录吗？\&quot;,\n  2380\t    button1 = \&quot;确定\&quot;,\n  2381\t    button2 = \&quot;取消\&quot;,\n  2382\t    OnAccept = function(self, playerName)\n  2383\t        if playerName then\n  2384\t            local playerData = GetPlayerData(playerName)\n  2385\t            if playerData then\n  2386\t                GetWhisperGlobalDB().record[1][playerName] = nil\n  2387\t                PostDBOperation(true, true)  -- 需要清理缓存和UI更新\n  2388\t                -- 延迟更新UI，避免阻塞\n  2389\t                C_Timer.After(CONFIG.TIME.UI_UPDATE_DELAY, function()\n  2390\t                    if WhisperRecord.WanMY and WhisperRecord.WanMY:IsShown() then\n  2391\t                        WhisperRecord.UpdateWhisperList()\n  2392\t                    end\n  2393\t                end)\n  2394\t            end\n  2395\t        end\n  2396\t    end,\n  2397\t    timeout = 0,\n  2398\t    whileDead = true,\n  2399\t    hideOnEscape = true,\n  2400\t    preferredIndex = 3,\nTotal lines in file: 2474\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;46b3f473-4f15-4903-a6fd-c62017836434;toolu_vrtx_01H6rg61YKmzx3vLaYdfzLLX&quot;:{&quot;requestId&quot;:&quot;46b3f473-4f15-4903-a6fd-c62017836434&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01H6rg61YKmzx3vLaYdfzLLX&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Regex search results for pattern: OnConfigChanged in 222.lua\nFound 4 matching lines:\n\n    244\t        end\n    245\t    end))\n    246\tend\n    247\t\n    248\t-- 配置变化处理（统一简化版）\n&gt;   249\tWChat.OnConfigChanged = {}\n    250\tlocal function HandleConfigChange(key, value, customHandler)\n    251\t    GetConfig()[key] = value\n    252\t    if customHandler then\n    253\t        customHandler(value)\n    254\t    elseif WChat.InitChatBar then\n...\n    367\tfor _, group in ipairs(configGroups) do\n    368\t    Utils.ProcessChannels(group[1], function(key) configHandlers[key] = group[2] end)\n    369\tend\n    370\t\n    371\tfor key, handler in pairs(configHandlers) do\n&gt;   372\t    WChat.OnConfigChanged[key] = function(value) HandleConfigChange(key, value, handler) end\n    373\tend\n    374\t\n    375\t-- WChat模块清理函数（简化版）\n    376\tfunction WChat:Cleanup()\n    377\t    -- 清理定时器\n...\n   2455\t    ColorPickerFrame.previousValues = {r, g, b}\n   2456\t\n   2457\t    -- 颜色更新函数\n   2458\t    local function updateColor(r, g, b)\n   2459\t        config.TimestampColor = {r = math.floor(r * 255 + 0.5), g = math.floor(g * 255 + 0.5), b = math.floor(b * 255 + 0.5)}\n&gt;  2460\t        if WChat.OnConfigChanged and WChat.OnConfigChanged.TimestampColor then\n   2461\t            WChat.OnConfigChanged.TimestampColor(config.TimestampColor)\n   2462\t        end\n   2463\t    end\n   2464\t\n   2465\t    -- 设置颜色选择器回调\n   2466\t    ColorPickerFrame.func = function() updateColor(ColorPickerFrame:GetColorRGB()) end\n\nTotal matches: 4\nTotal lines in file: 2474&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;d7239aff-3b72-4e30-9797-10fc341e88a2;toolu_vrtx_01A4M7u6gMQdoDbpNobZ65yJ&quot;:{&quot;requestId&quot;:&quot;d7239aff-3b72-4e30-9797-10fc341e88a2&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01A4M7u6gMQdoDbpNobZ65yJ&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Successfully edited the file 222.lua.\nResult for str_replace for entry with index [0]:\nReplacement successful.\nnew_str starts at line 259 and ends at line 279.\n\nReview the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).\nEdit the file again if necessary.\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;80864def-47bd-422f-bae7-3c05c93706ac;toolu_vrtx_01JukCxKggeDfkqhWJy3BgW3&quot;:{&quot;requestId&quot;:&quot;80864def-47bd-422f-bae7-3c05c93706ac&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01JukCxKggeDfkqhWJy3BgW3&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on Moudel/WChat.lua:\n     1\t-------------------------------WChat 聊天增强模块-------------------------------\n     2\t-- 独立聊天增强模块，移除ACE3依赖\n     3\t-- 作者: VGGFAC\n     4\t-- 版本: 2.0.0\n     5\t\n     6\tlocal addonName, addon = ...\n     7\t\n     8\t-- 模块配置\n     9\tlocal WChat = {}\n    10\tlocal WChat_Config = {\n    11\t    -- 默认配置\n    12\t    ShortChannel = true,        -- 频道标签精简\n    13\t    EnableEmoteInput = true,    -- 启用表情输入\n    14\t    EmoteIconSize = 16,         -- 表情图标大小\n    15\t    EmoteIconListSize = 20,     -- 表情列表图标大小\n    16\t    EmoteOffsetX = 0,           -- 表情面板X偏移\n    17\t    EmoteOffsetY = 30,          -- 表情面板Y偏移\n    18\t    UseVertical = false,        -- 聊天条垂直布局\n    19\t    UseTopChatbar = false,      -- 聊天条在上方\n    20\t    UseTopInput = false,        -- 输入框在上方\n    21\t    ChatBarOffsetX = 0,         -- 聊天条X偏移\n    22\t    ChatBarOffsetY = 0,         -- 聊天条Y偏移\n    23\t    DistanceVertical = -25,     -- 垂直间距\n    24\t    DistanceHorizontal = 25,    -- 水平间距\n    25\t    AlphaOnEnter = 1.0,         -- 鼠标悬停透明度\n    26\t    AlphaOnLeave = 0.7,         -- 鼠标离开透明度\n    27\t    Position = nil,             -- 聊天条位置\n    28\t    EnableTimestampCopy = true, -- 启用时间戳点击复制\n    29\t    TimestampFormat = \&quot;%H:%M:%S\&quot;, -- 时间戳格式\n    30\t    TimestampColor = { 1.0, 1.0, 1.0 }, -- 时间戳颜色\n    31\t    -- 插件按钮显示控制\n    32\t    ShowBiaoGeButton = true,    -- 显示BiaoGe按钮\n    33\t    ShowBiaoGeAIButton = true,  -- 显示BiaoGeAI按钮\n    34\t    ShowAtlasButton = true,     -- 显示Atlas按钮\n    35\t    ShowMeetingHornButton = true, -- 显示MeetingHorn按钮\n    36\t    -- 频道隐藏设置\n    37\t    HiddenChannels = {}\n    38\t}\n    39\t\n    40\t-- 频道配置映射\n    41\tlocal CHANNEL_CONFIG = {\n    42\t    MAPPINGS = {\n    43\t        chnGen = \&quot;综合\&quot;,\n    44\t        chnTrade = \&quot;交易\&quot;,\n    45\t        chnLFG = \&quot;寻求组队\&quot;,\n    46\t        world = \&quot;大脚世界频道\&quot;\n    47\t    }\n    48\t}\n    49\t\n    50\t-- 频道显示/隐藏管理函数\n    51\tlocal function IsChannelShown(channelName)\n    52\t    local channels = {GetChatWindowChannels(DEFAULT_CHAT_FRAME:GetID() or 1)}\n    53\t    for i = 1, #channels, 2 do\n    54\t        if channels[i] == channelName then return true end\n    55\t    end\n    56\t    return false\n    57\tend\n    58\t\n    59\tlocal function ToggleChannelShowHide(channelName)\n    60\t    local isShown = IsChannelShown(channelName)\n    61\t    if isShown then\n    62\t        ChatFrame_RemoveChannel(DEFAULT_CHAT_FRAME, channelName)\n    63\t        WChat_Config.HiddenChannels[channelName] = true\n    64\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 已隐藏频道: \&quot; .. channelName)\n    65\t    else\n    66\t        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)\n    67\t        WChat_Config.HiddenChannels[channelName] = nil\n    68\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 已显示频道: \&quot; .. channelName)\n    69\t    end\n    70\tend\n    71\t\n    72\tlocal function UpdateChannelXIcons()\n    73\t    if not WChatBar then return end\n    74\t\n    75\t    for _, child in pairs({WChatBar:GetChildren()}) do\n    76\t        if child.X and child.buttonName and CHANNEL_CONFIG.MAPPINGS[child.buttonName] then\n    77\t            local channelName = CHANNEL_CONFIG.MAPPINGS[child.buttonName]\n    78\t            child.X:SetShown(not IsChannelShown(channelName))\n    79\t        end\n    80\t    end\n    81\tend\n    82\t\n    83\t-- 获取语言\n    84\tlocal Language = GetLocale()\n    85\tif (Language == \&quot;zhTW\&quot;) then\n    86\t    --公会\n    87\t    CHAT_GUILD_GET = \&quot;|Hchannel:GUILD|h[公會]|h %s: \&quot;\n    88\t    CHAT_OFFICER_GET = \&quot;|Hchannel:OFFICER|h[官員]|h %s: \&quot;\n    89\t    \n    90\t    --团队\n    91\t    CHAT_RAID_GET = \&quot;|Hchannel:RAID|h[團隊]|h %s: \&quot;\n    92\t    CHAT_RAID_WARNING_GET = \&quot;[通知] %s: \&quot;\n    93\t    CHAT_RAID_LEADER_GET = \&quot;|Hchannel:RAID|h[團長]|h %s: \&quot;\n    94\t    \n    95\t    --队伍\n    96\t    CHAT_PARTY_GET = \&quot;|Hchannel:PARTY|h[隊伍]|h %s: \&quot;\n    97\t    CHAT_PARTY_LEADER_GET = \&quot;|Hchannel:PARTY|h[隊長]|h %s: \&quot;\n    98\t    CHAT_PARTY_GUIDE_GET = \&quot;|Hchannel:PARTY|h[向導]|h %s: \&quot;\n    99\t    \n   100\t    --战场\n   101\t    CHAT_BATTLEGROUND_GET = \&quot;|Hchannel:BATTLEGROUND|h[戰場]|h %s: \&quot;\n   102\t    CHAT_BATTLEGROUND_LEADER_GET = \&quot;|Hchannel:BATTLEGROUND|h[領袖]|h %s: \&quot;\n   103\t    \n   104\t    --说 / 喊\n   105\t    CHAT_SAY_GET = \&quot;%s: \&quot;\n   106\t    CHAT_YELL_GET = \&quot;%s: \&quot;\n   107\t    \n   108\t    --密语\n   109\t    CHAT_WHISPER_INFORM_GET = \&quot;發送給%s: \&quot;\n   110\t    CHAT_WHISPER_GET = \&quot;%s悄悄話: \&quot;\n   111\t    \n   112\t    --flags\n   113\t    CHAT_FLAG_AFK = \&quot;[暫離] \&quot;\n   114\t    CHAT_FLAG_DND = \&quot;[勿擾] \&quot;\n   115\t    CHAT_FLAG_GM = \&quot;[GM] \&quot;\n   116\telseif (Language == \&quot;zhCN\&quot;) then\n   117\t    --公会\n   118\t    CHAT_GUILD_GET = \&quot;|Hchannel:GUILD|h[公会]|h %s: \&quot;\n   119\t    CHAT_OFFICER_GET = \&quot;|Hchannel:OFFICER|h[官员]|h %s: \&quot;\n   120\t    \n   121\t    --团队\n   122\t    CHAT_RAID_GET = \&quot;|Hchannel:RAID|h[团队]|h %s: \&quot;\n   123\t    CHAT_RAID_WARNING_GET = \&quot;[通知] %s: \&quot;\n   124\t    CHAT_RAID_LEADER_GET = \&quot;|Hchannel:RAID|h[团长]|h %s: \&quot;\n   125\t    \n   126\t    --队伍\n   127\t    CHAT_PARTY_GET = \&quot;|Hchannel:PARTY|h[队伍]|h %s: \&quot;\n   128\t    CHAT_PARTY_LEADER_GET = \&quot;|Hchannel:PARTY|h[队长]|h %s: \&quot;\n   129\t    CHAT_PARTY_GUIDE_GET = \&quot;|Hchannel:PARTY|h[向导]:|h %s: \&quot;\n   130\t    \n   131\t    --战场\n   132\t    CHAT_BATTLEGROUND_GET = \&quot;|Hchannel:BATTLEGROUND|h[副本]|h %s: \&quot;\n   133\t    CHAT_BATTLEGROUND_LEADER_GET = \&quot;|Hchannel:BATTLEGROUND|h[领袖]|h %s: \&quot;\n   134\t    \n   135\t    --密语\n   136\t    CHAT_WHISPER_INFORM_GET = \&quot;发送给%s: \&quot;\n   137\t    CHAT_WHISPER_GET = \&quot;%s悄悄的说: \&quot;\n   138\t    CHAT_BN_WHISPER_INFORM_GET = \&quot;发送给%s: \&quot;\n   139\t    CHAT_BN_WHISPER_GET = \&quot;悄悄的说%s: \&quot;\n   140\t    \n   141\t    --说 / 喊\n   142\t    CHAT_SAY_GET = \&quot;%s: \&quot;\n   143\t    CHAT_YELL_GET = \&quot;%s: \&quot;\n   144\t    \n   145\t    --flags\n   146\t    CHAT_FLAG_AFK = \&quot;[暂离] \&quot;\n   147\t    CHAT_FLAG_DND = \&quot;[勿扰] \&quot;\n   148\t    CHAT_FLAG_GM = \&quot;[GM] \&quot;\n   149\telse\n   150\t    CHAT_GUILD_GET = \&quot;|Hchannel:GUILD|hG|h %s \&quot;\n   151\t    CHAT_OFFICER_GET = \&quot;|Hchannel:OFFICER|hO|h %s \&quot;\n   152\t    CHAT_RAID_GET = \&quot;|Hchannel:RAID|hR|h %s \&quot;\n   153\t    CHAT_RAID_WARNING_GET = \&quot;RW %s \&quot;\n   154\t    CHAT_RAID_LEADER_GET = \&quot;|Hchannel:RAID|hRL|h %s \&quot;\n   155\t    CHAT_PARTY_GET = \&quot;|Hchannel:PARTY|hP|h %s \&quot;\n   156\t    CHAT_PARTY_LEADER_GET = \&quot;|Hchannel:PARTY|hPL|h %s \&quot;\n   157\t    CHAT_PARTY_GUIDE_GET = \&quot;|Hchannel:PARTY|hPG|h %s \&quot;\n   158\t    CHAT_BATTLEGROUND_GET = \&quot;|Hchannel:BATTLEGROUND|hB|h %s \&quot;\n   159\t    CHAT_BATTLEGROUND_LEADER_GET = \&quot;|Hchannel:BATTLEGROUND|hBL|h %s \&quot;\n   160\t    CHAT_WHISPER_INFORM_GET = \&quot;to %s \&quot;\n   161\t    CHAT_WHISPER_GET = \&quot;from %s \&quot;\n   162\t    CHAT_BN_WHISPER_INFORM_GET = \&quot;to %s \&quot;\n   163\t    CHAT_BN_WHISPER_GET = \&quot;from %s \&quot;\n   164\t    CHAT_SAY_GET = \&quot;%s \&quot;\n   165\t    CHAT_YELL_GET = \&quot;%s \&quot;\n   166\t    CHAT_FLAG_AFK = \&quot;[AFK] \&quot;\n   167\t    CHAT_FLAG_DND = \&quot;[DND] \&quot;\n   168\t    CHAT_FLAG_GM = \&quot;[GM] \&quot;\n   169\tend\n   170\t\n   171\t--================================公共频道和自定义频道精简================================--\n   172\tlocal gsub = _G.string.gsub\n   173\tlocal newAddMsg = {}\n   174\tlocal chn = {\n   175\t    \&quot;%[%d+%. General.-%]\&quot;,\n   176\t    \&quot;%[%d+%. Trade.-%]\&quot;,\n   177\t    \&quot;%[%d+%. LocalDefense.-%]\&quot;,\n   178\t    \&quot;%[%d+%. LookingForGroup%]\&quot;,\n   179\t    \&quot;%[%d+%. WorldDefense%]\&quot;,\n   180\t    \&quot;%[%d+%. GuildRecruitment.-%]\&quot;,\n   181\t    \&quot;%[%d+%. BigFootChannel.-%]\&quot;,\n   182\t    \&quot;%[%d+%. CustomChannel.-%]\&quot; -- 自定义频道英文名随便填写\n   183\t}\n   184\t\n   185\tlocal rplc = {\n   186\t    \&quot;[GEN]\&quot;,\n   187\t    \&quot;[TR]\&quot;,\n   188\t    \&quot;[WD]\&quot;,\n   189\t    \&quot;[LD]\&quot;,\n   190\t    \&quot;[LFG]\&quot;,\n   191\t    \&quot;[GR]\&quot;,\n   192\t    \&quot;[BFC]\&quot;,\n   193\t    \&quot;[CL]\&quot; -- 英文缩写\n   194\t}\n   195\t\n   196\tif (Language == \&quot;zhCN\&quot;) then ---国服\n   197\t    rplc[1] = \&quot;[%1综]\&quot;\n   198\t    rplc[2] = \&quot;[%1交]\&quot;\n   199\t    rplc[3] = \&quot;[%1防]\&quot;\n   200\t    rplc[4] = \&quot;[%1组]\&quot;\n   201\t    rplc[5] = \&quot;[%1守]\&quot;\n   202\t    rplc[6] = \&quot;[%1招]\&quot;\n   203\t    rplc[7] = \&quot;[%1世]\&quot;\n   204\t    rplc[8] = \&quot;[%1自定义]\&quot; -- 自定义频道缩写请自行修改\n   205\telseif (Language == \&quot;zhTW\&quot;) then ---台服\n   206\t    rplc[1] = \&quot;[%1綜合]\&quot;\n   207\t    rplc[2] = \&quot;[%1貿易]\&quot;\n   208\t    rplc[3] = \&quot;[%1防務]\&quot;\n   209\t    rplc[4] = \&quot;[%1組隊]\&quot;\n   210\t    rplc[5] = \&quot;[%1守備]\&quot;\n   211\t    rplc[6] = \&quot;[%1招募]\&quot;\n   212\t    rplc[7] = \&quot;[%1世界]\&quot;\n   213\t    rplc[8] = \&quot;[%1自定义]\&quot; -- 自定义频道缩写请自行修改\n   214\tend\n   215\t\n   216\tif Language == \&quot;zhCN\&quot; then\n   217\t    ---------------------------------------- 国服简体中文 ---------------------------------------------\n   218\t    chn[1] = \&quot;%[%d+%. 综合.-%]\&quot;\n   219\t    chn[2] = \&quot;%[%d+%. 交易.-%]\&quot;\n   220\t    chn[3] = \&quot;%[%d+%. 本地防务.-%]\&quot;\n   221\t    chn[4] = \&quot;%[%d+%. 寻求组队%]\&quot;\n   222\t    chn[5] = \&quot;%[%d+%. 世界防务%]\&quot;\n   223\t    chn[6] = \&quot;%[%d+%. 公会招募.-%]\&quot;\n   224\t    chn[7] = \&quot;%[%d+%. 大脚世界频道.-%]\&quot;\n   225\t    chn[8] = \&quot;%[%d+%. 自定义频道.-%]\&quot; -- 请修改频道名对应你游戏里的频道\n   226\telseif Language == \&quot;zhTW\&quot; then\n   227\t    ---------------------------------------- 台服繁体中文 ---------------------------------------------\n   228\t    chn[1] = \&quot;%[%d+%. 綜合.-%]\&quot;\n   229\t    chn[2] = \&quot;%[%d+%. 貿易.-%]\&quot;\n   230\t    chn[3] = \&quot;%[%d+%. 本地防務.-%]\&quot;\n   231\t    chn[4] = \&quot;%[%d+%. 尋求組隊%]\&quot;\n   232\t    chn[5] = \&quot;%[%d+%. 世界防務%]\&quot;\n   233\t    chn[6] = \&quot;%[%d+%. 公會招募.-%]\&quot;\n   234\t    chn[7] = \&quot;%[%d+%. 大脚世界频道.-%]\&quot;\n   235\t    chn[8] = \&quot;%[%d+%. 自定义频道.-%]\&quot; -- 请修改频道名对应你游戏里的频道\n   236\tend\n   237\t\n   238\tlocal rules = {\n   239\t        --!!不要改\n   240\t        {pat = \&quot;|c%x+|HChatCopy|h.-|h|r\&quot;, repl = \&quot;\&quot;},\n   241\t        {pat = \&quot;|c%x%x%x%x%x%x%x%x(.-)|r\&quot;, repl = \&quot;%1\&quot;},\n   242\t        --左鍵\n   243\t        {pat = \&quot;|Hchannel:.-|h.-|h\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   244\t        {pat = \&quot;|Hplayer:.-|h.-|h\&quot; .. \&quot;:\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   245\t        {pat = \&quot;|Hplayer:.-|h.-|h\&quot; .. \&quot;：\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   246\t        {pat = \&quot;|HBNplayer:.-|h.-|h\&quot; .. \&quot;:\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   247\t        {pat = \&quot;|HBNplayer:.-|h.-|h\&quot; .. \&quot;：\&quot;, repl = \&quot;\&quot;, button = \&quot;LeftButton\&quot;},\n   248\t        --右鍵\n   249\t        {pat = \&quot;|Hchannel:.-|h(.-)|h\&quot;, repl = \&quot;%1\&quot;, button = \&quot;RightButton\&quot;},\n   250\t        {pat = \&quot;|Hplayer:.-|h(.-)|h\&quot;, repl = \&quot;%1\&quot;, button = \&quot;RightButton\&quot;},\n   251\t        {pat = \&quot;|HBNplayer:.-|h(.-)|h\&quot;, repl = \&quot;%1\&quot;, button = \&quot;RightButton\&quot;},\n   252\t        --!!不要改\n   253\t        {pat = \&quot;|H.-|h(.-)|h\&quot;, repl = \&quot;%1\&quot;},\n   254\t        {pat = \&quot;|TInterface\\\\TargetingFrame\\\\UI%-RaidTargetingIcon_(%d):0|t\&quot;, repl = \&quot;{rt%1}\&quot;},\n   255\t        {pat = \&quot;|T.-|t\&quot;, repl = \&quot;\&quot;},\n   256\t        {pat = \&quot;^%s+\&quot;, repl = \&quot;\&quot;}\n   257\t}\n   258\t\n   259\t-- 文字修改函数\n   260\tfunction WChat:AddMessage(text, ...)\n   261\t    -- 频道标签精简\n   262\t    if (type(text) ~= \&quot;string\&quot;) then\n   263\t        text = tostring(text)\n   264\t    end\n   265\t    for i = 1, 8 do -- 对应上面几个频道(如果有9个频道就for i = 1, 9 do)\n   266\t        text = text:gsub(chn[i], rplc[i])\n   267\t    end\n   268\t    text = text:gsub(\&quot;%[(%d0?)%. .-%]\&quot;, \&quot;%1.\&quot;)\n   269\t    return self.DefaultAddMessage(self, text, ...)\n   270\tend\n   271\t\n   272\t-- 初始化频道信息精简模块\n   273\tfunction WChat:InitChannel()\n   274\t    if WChat_Config.ShortChannel then\n   275\t        for i = 1, (NUM_CHAT_WINDOWS or 10) do\n   276\t            if i ~= 2 then\n   277\t                local cf = _G['ChatFrame' .. i]\n   278\t                cf.DefaultAddMessage = cf.AddMessage\n   279\t                cf.AddMessage = self.AddMessage\n   280\t            end\n   281\t        end\n   282\t    end\n   283\tend\n   284\t-------------------------------聊天复制------------------------------------\n   285\t\n   286\tlocal lines = {}\n   287\t\n   288\tlocal chatCopyFrame = CreateFrame(\&quot;Frame\&quot;, \&quot;ChatCopyFrame\&quot;, UIParent, BackdropTemplateMixin and \&quot;BackdropTemplate\&quot; or nil)\n   289\tchatCopyFrame:SetPoint(\&quot;CENTER\&quot;, UIParent, \&quot;CENTER\&quot;)\n   290\tchatCopyFrame:SetSize(700, 400)\n   291\tchatCopyFrame:Hide()\n   292\tchatCopyFrame:SetFrameStrata(\&quot;DIALOG\&quot;)\n   293\tchatCopyFrame.close = CreateFrame(\&quot;Button\&quot;, nil, chatCopyFrame, \&quot;UIPanelCloseButton\&quot;)\n   294\tchatCopyFrame.close:SetPoint(\&quot;TOPRIGHT\&quot;, chatCopyFrame, \&quot;TOPRIGHT\&quot;)\n   295\tchatCopyFrame:SetBackdrop({\n   296\t    bgFile = \&quot;Interface/DialogFrame/UI-DialogBox-Background\&quot;,\n   297\t    edgeFile = \&quot;Interface/DialogFrame/UI-DialogBox-Border\&quot;,\n   298\t    tile = true,\n   299\t    tileSize = 16,\n   300\t    edgeSize = 16,\n   301\t    insets = {left = 4, right = 4, top = 4, bottom = 4}\n   302\t})\n   303\t\n   304\tlocal scrollArea = CreateFrame(\&quot;ScrollFrame\&quot;, \&quot;ChatCopyScrollFrame\&quot;, chatCopyFrame, \&quot;UIPanelScrollFrameTemplate\&quot;)\n   305\tscrollArea:SetPoint(\&quot;TOPLEFT\&quot;, chatCopyFrame, \&quot;TOPLEFT\&quot;, 10, -30)\n   306\tscrollArea:SetPoint(\&quot;BOTTOMRIGHT\&quot;, chatCopyFrame, \&quot;BOTTOMRIGHT\&quot;, -30, 10)\n   307\t\n   308\tlocal editBox = CreateFrame(\&quot;EditBox\&quot;, nil, chatCopyFrame)\n   309\teditBox:SetMultiLine(true)\n   310\teditBox:SetMaxLetters(99999)\n   311\teditBox:EnableMouse(true)\n   312\teditBox:SetAutoFocus(false)\n   313\teditBox:SetFontObject(ChatFontNormal)\n   314\teditBox:SetWidth(scrollArea:GetWidth())\n   315\teditBox:SetHeight(270)\n   316\teditBox:SetScript(\&quot;OnEscapePressed\&quot;, function(f)f:GetParent():GetParent():Hide()f:SetText(\&quot;\&quot;) end)\n   317\tscrollArea:SetScrollChild(editBox)\n   318\t\n   319\tfunction WChat:CopyFunc()\n   320\t    local cf = SELECTED_CHAT_FRAME\n   321\t    local _, size = cf:GetFont()\n   322\t    FCF_SetChatWindowFontSize(cf, cf, .01)\n   323\t    local ct = 1\n   324\t    for i = select(\&quot;#\&quot;, cf.FontStringContainer:GetRegions()), 1, -1 do\n   325\t        local region = select(i, cf.FontStringContainer:GetRegions())\n   326\t        if region:GetObjectType() == \&quot;FontString\&quot; then\n   327\t            if region:GetText() ~= nil then\n   328\t                lines[ct] = tostring(region:GetText())\n   329\t                ct = ct + 1\n   330\t            end\n   331\t        end\n   332\t    end\n   333\t    local lineCt = ct - 1\n   334\t    local text = table.concat(lines, \&quot;\\n\&quot;, 1, lineCt)\n   335\t    FCF_SetChatWindowFontSize(cf, cf, size)\n   336\t    chatCopyFrame:Show()\n   337\t    editBox:SetText(text)\n   338\t    editBox:HighlightText(0)\n   339\t    wipe(lines)\n   340\tend\n   341\t----------------------------聊天表情相关代码-----------------------------\n   342\t\n   343\t-- 表情选择器框架\n   344\tlocal EmoteTableFrame\n   345\t\n   346\t-- 表情解析规则\n   347\tlocal fmtstring\n   348\t\n   349\t-- 自定义表情开始的序号\n   350\tlocal customEmoteStartIndex = 9\n   351\t\n   352\tlocal emotes = {\n   353\t        --原版暴雪提供的8个图标\n   354\t        {\&quot;{rt1}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_1]=]},\n   355\t        {\&quot;{rt2}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_2]=]},\n   356\t        {\&quot;{rt3}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_3]=]},\n   357\t        {\&quot;{rt4}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_4]=]},\n   358\t        {\&quot;{rt5}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_5]=]},\n   359\t        {\&quot;{rt6}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_6]=]},\n   360\t        {\&quot;{rt7}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_7]=]},\n   361\t        {\&quot;{rt8}\&quot;, [=[Interface\\TargetingFrame\\UI-RaidTargetingIcon_8]=]},\n   362\t        --自定义表情\n   363\t        {\&quot;{天使}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Angel]=]},\n   364\t        {\&quot;{生气}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Angry]=]},\n   365\t        {\&quot;{大笑}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Biglaugh]=]},\n   366\t        {\&quot;{鼓掌}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Clap]=]},\n   367\t        {\&quot;{酷}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Cool]=]},\n   368\t        {\&quot;{哭}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Cry]=]},\n   369\t        {\&quot;{可爱}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Cutie]=]},\n   370\t        {\&quot;{鄙视}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Despise]=]},\n   371\t        {\&quot;{美梦}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Dreamsmile]=]},\n   372\t        {\&quot;{尴尬}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Embarrass]=]},\n   373\t        {\&quot;{邪恶}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Evil]=]},\n   374\t        {\&quot;{兴奋}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Excited]=]},\n   375\t        {\&quot;{晕}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Faint]=]},\n   376\t        {\&quot;{打架}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Fight]=]},\n   377\t        {\&quot;{流感}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Flu]=]},\n   378\t        {\&quot;{呆}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Freeze]=]},\n   379\t        {\&quot;{皱眉}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Frown]=]},\n   380\t        {\&quot;{致敬}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Greet]=]},\n   381\t        {\&quot;{鬼脸}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Grimace]=]},\n   382\t        {\&quot;{龇牙}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Growl]=]},\n   383\t        {\&quot;{开心}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Happy]=]},\n   384\t        {\&quot;{心}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Heart]=]},\n   385\t        {\&quot;{恐惧}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Horror]=]},\n   386\t        {\&quot;{生病}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Ill]=]},\n   387\t        {\&quot;{无辜}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Innocent]=]},\n   388\t        {\&quot;{功夫}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Kongfu]=]},\n   389\t        {\&quot;{花痴}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Love]=]},\n   390\t        {\&quot;{邮件}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Mail]=]},\n   391\t        {\&quot;{化妆}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Makeup]=]},\n   392\t        -- {\&quot;{马里奥}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Mario]=]},\n   393\t        {\&quot;{沉思}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Meditate]=]},\n   394\t        {\&quot;{可怜}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Miserable]=]},\n   395\t        {\&quot;{好}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Okay]=]},\n   396\t        {\&quot;{漂亮}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Pretty]=]},\n   397\t        {\&quot;{吐}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Puke]=]},\n   398\t        {\&quot;{握手}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Shake]=]},\n   399\t        {\&quot;{喊}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Shout]=]},\n   400\t        {\&quot;{闭嘴}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Shuuuu]=]},\n   401\t        {\&quot;{害羞}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Shy]=]},\n   402\t        {\&quot;{睡觉}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Sleep]=]},\n   403\t        {\&quot;{微笑}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Smile]=]},\n   404\t        {\&quot;{吃惊}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Suprise]=]},\n   405\t        {\&quot;{失败}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Surrender]=]},\n   406\t        {\&quot;{流汗}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Sweat]=]},\n   407\t        {\&quot;{流泪}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Tear]=]},\n   408\t        {\&quot;{悲剧}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Tears]=]},\n   409\t        {\&quot;{想}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Think]=]},\n   410\t        {\&quot;{偷笑}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Titter]=]},\n   411\t        {\&quot;{猥琐}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Ugly]=]},\n   412\t        {\&quot;{胜利}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Victory]=]},\n   413\t        {\&quot;{雷锋}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Volunteer]=]},\n   414\t        {\&quot;{委屈}\&quot;, [=[Interface\\Addons\\WanTiny\\Textures\\Emotion\\Wronged]=]}\n   415\t}\n   416\t\n   417\tlocal function ChatEmoteFilter(self, event, msg, ...)\n   418\t    if (WChat_Config and WChat_Config.EnableEmoteInput) then\n   419\t        for i = customEmoteStartIndex, #emotes do\n   420\t            if msg:find(emotes[i][1]) then\n   421\t                msg = msg:gsub(emotes[i][1], format(fmtstring, emotes[i][2]), 1)\n   422\t            end\n   423\t        end\n   424\t    end\n   425\t    return false, msg, ...\n   426\tend\n   427\t\n   428\tlocal function EmoteIconMouseUp(frame, button)\n   429\t    if (button == \&quot;LeftButton\&quot;) then\n   430\t        local chatFrame = GetCVar(\&quot;chatStyle\&quot;) == \&quot;im\&quot; and SELECTED_CHAT_FRAME or DEFAULT_CHAT_FRAME\n   431\t        local eb = chatFrame and chatFrame.editBox\n   432\t        if (eb) then\n   433\t            eb:Insert(frame.text)\n   434\t            eb:Show();\n   435\t            eb:SetFocus()\n   436\t        end\n   437\t    end\n   438\t    WChat:ToggleEmoteTable()\n   439\tend\n   440\t\n   441\tfunction WChat:InitEmoteTableFrame()\n   442\t    fmtstring = format(\&quot;\\124T%%s:%d\\124t\&quot;, max(floor(select(2, SELECTED_CHAT_FRAME:GetFont())), WChat_Config.EmoteIconSize))\n   443\t    \n   444\t    EmoteTableFrame = CreateFrame(\&quot;Frame\&quot;, \&quot;EmoteTableFrame\&quot;, UIParent, BackdropTemplateMixin and \&quot;BackdropTemplate\&quot; or nil)\n   445\t    EmoteTableFrame:SetMovable(true)\n   446\t    EmoteTableFrame:RegisterForDrag(\&quot;LeftButton\&quot;)\n   447\t    EmoteTableFrame:SetScript(\&quot;OnDragStart\&quot;, EmoteTableFrame.StartMoving)\n   448\t    EmoteTableFrame:SetScript(\&quot;OnDragStop\&quot;, EmoteTableFrame.StopMovingOrSizing)\n   449\t    EmoteTableFrame:EnableMouse(true)\n   450\t    EmoteTableFrame:SetWidth((WChat_Config.EmoteIconListSize + 6) * 12 + 10)\n   451\t    EmoteTableFrame:SetHeight((WChat_Config.EmoteIconListSize + 6) * 5 + 10)\n   452\t    EmoteTableFrame:SetPoint(\&quot;BOTTOM\&quot;, ChatFrame1EditBox, WChat_Config.EmoteOffsetX, WChat_Config.EmoteOffsetY)\n   453\t    EmoteTableFrame:SetBackdrop({\n   454\t        bgFile = \&quot;Interface\\\\Buttons\\\\WHITE8x8\&quot;,\n   455\t        edgeFile = \&quot;Interface\\\\Tooltips\\\\UI-Tooltip-Border\&quot;,\n   456\t        tile = true,\n   457\t        tileSize = 16,\n   458\t        edgeSize = 16,\n   459\t        insets = {left = 3, right = 3, top = 3, bottom = 3}\n   460\t    })\n   461\t    EmoteTableFrame:SetBackdropColor(0.05, 0.05, 0.05, 0.8)\n   462\t    EmoteTableFrame:SetBackdropBorderColor(0.3, 0.3, 0.3)\n   463\t    -- 表情选择框出现位置 默认30,30\n   464\t    EmoteTableFrame:Hide()\n   465\t    EmoteTableFrame:SetFrameStrata(\&quot;DIALOG\&quot;)\n   466\t    local icon, row, col\n   467\t    row = 1\n   468\t    col = 1\n   469\t    for i = 1, #emotes do\n   470\t        text = emotes[i][1]\n   471\t        texture = emotes[i][2]\n   472\t        icon = CreateFrame(\&quot;Frame\&quot;, format(\&quot;IconButton%d\&quot;, i), EmoteTableFrame)\n   473\t        icon:SetWidth(WChat_Config.EmoteIconListSize + 6)\n   474\t        icon:SetHeight(WChat_Config.EmoteIconListSize + 6)\n   475\t        icon.text = text\n   476\t        icon.texture = icon:CreateTexture(nil, \&quot;ARTWORK\&quot;)\n   477\t        icon.texture:SetTexture(texture)\n   478\t        icon.texture:SetAllPoints(icon)\n   479\t        icon:Show()\n   480\t        icon:SetPoint(\n   481\t            \&quot;TOPLEFT\&quot;,\n   482\t            5 + (col - 1) * (WChat_Config.EmoteIconListSize + 6),\n   483\t            -5 - (row - 1) * (WChat_Config.EmoteIconListSize + 6)\n   484\t        )\n   485\t        icon:SetScript(\&quot;OnMouseUp\&quot;, EmoteIconMouseUp)\n   486\t        icon:EnableMouse(true)\n   487\t        col = col + 1\n   488\t        if (col &gt; 12) then\n   489\t            row = row + 1\n   490\t            col = 1\n   491\t        end\n   492\t    end\n   493\t    \n   494\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_CHANNEL\&quot;, ChatEmoteFilter)-- 公共频道\n   495\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_SAY\&quot;, ChatEmoteFilter)-- 说\n   496\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_YELL\&quot;, ChatEmoteFilter)-- 大喊\n   497\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_RAID\&quot;, ChatEmoteFilter)-- 团队\n   498\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_RAID_LEADER\&quot;, ChatEmoteFilter)-- 团队领袖\n   499\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_PARTY\&quot;, ChatEmoteFilter)-- 队伍\n   500\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_PARTY_LEADER\&quot;, ChatEmoteFilter)-- 队伍领袖\n   501\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_GUILD\&quot;, ChatEmoteFilter)-- 公会\n   502\t    \n   503\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_AFK\&quot;, ChatEmoteFilter)-- AFK玩家自动回复\n   504\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_DND\&quot;, ChatEmoteFilter)-- 切勿打扰自动回复\n   505\t    \n   506\t    -- 副本和副本领袖\n   507\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_INSTANCE_CHAT\&quot;, ChatEmoteFilter)\n   508\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_INSTANCE_CHAT_LEADER\&quot;, ChatEmoteFilter)\n   509\t    -- 解析战网私聊\n   510\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_WHISPER\&quot;, ChatEmoteFilter)\n   511\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_WHISPER_INFORM\&quot;, ChatEmoteFilter)\n   512\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_BN_WHISPER\&quot;, ChatEmoteFilter)\n   513\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_BN_WHISPER_INFORM\&quot;, ChatEmoteFilter)\n   514\t    -- 解析社区聊天内容\n   515\t    ChatFrame_AddMessageEventFilter(\&quot;CHAT_MSG_COMMUNITIES_CHANNEL\&quot;, ChatEmoteFilter)\n   516\tend\n   517\t\n   518\tfunction WChat:ToggleEmoteTable()\n   519\t    if (EmoteTableFrame:IsShown()) then\n   520\t        EmoteTableFrame:Hide()\n   521\t    else\n   522\t        EmoteTableFrame:Show()\n   523\t    end\n   524\tend\n   525\t-----------------------------------频道和密语对象的快速切换\n   526\t\n   527\tfunction ChatEdit_CustomTabPressed(...)\n   528\t    return ChatEdit_CustomTabPressed_Inner(...)\n   529\tend\n   530\t\n   531\tlocal cycles = {\n   532\t        -- \&quot;说\&quot;\n   533\t        {\n   534\t            chatType = \&quot;SAY\&quot;,\n   535\t            use = function(self, editbox)\n   536\t                return 1\n   537\t            end\n   538\t        },\n   539\t        --大喊\n   540\t        {\n   541\t            chatType = \&quot;YELL\&quot;,\n   542\t            use = function(self, editbox)\n   543\t                return 1\n   544\t            end\n   545\t        },\n   546\t        --小队\n   547\t        {\n   548\t            chatType = \&quot;PARTY\&quot;,\n   549\t            use = function(self, editbox)\n   550\t                return IsInGroup()\n   551\t            end\n   552\t        },\n   553\t        --团队\n   554\t        {\n   555\t            chatType = \&quot;RAID\&quot;,\n   556\t            use = function(self, editbox)\n   557\t                return IsInRaid()\n   558\t            end\n   559\t        },\n   560\t        --实时聊天\n   561\t        {\n   562\t            chatType = \&quot;INSTANCE_CHAT\&quot;,\n   563\t            use = function(self, editbox)\n   564\t                return select(2, IsInInstance()) == \&quot;pvp\&quot;\n   565\t            end\n   566\t        },\n   567\t        --公会\n   568\t        {\n   569\t            chatType = \&quot;GUILD\&quot;,\n   570\t            use = function(self, editbox)\n   571\t                return IsInGuild()\n   572\t            end\n   573\t        },\n   574\t        --频道\n   575\t        {\n   576\t            chatType = \&quot;CHANNEL\&quot;,\n   577\t            use = function(self, editbox, currChatType)\n   578\t                local currNum\n   579\t                if currChatType ~= \&quot;CHANNEL\&quot; then\n   580\t                    currNum = IsShiftKeyDown() and 21 or 0\n   581\t                else\n   582\t                    currNum = editbox:GetAttribute(\&quot;channelTarget\&quot;)\n   583\t                end\n   584\t                local h, r, step = currNum + 1, 20, 1\n   585\t                if IsShiftKeyDown() then\n   586\t                    h, r, step = currNum - 1, 1, -1\n   587\t                end\n   588\t                for i = h, r, step do\n   589\t                    local channelNum, channelName = GetChannelName(i)\n   590\t                    if channelNum &gt; 0 and channelName:find(\&quot;大脚世界频道\&quot;) then\n   591\t                        --print(channelName); --DEBUG\n   592\t                        editbox:SetAttribute(\&quot;channelTarget\&quot;, i)\n   593\t                        return true\n   594\t                    end\n   595\t                end\n   596\t            end\n   597\t        },\n   598\t        {\n   599\t            chatType = \&quot;SAY\&quot;,\n   600\t            use = function(self, editbox)\n   601\t                return 1\n   602\t            end\n   603\t        }\n   604\t}\n   605\t\n   606\tlocal chatTypeBeforeSwitch, tellTargetBeforeSwitch --记录在频道和密语之间切换时的状态\n   607\tfunction ChatEdit_CustomTabPressed_Inner(self)\n   608\t    if strsub(tostring(self:GetText()), 1, 1) == \&quot;/\&quot; then\n   609\t        return\n   610\t    end\n   611\t    local currChatType = self:GetAttribute(\&quot;chatType\&quot;)\n   612\t    if (IsControlKeyDown()) then\n   613\t        if (currChatType == \&quot;WHISPER\&quot; or currChatType == \&quot;BN_WHISPER\&quot;) then\n   614\t            --记录之前的密语对象，以便后续切回\n   615\t            self:SetAttribute(\&quot;chatType\&quot;, chatTypeBeforeSwitch or \&quot;SAY\&quot;)\n   616\t            ChatEdit_UpdateHeader(self)\n   617\t            chatTypeBeforeSwitch = \&quot;WHISPER\&quot;\n   618\t            tellTargetBeforeSwitch = self:GetAttribute(\&quot;tellTarget\&quot;)\n   619\t            return --这里和下面不同，这里可以不返回true\n   620\t        else\n   621\t            local newTarget, newTargetType = ChatEdit_GetNextTellTarget()\n   622\t            if tellTargetBeforeSwitch or (newTarget and newTarget ~= \&quot;\&quot;) then\n   623\t                self:SetAttribute(\&quot;chatType\&quot;, tellTargetBeforeSwitch and chatTypeBeforeSwitch or newTargetType)\n   624\t                self:SetAttribute(\&quot;tellTarget\&quot;, tellTargetBeforeSwitch or newTarget)\n   625\t                ChatEdit_UpdateHeader(self)\n   626\t                chatTypeBeforeSwitch = currChatType\n   627\t                tellTargetBeforeSwitch = nil\n   628\t                return true --这里必须返回true，否则会被暴雪默认的再切换一次密语对象\n   629\t            end\n   630\t        end\n   631\t    end\n   632\t    \n   633\t    --对于说然后SHIFT的情况，因为没有return，所以第一层循环会一直遍历到最后的SAY\n   634\t    for i, curr in ipairs(cycles) do\n   635\t        if curr.chatType == currChatType then\n   636\t            local h, r, step = i + 1, #cycles, 1\n   637\t            if IsShiftKeyDown() then\n   638\t                h, r, step = i - 1, 1, -1\n   639\t            end\n   640\t            if currChatType == \&quot;CHANNEL\&quot; then\n   641\t                h = i\n   642\t            end --频道仍然要测试一下\n   643\t            for j = h, r, step do\n   644\t                if cycles[j]:use(self, currChatType) then\n   645\t                    self:SetAttribute(\&quot;chatType\&quot;, cycles[j].chatType)\n   646\t                    ChatEdit_UpdateHeader(self)\n   647\t                    return\n   648\t                end\n   649\t            end\n   650\t        end\n   651\t    end\n   652\tend\n   653\t\n   654\t\n   655\t\n   656\t-----------------------------点击时间复制 (alaCHAT完整实现)\n   657\t-- 基于alaCHAT的完整时间戳点击复制功能\n   658\t\n   659\tlocal hooksecurefunc = hooksecurefunc;\n   660\tlocal date = date;\n   661\tlocal format, gsub, strmatch = string.format, string.gsub, string.match;\n   662\tlocal GetCVar, SetCVar = GetCVar, SetCVar;\n   663\tlocal ChatEdit_ChooseBoxForSend, ChatEdit_ActivateChat = ChatEdit_ChooseBoxForSend, ChatEdit_ActivateChat;\n   664\tlocal ItemRefTooltip = ItemRefTooltip;\n   665\tlocal _G = _G;\n   666\t\n   667\tlocal __copy = {};\n   668\tlocal _db = {};\n   669\t\n   670\tlocal __TAG = \&quot;\&quot;;\n   671\tlocal __FMT = \&quot;\&quot;;\n   672\tlocal __CLR = { 1.0, 1.0, 1.0, };\n   673\t\n   674\t-- 设置时间戳 (alaCHAT原版)\n   675\tlocal function SetTimeStamp()\n   676\t    __TAG = format(\&quot;|cff%.2x%.2x%.2x|Haccopy:-1|h%s|h|r\&quot;, __CLR[1] * 255, __CLR[2] * 255, __CLR[3] * 255, (__FMT == nil or __FMT == \&quot;\&quot; or __FMT == \&quot;none\&quot;) and \&quot;*\&quot; or __FMT);\n   677\t    if GetCVar(\&quot;showTimestamps\&quot;) ~= __TAG then\n   678\t        SetCVar(\&quot;showTimestamps\&quot;, __TAG);\n   679\t        _G.CHAT_TIMESTAMP_FORMAT = __TAG;\n   680\t    end\n   681\tend\n   682\t\n   683\t-- 初始化时间戳点击复制功能 (alaCHAT原版)\n   684\tlocal B_Initialized = false;\n   685\tlocal function Init()\n   686\t    B_Initialized = true;\n   687\t    local _ItemRefTooltip_SetHyperlink = ItemRefTooltip.SetHyperlink;\n   688\t    function ItemRefTooltip:SetHyperlink(link, ...)\n   689\t        if link == \&quot;accopy:-1\&quot; then\n   690\t            local focus = GetMouseFocus and GetMouseFocus() or GetMouseFoci and GetMouseFoci()[1];\n   691\t            if not focus:IsObjectType(\&quot;FontString\&quot;) then\n   692\t                focus = focus:GetParent();\n   693\t                if not focus:IsObjectType(\&quot;FontString\&quot;) then\n   694\t                    return;\n   695\t                end\n   696\t            end\n   697\t            local tx = focus:GetText();\n   698\t            if tx == nil or tx == \&quot;\&quot; then\n   699\t                return;\n   700\t            end\n   701\t            -- 清理文本格式 (alaCHAT原版处理)\n   702\t            tx = gsub(tx, \&quot;|H.-|h\&quot;, \&quot;\&quot;);\n   703\t            tx = gsub(tx, \&quot;|c%x%x%x%x%x%x%x%x\&quot;, \&quot;\&quot;);\n   704\t            tx = gsub(tx, \&quot;|[Hhr]\&quot;, \&quot;\&quot;);\n   705\t            local editBox = ChatEdit_ChooseBoxForSend();\n   706\t            if not editBox:HasFocus() then\n   707\t                ChatEdit_ActivateChat(editBox);\n   708\t            end\n   709\t            editBox:SetText(tx);\n   710\t            return;\n   711\t        end\n   712\t        return _ItemRefTooltip_SetHyperlink(self, link, ...);\n   713\t    end\n   714\t    -- 监听时间戳设置变化\n   715\t    if InterfaceOptionsSocialPanelTimestamps ~= nil and InterfaceOptionsSocialPanelTimestamps.SetValue ~= nil then\n   716\t        hooksecurefunc(InterfaceOptionsSocialPanelTimestamps, \&quot;SetValue\&quot;, function(self, value)\n   717\t            _db.toggle = false;\n   718\t        end);\n   719\t    end\n   720\tend\n   721\t\n   722\t-- alaCHAT模块功能实现\n   723\tfunction __copy.color(value, loading)\n   724\t    if not loading and _db.toggle then\n   725\t        if value ~= nil and( __CLR[1] ~= value[1] or __CLR[2] ~= value[2] or __CLR[3] ~= value[3]) then\n   726\t            __CLR = { value[1], value[2], value[3], };\n   727\t            SetTimeStamp();\n   728\t        end\n   729\t    end\n   730\tend\n   731\t\n   732\tfunction __copy.format(value, loading)\n   733\t    if not loading and _db.toggle then\n   734\t        if value ~= nil and __FMT ~= value then\n   735\t            __FMT = value;\n   736\t            SetTimeStamp();\n   737\t        end\n   738\t    end\n   739\tend\n   740\t\n   741\tfunction __copy.toggle(value, loading)\n   742\t    if value then\n   743\t        if not B_Initialized then\n   744\t            Init();\n   745\t        end\n   746\t        local c = _db.color;\n   747\t        local f = _db.format;\n   748\t        __FMT = f;\n   749\t        __CLR = { c[1], c[2], c[3], };\n   750\t        SetTimeStamp();\n   751\t    elseif loading then\n   752\t        local fmt = GetCVar(\&quot;showTimestamps\&quot;);\n   753\t        if fmt ~= \&quot;none\&quot; then\n   754\t            local fmt2 = strmatch(fmt, \&quot;|h(.+)|h\&quot;);\n   755\t            if fmt2 ~= nil then\n   756\t                SetCVar(\&quot;showTimestamps\&quot;, fmt2);\n   757\t                _G.CHAT_TIMESTAMP_FORMAT = fmt2;\n   758\t            end\n   759\t        end\n   760\t    else\n   761\t        if __FMT == \&quot;none\&quot; or __FMT == \&quot;\&quot; or __FMT == nil then\n   762\t            if GetCVar(\&quot;showTimestamps\&quot;) ~= \&quot;none\&quot; then\n   763\t                SetCVar(\&quot;showTimestamps\&quot;, \&quot;none\&quot;);\n   764\t                _G.CHAT_TIMESTAMP_FORMAT = nil;\n   765\t            end\n   766\t        else\n   767\t            if GetCVar(\&quot;showTimestamps\&quot;) ~= __FMT then\n   768\t                SetCVar(\&quot;showTimestamps\&quot;, __FMT);\n   769\t                _G.CHAT_TIMESTAMP_FORMAT = __FMT;\n   770\t            end\n   771\t        end\n   772\t    end\n   773\tend\n   774\t\n   775\t-- WChat集成的时间戳复制功能\n   776\tfunction WChat:InitTimestampCopy()\n   777\t    if WChat_Config.EnableTimestampCopy then\n   778\t        -- 初始化_db配置\n   779\t        _db = {\n   780\t            toggle = true,\n   781\t            format = WChat_Config.TimestampFormat or \&quot;%H:%M:%S\&quot;,\n   782\t            color = WChat_Config.TimestampColor or { 1.0, 1.0, 1.0 }\n   783\t        };\n   784\t\n   785\t        -- 启用功能\n   786\t        __copy.toggle(true, false);\n   787\t    end\n   788\tend\n   789\t\n   790\t-----------------------------聊天条\n   791\t\n   792\t--[[=========================== 变量区 ==========================]]\n   793\t-- 是否可移动的标记\n   794\tlocal IsMovable = false -- 没事干别动这个，你改成ture那么进入游戏后聊天条就是可以移动的\n   795\t--[[=============================== END ==============================]]\n   796\tlocal chatFrame = SELECTED_DOCK_FRAME -- 聊天框架\n   797\tlocal inputbox = chatFrame.editBox -- 输入框\n   798\t\n   799\tCOLORSCHEME_BORDER = {0.3, 0.3, 0.3, 1}\n   800\t-- 边框颜色\n   801\t-- 主框架初始化\n   802\tlocal ChatBar = CreateFrame(\&quot;Frame\&quot;, nil, UIParent, BackdropTemplateMixin and \&quot;BackdropTemplate\&quot; or nil)\n   803\tWChatBar = ChatBar\n   804\t\n   805\tlocal function ChannelSay_OnClick()\n   806\t    ChatFrame_OpenChat(\&quot;/s \&quot; .. inputbox:GetText(), chatFrame)\n   807\tend\n   808\t\n   809\tlocal function ChannelYell_OnClick()\n   810\t    ChatFrame_OpenChat(\&quot;/y \&quot; .. inputbox:GetText(), chatFrame)\n   811\tend\n   812\t\n   813\tlocal function ChannelParty_OnClick()\n   814\t    ChatFrame_OpenChat(\&quot;/p \&quot; .. inputbox:GetText(), chatFrame)\n   815\tend\n   816\t\n   817\tlocal function ChannelGuild_OnClick()\n   818\t    ChatFrame_OpenChat(\&quot;/g \&quot; .. inputbox:GetText(), chatFrame)\n   819\tend\n   820\t\n   821\tlocal function ChannelRaid_OnClick()\n   822\t    ChatFrame_OpenChat(\&quot;/raid \&quot; .. inputbox:GetText(), chatFrame)\n   823\tend\n   824\t\n   825\t-- 综合频道点击处理\n   826\tlocal function ChannelGen_OnClick(self, button)\n   827\t    if button == \&quot;RightButton\&quot; then\n   828\t        -- 右键：切换频道显示/隐藏\n   829\t        ToggleChannelShowHide(\&quot;综合\&quot;)\n   830\t        C_Timer.After(0.1, UpdateChannelXIcons)\n   831\t    else\n   832\t        -- 左键：如果频道未显示则先显示，然后发言\n   833\t        if not IsChannelShown(\&quot;综合\&quot;) then\n   834\t            ToggleChannelShowHide(\&quot;综合\&quot;)\n   835\t            C_Timer.After(0.1, UpdateChannelXIcons)\n   836\t        end\n   837\t        local channel, _, _ = GetChannelName(\&quot;综合\&quot;)\n   838\t        if channel then\n   839\t            ChatFrame_OpenChat(\&quot;/\&quot; .. channel .. \&quot; \&quot; .. inputbox:GetText(), chatFrame)\n   840\t        end\n   841\t    end\n   842\tend\n   843\t\n   844\t-- 交易频道点击处理\n   845\tlocal function ChannelTrade_OnClick(self, button)\n   846\t    if button == \&quot;RightButton\&quot; then\n   847\t        -- 右键：切换频道显示/隐藏\n   848\t        ToggleChannelShowHide(\&quot;交易\&quot;)\n   849\t        C_Timer.After(0.1, UpdateChannelXIcons)\n   850\t    else\n   851\t        -- 左键：如果频道未显示则先显示，然后发言\n   852\t        if not IsChannelShown(\&quot;交易\&quot;) then\n   853\t            ToggleChannelShowHide(\&quot;交易\&quot;)\n   854\t            C_Timer.After(0.1, UpdateChannelXIcons)\n   855\t        end\n   856\t        local channel, _, _ = GetChannelName(\&quot;交易\&quot;)\n   857\t        if channel then\n   858\t            ChatFrame_OpenChat(\&quot;/\&quot; .. channel .. \&quot; \&quot; .. inputbox:GetText(), chatFrame)\n   859\t        end\n   860\t    end\n   861\tend\n   862\t\n   863\t-- 寻求组队频道点击处理\n   864\tlocal function ChannelLFG_OnClick(self, button)\n   865\t    if button == \&quot;RightButton\&quot; then\n   866\t        -- 右键：切换频道显示/隐藏\n   867\t        ToggleChannelShowHide(\&quot;寻求组队\&quot;)\n   868\t        C_Timer.After(0.1, UpdateChannelXIcons)\n   869\t    else\n   870\t        -- 左键：如果频道未显示则先显示，然后发言\n   871\t        if not IsChannelShown(\&quot;寻求组队\&quot;) then\n   872\t            ToggleChannelShowHide(\&quot;寻求组队\&quot;)\n   873\t            C_Timer.After(0.1, UpdateChannelXIcons)\n   874\t        end\n   875\t        local channel, _, _ = GetChannelName(\&quot;寻求组队\&quot;)\n   876\t        if channel then\n   877\t            ChatFrame_OpenChat(\&quot;/\&quot; .. channel .. \&quot; \&quot; .. inputbox:GetText(), chatFrame)\n   878\t        end\n   879\t    end\n   880\tend\n   881\t\n   882\tlocal function ChannelBG_OnClick(self, button)\n   883\t    -- 副本频道按钮，只有左键功能\n   884\t    ChatFrame_OpenChat(\&quot;/bg \&quot; .. inputbox:GetText(), chatFrame)\n   885\tend\n   886\t\n   887\t-- function Channel01_OnClick()\n   888\t--     ChatFrame_OpenChat(\&quot;/1 \&quot;, chatFrame)\n   889\t-- end\n   890\tlocal function ChatEmote_OnClick()\n   891\t    WChat:ToggleEmoteTable()\n   892\tend\n   893\t\n   894\tlocal function ChannelWorld_OnClick(self, button)\n   895\t    if button == \&quot;RightButton\&quot; then\n   896\t        -- 右键：切换频道显示/隐藏\n   897\t        ToggleChannelShowHide(\&quot;大脚世界频道\&quot;)\n   898\t        C_Timer.After(0.1, UpdateChannelXIcons)\n   899\t    else\n   900\t        -- 左键：发言或加入频道后发言\n   901\t        local _, channelName, _ = GetChannelName(\&quot;大脚世界频道\&quot;)\n   902\t        if channelName == nil then\n   903\t            JoinPermanentChannel(\&quot;大脚世界频道\&quot;, nil, 1, 1)\n   904\t            ChatFrame_RemoveMessageGroup(chatFrame, \&quot;CHANNEL\&quot;)\n   905\t            ChatFrame_AddChannel(chatFrame, \&quot;大脚世界频道\&quot;)\n   906\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cff00d200已加入大脚世界频道|r\&quot;)\n   907\t            -- 确保频道显示\n   908\t            C_Timer.After(0.2, function()\n   909\t                if not IsChannelShown(\&quot;大脚世界频道\&quot;) then\n   910\t                    ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, \&quot;大脚世界频道\&quot;)\n   911\t                    WChat_Config.HiddenChannels[\&quot;大脚世界频道\&quot;] = nil\n   912\t                end\n   913\t                UpdateChannelXIcons()\n   914\t            end)\n   915\t        else\n   916\t            -- 如果频道未显示则先显示\n   917\t            if not IsChannelShown(\&quot;大脚世界频道\&quot;) then\n   918\t                ToggleChannelShowHide(\&quot;大脚世界频道\&quot;)\n   919\t                C_Timer.After(0.1, UpdateChannelXIcons)\n   920\t            end\n   921\t        end\n   922\t\n   923\t        local channel, _, _ = GetChannelName(\&quot;大脚世界频道\&quot;)\n   924\t        if channel then\n   925\t            ChatFrame_OpenChat(\&quot;/\&quot; .. channel .. \&quot; \&quot; .. inputbox:GetText(), chatFrame)\n   926\t        end\n   927\t    end\n   928\tend\n   929\t\n   930\tlocal function Roll_OnClick()\n   931\t    RandomRoll(1, 100)\n   932\tend\n   933\t\n   934\tlocal function Report_OnClick(self, button)\n   935\t    local statText = WChat:StatReport()\n   936\t    if button == \&quot;RightButton\&quot; then\n   937\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cff00d200我的属性：|r\&quot; .. statText)\n   938\t    else\n   939\t        local editBox = ChatEdit_ChooseBoxForSend()\n   940\t        if not editBox:HasFocus() then\n   941\t            ChatEdit_ActivateChat(editBox)\n   942\t        end\n   943\t        editBox:Insert(statText)\n   944\t    end\n   945\tend\n   946\t\n   947\tlocal function ChatCopy_OnClick()\n   948\t    WChat:CopyFunc()\n   949\tend\n   950\t\n   951\t-- 插件配置表\n   952\tlocal AddonConfigs = {\n   953\t    BiaoGe = {\n   954\t        addonName = \&quot;BiaoGe\&quot;,\n   955\t        displayName = \&quot;BiaoGe金团\&quot;,\n   956\t        globalVar = \&quot;BG\&quot;,\n   957\t        mainFrameKey = \&quot;MainFrame\&quot;,\n   958\t        slashCmd = nil,\n   959\t        tooltip = function()\n   960\t            return \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开BiaoGe金团|r\&quot;\n   961\t        end\n   962\t    },\n   963\t    BiaoGeAI = {\n   964\t        addonName = \&quot;BiaoGeAI\&quot;,\n   965\t        displayName = \&quot;BiaoGeAI\&quot;,\n   966\t        globalVar = \&quot;BGAI\&quot;,\n   967\t        mainFrameKey = \&quot;MainFrame\&quot;,\n   968\t        slashCmd = nil,\n   969\t        tooltip = function()\n   970\t            return \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开BiaoGeAI|r\&quot;\n   971\t        end\n   972\t    },\n   973\t    AtlasLootClassic = {\n   974\t        addonName = \&quot;AtlasLootClassic\&quot;,\n   975\t        displayName = \&quot;Atlas掉落\&quot;,\n   976\t        globalVar = nil,\n   977\t        mainFrameKey = nil,\n   978\t        slashCmd = \&quot;ATLASLOOT\&quot;,\n   979\t        tooltip = function()\n   980\t            return \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开Atlas掉落查询|r\&quot;\n   981\t        end\n   982\t    },\n   983\t    MeetingHorn = {\n   984\t        addonName = \&quot;MeetingHorn\&quot;,\n   985\t        displayName = \&quot;MeetingHorn集合石\&quot;,\n   986\t        globalVar = nil,\n   987\t        mainFrameKey = nil,\n   988\t        slashCmd = \&quot;MEETINGHORN\&quot;,\n   989\t        tooltip = function()\n   990\t            local tooltip = \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开集结号|r\&quot;\n   991\t\n   992\t            local addon = LibStub and LibStub('AceAddon-3.0'):GetAddon('MeetingHorn', true)\n   993\t            if not addon then return tooltip end\n   994\t\n   995\t            local lfg = addon:GetModule('LFG', true)\n   996\t            if not lfg then return tooltip end\n   997\t\n   998\t            local icon1 = \&quot;|TInterface\\\\AddOns\\\\MeetingHorn\\\\Media\\\\DataBroker:16:16:0:0:64:32:0:32:0:32|t\&quot;\n   999\t            local icon2 = \&quot;|TInterface\\\\AddOns\\\\MeetingHorn\\\\Media\\\\DataBroker:16:16:0:0:64:32:32:64:0:32|t\&quot;\n  1000\t            tooltip = tooltip .. \&quot;\\n\&quot; .. icon2 .. \&quot;|cffFFD700活动数量: \&quot; .. lfg:GetActivityCount() .. \&quot;|r\&quot;\n  1001\t\n  1002\t            local count = lfg:GetCurrentActivity() and lfg:GetApplicantCount() or lfg:GetApplicationCount()\n  1003\t            local label = lfg:GetCurrentActivity() and \&quot;申请者数量\&quot; or \&quot;申请数量\&quot;\n  1004\t            return tooltip .. \&quot;\\n\&quot; .. icon1 .. \&quot;|cffFFD700\&quot; .. label .. \&quot;: \&quot; .. count .. \&quot;|r\&quot;\n  1005\t        end\n  1006\t    }\n  1007\t}\n  1008\t\n  1009\t-- 通用插件按钮点击处理函数\n  1010\tlocal function CreateAddonClickHandler(configKey)\n  1011\t    return function()\n  1012\t        local config = AddonConfigs[configKey]\n  1013\t        if not config then return end\n  1014\t\n  1015\t        -- 检查并加载插件\n  1016\t        if not IsAddOnLoaded(config.addonName) then\n  1017\t            local loaded, reason = LoadAddOn(config.addonName)\n  1018\t            if not loaded then\n  1019\t                print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cffff0000\&quot; .. config.displayName .. \&quot;插件加载失败: \&quot; .. (reason or \&quot;未知原因\&quot;) .. \&quot;|r\&quot;)\n  1020\t                return\n  1021\t            end\n  1022\t            -- 刷新聊天条按钮\n  1023\t            WChat:RefreshChatBarButtons()\n  1024\t        end\n  1025\t\n  1026\t        -- 尝试打开插件界面\n  1027\t        if config.globalVar and config.mainFrameKey then\n  1028\t            -- 使用MainFrame方式\n  1029\t            local addon = _G[config.globalVar]\n  1030\t            if addon and addon[config.mainFrameKey] then\n  1031\t                addon[config.mainFrameKey]:SetShown(not addon[config.mainFrameKey]:IsVisible())\n  1032\t            end\n  1033\t        elseif config.slashCmd then\n  1034\t            -- 使用斜杠命令方式\n  1035\t            if configKey == \&quot;MeetingHorn\&quot; then\n  1036\t                -- MeetingHorn特殊处理\n  1037\t                local meetingHornAddon = LibStub and LibStub('AceAddon-3.0'):GetAddon('MeetingHorn', true)\n  1038\t                if meetingHornAddon and meetingHornAddon.Toggle then\n  1039\t                    meetingHornAddon:Toggle()\n  1040\t                elseif SlashCmdList[config.slashCmd] then\n  1041\t                    SlashCmdList[config.slashCmd](\&quot;\&quot;)\n  1042\t                end\n  1043\t            else\n  1044\t                -- 其他插件使用斜杠命令\n  1045\t                if SlashCmdList[config.slashCmd] then\n  1046\t                    SlashCmdList[config.slashCmd](\&quot;\&quot;)\n  1047\t                end\n  1048\t            end\n  1049\t        end\n  1050\t    end\n  1051\tend\n  1052\t\n  1053\t-- 创建具体的点击处理函数\n  1054\tlocal Gold_OnClick = CreateAddonClickHandler(\&quot;BiaoGe\&quot;)\n  1055\tlocal AI_OnClick = CreateAddonClickHandler(\&quot;BiaoGeAI\&quot;)\n  1056\tlocal Atlas_OnClick = CreateAddonClickHandler(\&quot;AtlasLootClassic\&quot;)\n  1057\tlocal MeetingHorn_OnClick = CreateAddonClickHandler(\&quot;MeetingHorn\&quot;)\n  1058\t\n  1059\tlocal function Movelock_OnClick(self, button)\n  1060\t    if button == \&quot;LeftButton\&quot; then\n  1061\t        if IsMovable then\n  1062\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cffd20000锁定聊天条|r\&quot;)\n  1063\t            IsMovable = false\n  1064\t            WChatBar:SetBackdrop(nil)\n  1065\t\n  1066\t            local point, relativeTo, relativePoint, xOfs, yOfs = WChatBar:GetPoint()\n  1067\t\n  1068\t            if relativeTo then\n  1069\t                WChat_Config.Position = {point = point, relativeTo = relativeTo:GetName(), relativePoint = relativePoint, xOfs = xOfs, yOfs = yOfs}\n  1070\t            else\n  1071\t                WChat_Config.Position = {point = point, relativeTo = nil, relativePoint = relativePoint, xOfs = xOfs, yOfs = yOfs}\n  1072\t            end\n  1073\t        else\n  1074\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cff00d200解锁聊天条|r\&quot;)\n  1075\t            IsMovable = true\n  1076\t            WChatBar:SetBackdrop(\n  1077\t                {\n  1078\t                    bgFile = \&quot;Interface/DialogFrame/UI-DialogBox-Background\&quot;,\n  1079\t                    edgeFile = \&quot;Interface/DialogFrame/UI-DialogBox-Border\&quot;,\n  1080\t                    tile = true,\n  1081\t                    tileSize = 16,\n  1082\t                    edgeSize = 16,\n  1083\t                    insets = {left = 4, right = 4, top = 4, bottom = 4}\n  1084\t                }\n  1085\t        )\n  1086\t        end\n  1087\t        WChatBar:EnableMouse(IsMovable)\n  1088\t    elseif button == \&quot;MiddleButton\&quot; then\n  1089\t        if IsMovable == false then\n  1090\t            return\n  1091\t        end\n  1092\t        WChatBar:ClearAllPoints()\n  1093\t        if WChat_Config.UseVertical then\n  1094\t            if WChat_Config.UseTopChatbar then\n  1095\t                WChatBar:SetPoint(\&quot;TOPRIGHT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX - 30, WChat_Config.ChatBarOffsetY + 25)\n  1096\t            else\n  1097\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPRIGHT\&quot;, WChat_Config.ChatBarOffsetX + 30, WChat_Config.ChatBarOffsetY + 25)\n  1098\t            end\n  1099\t        else\n  1100\t            if WChat_Config.UseTopChatbar then\n  1101\t                WChatBar:SetPoint(\&quot;BOTTOMLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY + 30)\n  1102\t            else\n  1103\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;BOTTOMLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY - 30)\n  1104\t            end\n  1105\t        end\n  1106\t    end\n  1107\tend\n  1108\t\n  1109\tlocal ChannelButtons = {\n  1110\t    {name = \&quot;say\&quot;, text = \&quot;说\&quot;, color = {1.00, 1.00, 1.00}, callback = ChannelSay_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到说话频道|r\&quot; end},\n  1111\t    {name = \&quot;yell\&quot;, text = \&quot;喊\&quot;, color = {1.00, 0.25, 0.25}, callback = ChannelYell_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到大喊频道|r\&quot; end},\n  1112\t    {name = \&quot;party\&quot;, text = \&quot;队\&quot;, color = {0.66, 0.66, 1.00}, callback = ChannelParty_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到队伍频道|r\&quot; end},\n  1113\t    {name = \&quot;guild\&quot;, text = \&quot;会\&quot;, color = {0.25, 1.00, 0.25}, callback = ChannelGuild_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到公会频道|r\&quot; end},\n  1114\t    {name = \&quot;raid\&quot;, text = \&quot;团\&quot;, color = {1.00, 0.50, 0.00}, callback = ChannelRaid_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到团队频道|r\&quot; end},\n  1115\t    {name = \&quot;LFT\&quot;, text = \&quot;副\&quot;, color = {1.00, 0.50, 0.00}, callback = ChannelBG_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到副本频道|r\&quot; end},\n  1116\t    {name = \&quot;chnGen\&quot;, text = \&quot;综\&quot;, color = {0.82, 0.70, 0.55}, callback = ChannelGen_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到综合频道|r\\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r\&quot; end},\n  1117\t    {name = \&quot;chnTrade\&quot;, text = \&quot;交\&quot;, color = {1.00, 0.82, 0.00}, callback = ChannelTrade_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到交易频道|r\\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r\&quot; end},\n  1118\t    {name = \&quot;chnLFG\&quot;, text = \&quot;组\&quot;, color = {0.50, 1.00, 0.50}, callback = ChannelLFG_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到寻求组队频道|r\\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r\&quot; end},\n  1119\t    {name = \&quot;world\&quot;, text = \&quot;世\&quot;, color = {0.78, 1.00, 0.59}, callback = ChannelWorld_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff切换到世界频道|r\\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r\&quot; end},\n  1120\t    {name = \&quot;emote\&quot;, text = \&quot;表\&quot;, color = {1.00, 0.50, 1.00}, callback = ChatEmote_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff打开表情选择器|r\&quot; end},\n  1121\t    {name = \&quot;roll\&quot;, text = \&quot;骰\&quot;, color = {1.00, 1.00, 0.00}, callback = Roll_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff投掷骰子|r\&quot; end},\n  1122\t    {name = \&quot;report\&quot;, text = \&quot;报\&quot;, color = {0.80, 0.30, 0.30}, callback = Report_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff发送属性报告|r\\n|cff00ffff鼠标右键|r-|cffff80ff在聊天框显示属性|r\&quot; end},\n  1123\t    {name = \&quot;movelock\&quot;, text = \&quot;锁\&quot;, color = {0.20, 0.20, 0.80}, callback = Movelock_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff锁定/解锁聊天条位置|r\&quot; end},\n  1124\t    {name = \&quot;chatcopy\&quot;, text = \&quot;复\&quot;, color = {0.20, 0.60, 0.80}, callback = ChatCopy_OnClick, tooltip = function() return \&quot;|cff00ffff鼠标左键|r-|cffff80ff复制聊天内容|r\&quot; end},\n  1125\t    -- 插件按钮\n  1126\t    {name = \&quot;biaoge\&quot;, text = \&quot;金\&quot;, color = {1.00, 0.84, 0.00}, callback = Gold_OnClick, tooltip = AddonConfigs.BiaoGe.tooltip},\n  1127\t    {name = \&quot;biaogeai\&quot;, text = \&quot;AI\&quot;, color = {0.00, 0.80, 1.00}, callback = AI_OnClick, tooltip = AddonConfigs.BiaoGeAI.tooltip},\n  1128\t    {name = \&quot;atlas\&quot;, text = \&quot;掉\&quot;, color = {0.80, 0.20, 0.80}, callback = Atlas_OnClick, tooltip = AddonConfigs.AtlasLootClassic.tooltip},\n  1129\t    {name = \&quot;meetinghorn\&quot;, text = \&quot;集\&quot;, color = {0.20, 0.80, 0.20}, callback = MeetingHorn_OnClick, tooltip = AddonConfigs.MeetingHorn.tooltip}\n  1130\t}\n  1131\t\n  1132\tlocal function CreateChannelButton(data, index)\n  1133\t    local frame = CreateFrame(\&quot;Button\&quot;, \&quot;frameName\&quot;, WChatBar)\n  1134\t    frame:SetWidth(22)\n  1135\t    -- 按钮宽度\n  1136\t    frame:SetHeight(22)\n  1137\t    -- 按钮高度\n  1138\t    frame:SetAlpha(WChat_Config.AlphaOnLeave)\n  1139\t    \n  1140\t    frame:SetFrameLevel(1)\n  1141\t    \n  1142\t    frame:SetScript(\n  1143\t        \&quot;OnEnter\&quot;,\n  1144\t        function(self)\n  1145\t            self:SetAlpha(WChat_Config.AlphaOnEnter)\n  1146\t            -- 显示鼠标提示\n  1147\t            if data.tooltip then\n  1148\t                GameTooltip:SetOwner(self, \&quot;ANCHOR_TOP\&quot;)\n  1149\t                GameTooltip:SetText(data.tooltip(), nil, nil, nil, nil, true)\n  1150\t                GameTooltip:Show()\n  1151\t            end\n  1152\t        end\n  1153\t    )\n  1154\t    frame:SetScript(\n  1155\t        \&quot;OnLeave\&quot;,\n  1156\t        function(self)\n  1157\t            self:SetAlpha(WChat_Config.AlphaOnLeave)\n  1158\t            -- 隐藏鼠标提示\n  1159\t            GameTooltip:Hide()\n  1160\t        end\n  1161\t    )\n  1162\t    if WChat_Config.UseVertical then\n  1163\t        frame:SetPoint(\&quot;TOP\&quot;, WChatBar, \&quot;TOP\&quot;, 0, (1 - index) * WChat_Config.DistanceVertical)\n  1164\t    else\n  1165\t        frame:SetPoint(\&quot;LEFT\&quot;, WChatBar, \&quot;LEFT\&quot;, 10 + (index - 1) * WChat_Config.DistanceHorizontal, 0)\n  1166\t    end\n  1167\t    \n  1168\t    frame:RegisterForClicks(\&quot;AnyUp\&quot;)\n  1169\t    frame:SetScript(\&quot;OnClick\&quot;, data.callback)\n  1170\t    -- 显示的文字\n  1171\t    frameText = frame:CreateFontString(data.name .. \&quot;Text\&quot;, \&quot;OVERLAY\&quot;)\n  1172\t    -- 字体设置\n  1173\t    frameText:SetFont(STANDARD_TEXT_FONT, 15, \&quot;OUTLINE\&quot;)\n  1174\t    \n  1175\t    frameText:SetJustifyH(\&quot;CENTER\&quot;)\n  1176\t    frameText:SetWidth(26)\n  1177\t    frameText:SetHeight(26)\n  1178\t    frameText:SetText(data.text)\n  1179\t    frameText:SetPoint(\&quot;CENTER\&quot;, 0, 0)\n  1180\t    \n  1181\t    -- 文字按钮的颜色\n  1182\t    frameText:SetTextColor(data.color[1], data.color[2], data.color[3])\n  1183\t\n  1184\t    -- 创建频道屏蔽X图标\n  1185\t    if CHANNEL_CONFIG.MAPPINGS[data.name] then\n  1186\t        -- 创建文字X图标（更可靠）\n  1187\t        frame.X = frame:CreateFontString(nil, \&quot;OVERLAY\&quot;, \&quot;GameFontNormal\&quot;)\n  1188\t        frame.X:SetText(\&quot;X\&quot;)\n  1189\t        frame.X:SetTextColor(1, 0, 0, 0.8) -- 红色X\n  1190\t        frame.X:SetFont(\&quot;Fonts\\\\FRIZQT__.TTF\&quot;, 16, \&quot;OUTLINE\&quot;)\n  1191\t        frame.X:SetPoint(\&quot;CENTER\&quot;, frame, \&quot;CENTER\&quot;, 0, 0)\n  1192\t        frame.X:Hide() -- 默认隐藏\n  1193\t        print(\&quot;创建文字X图标: \&quot; .. data.name)\n  1194\t    end\n  1195\t\n  1196\t    -- 设置按钮名称用于识别\n  1197\t    frame.buttonName = data.name\n  1198\tend\n  1199\t\n  1200\tfunction WChat:InitChatBar()\n  1201\t\n  1202\t    WChatBar:SetFrameLevel(0)\n  1203\t\n  1204\t    -- 使用竖直布局\n  1205\t    if WChat_Config.UseVertical then\n  1206\t        -- 主框体宽度\n  1207\t        WChatBar:SetWidth(30)\n  1208\t        -- 主框体高度\n  1209\t        WChatBar:SetHeight(#ChannelButtons * WChat_Config.DistanceVertical + 10)\n  1210\t    else\n  1211\t        -- 主框体宽度\n  1212\t        WChatBar:SetWidth(#ChannelButtons * WChat_Config.DistanceHorizontal + 10)\n  1213\t        -- 主框体高度\n  1214\t        WChatBar:SetHeight(30)\n  1215\t    end\n  1216\t\n  1217\t    -- 上方聊天输入框\n  1218\t    if WChat_Config.UseTopInput then\n  1219\t        inputbox:ClearAllPoints()\n  1220\t        inputbox:SetPoint(\&quot;BOTTOMLEFT\&quot;, chatFrame, \&quot;TOPLEFT\&quot;, 0, 20)\n  1221\t        inputbox:SetPoint(\&quot;BOTTOMRIGHT\&quot;, chatFrame, \&quot;TOPRIGHT\&quot;, 0, 20)\n  1222\t    end\n  1223\t    \n  1224\t    -- 位置设定\n  1225\t    if WChat_Config.Position == nil then\n  1226\t        if WChat_Config.UseVertical then\n  1227\t            if WChat_Config.UseTopChatbar then\n  1228\t                WChatBar:SetPoint(\&quot;TOPRIGHT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX - 30, WChat_Config.ChatBarOffsetY + 25)\n  1229\t            else\n  1230\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPRIGHT\&quot;, WChat_Config.ChatBarOffsetX + 30, WChat_Config.ChatBarOffsetY + 25)\n  1231\t            end\n  1232\t        else\n  1233\t            if WChat_Config.UseTopChatbar then\n  1234\t                WChatBar:SetPoint(\&quot;BOTTOMLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY + 30)\n  1235\t            else\n  1236\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;BOTTOMLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY - 30)\n  1237\t            end\n  1238\t        end\n  1239\t    else\n  1240\t        local point = WChat_Config.Position.point\n  1241\t        local relativeTo = WChat_Config.Position.relativeTo\n  1242\t        local relativePoint = WChat_Config.Position.relativePoint\n  1243\t        local xOfs = WChat_Config.Position.xOfs\n  1244\t        local yOfs = WChat_Config.Position.yOfs\n  1245\t        WChatBar:SetPoint(point, relativeTo, relativePoint, xOfs, yOfs)\n  1246\t    end\n  1247\t\n  1248\t    WChatBar:SetMovable(true)\n  1249\t    WChatBar:RegisterForDrag(\&quot;LeftButton\&quot;)\n  1250\t    WChatBar:SetScript(\&quot;OnDragStart\&quot;, WChatBar.StartMoving)\n  1251\t    WChatBar:SetScript(\&quot;OnDragStop\&quot;, WChatBar.StopMovingOrSizing)\n  1252\t    \n  1253\t    -- 动态创建按钮，根据配置和插件存在性\n  1254\t    local buttonIndex = 1\n  1255\t    for i = 1, #ChannelButtons do\n  1256\t        local buttonData = ChannelButtons[i]\n  1257\t        local shouldShow = true\n  1258\t\n  1259\t        -- 检查插件按钮是否应该显示（必须已加载才显示）\n  1260\t        if buttonData.name == \&quot;biaoge\&quot; then\n  1261\t            shouldShow = WChat_Config.ShowBiaoGeButton and IsAddOnLoaded(\&quot;BiaoGe\&quot;)\n  1262\t        elseif buttonData.name == \&quot;biaogeai\&quot; then\n  1263\t            shouldShow = WChat_Config.ShowBiaoGeAIButton and IsAddOnLoaded(\&quot;BiaoGeAI\&quot;)\n  1264\t        elseif buttonData.name == \&quot;atlas\&quot; then\n  1265\t            shouldShow = WChat_Config.ShowAtlasButton and IsAddOnLoaded(\&quot;AtlasLootClassic\&quot;)\n  1266\t        elseif buttonData.name == \&quot;meetinghorn\&quot; then\n  1267\t            shouldShow = WChat_Config.ShowMeetingHornButton and IsAddOnLoaded(\&quot;MeetingHorn\&quot;)\n  1268\t        end\n  1269\t\n  1270\t        if shouldShow then\n  1271\t            CreateChannelButton(buttonData, buttonIndex)\n  1272\t            buttonIndex = buttonIndex + 1\n  1273\t        end\n  1274\t    end\n  1275\t\n  1276\t    -- 延迟更新频道X图标状态\n  1277\t    C_Timer.After(0.5, UpdateChannelXIcons)\n  1278\t\n  1279\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 聊天条加载完毕\&quot;)\n  1280\tend\n  1281\t\n  1282\t-- 添加StatReport函数\n  1283\tfunction WChat:StatReport()\n  1284\t    -- 属性报告功能 (参考alaChat实现)\n  1285\t    local function GetItemLevel()\n  1286\t        local slots = { 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15 }\n  1287\t        local playerClass = UnitClassBase('player')\n  1288\t        if playerClass ~= \&quot;DRUID\&quot; and playerClass ~= \&quot;PALADIN\&quot; and playerClass ~= \&quot;SHAMAN\&quot; then\n  1289\t            slots[#slots + 1] = 18 -- 远程武器槽\n  1290\t        end\n  1291\t        slots[#slots + 1] = 16 -- 主手\n  1292\t        slots[#slots + 1] = 17 -- 副手\n  1293\t\n  1294\t        local total = 0\n  1295\t        local num = 0\n  1296\t        for index = 1, #slots do\n  1297\t            local slot = slots[index]\n  1298\t            local item = GetInventoryItemLink('player', slot)\n  1299\t            if item ~= nil and item ~= \&quot;\&quot; then\n  1300\t                local _, _, _, level, _, _, _, _, loc = GetItemInfo(item)\n  1301\t                if level ~= nil then\n  1302\t                    total = total + level\n  1303\t                    num = num + 1\n  1304\t                end\n  1305\t                if slot == 16 and loc == \&quot;INVTYPE_2HWEAPON\&quot; then\n  1306\t                    break -- 双手武器不计算副手\n  1307\t                end\n  1308\t            end\n  1309\t        end\n  1310\t        if num == 0 then return nil end\n  1311\t        local lvl = total / num + 0.05\n  1312\t        return math.floor(lvl * 10) / 10\n  1313\t    end\n  1314\t\n  1315\t    local function GetTalentDesc()\n  1316\t        local _, n1, _, _, p1 = GetTalentTabInfo(1)\n  1317\t        local _, n2, _, _, p2 = GetTalentTabInfo(2)\n  1318\t        local _, n3, _, _, p3 = GetTalentTabInfo(3)\n  1319\t        if p1 == p2 or p2 == p3 or p1 == p3 then\n  1320\t            return \&quot;天赋 (\&quot; .. p1 .. \&quot;/\&quot; .. p2 .. \&quot;/\&quot; .. p3 .. \&quot;)\&quot;\n  1321\t        elseif p1 &gt; p2 and p1 &gt; p3 then\n  1322\t            return \&quot;天赋 \&quot; .. n1 .. \&quot;(\&quot; .. p1 .. \&quot;/\&quot; .. p2 .. \&quot;/\&quot; .. p3 .. \&quot;)\&quot;\n  1323\t        elseif p2 &gt; p1 and p2 &gt; p3 then\n  1324\t            return \&quot;天赋 \&quot; .. n2 .. \&quot;(\&quot; .. p1 .. \&quot;/\&quot; .. p2 .. \&quot;/\&quot; .. p3 .. \&quot;)\&quot;\n  1325\t        else\n  1326\t            return \&quot;天赋 \&quot; .. n3 .. \&quot;(\&quot; .. p1 .. \&quot;/\&quot; .. p2 .. \&quot;/\&quot; .. p3 .. \&quot;)\&quot;\n  1327\t        end\n  1328\t    end\n  1329\t\n  1330\t    local function GetStatReport()\n  1331\t        local&lt;response clipped&gt;&lt;NOTE&gt;To save on context only part of this file has been shown to you.&lt;/NOTE&gt;\nTotal lines in file: 1605\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;05ce5fc6-7aa0-46e6-9dcc-9374eee8f605;toolu_vrtx_01396ZibeUTnDRrNbgGhdieT&quot;:{&quot;requestId&quot;:&quot;05ce5fc6-7aa0-46e6-9dcc-9374eee8f605&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01396ZibeUTnDRrNbgGhdieT&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on Moudel/WChat.lua:\n  1330\t    local function GetStatReport()\n  1331\t        local ilv = GetItemLevel()\n  1332\t        local class, file = UnitClass('player')\n  1333\t        local health = UnitHealthMax('player')\n  1334\t        local mana = UnitPowerMax('player', 0)\n  1335\t\n  1336\t        -- 基础信息\n  1337\t        local report = class .. \&quot;, \&quot; .. \&quot;等级\&quot; .. UnitLevel('player') .. \&quot;, \&quot; .. GetTalentDesc()\n  1338\t        if ilv then\n  1339\t            report = report .. \&quot;, 装等 \&quot; .. ilv\n  1340\t        end\n  1341\t        report = report .. \&quot;, 生命 \&quot; .. health\n  1342\t        if mana &gt; 0 then\n  1343\t            report = report .. \&quot;, 法力 \&quot; .. mana\n  1344\t        end\n  1345\t\n  1346\t        -- 根据职业添加特定属性\n  1347\t        if file == \&quot;WARRIOR\&quot; then\n  1348\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1349\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1350\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1351\t            if p3 &gt;= p1 and p3 &gt;= p2 then\n  1352\t                -- 防护战士\n  1353\t                local _, armor = UnitArmor('player')\n  1354\t                local pChance = GetParryChance()\n  1355\t                local dChance = GetDodgeChance()\n  1356\t                local bChance = GetBlockChance()\n  1357\t                local block = GetShieldBlock()\n  1358\t                report = report .. \&quot;, 护甲 \&quot; .. armor\n  1359\t                report = report .. \&quot;, 招架 \&quot; .. string.format(\&quot;%.1f\&quot;, pChance) .. \&quot;%\&quot;\n  1360\t                report = report .. \&quot;, 躲闪 \&quot; .. string.format(\&quot;%.1f\&quot;, dChance) .. \&quot;%\&quot;\n  1361\t                report = report .. \&quot;, 格挡 \&quot; .. string.format(\&quot;%.1f\&quot;, bChance) .. \&quot;% (\&quot; .. block .. \&quot;)\&quot;\n  1362\t            else\n  1363\t                -- 输出战士\n  1364\t                local apBase, apPos, apNeg = UnitAttackPower('player')\n  1365\t                local critChance = GetCritChance()\n  1366\t                report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1367\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1368\t            end\n  1369\t        elseif file == \&quot;HUNTER\&quot; then\n  1370\t            local rapBase, rapPos, rapNeg = UnitRangedAttackPower('player')\n  1371\t            local critChance = GetRangedCritChance()\n  1372\t            report = report .. \&quot;, 远攻 \&quot; .. (rapBase + rapPos + rapNeg)\n  1373\t            report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1374\t        elseif file == \&quot;MAGE\&quot; or file == \&quot;WARLOCK\&quot; then\n  1375\t            local sp = GetSpellBonusDamage(2)\n  1376\t            local critChance = GetSpellCritChance(2)\n  1377\t            report = report .. \&quot;, 法强 \&quot; .. sp\n  1378\t            report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1379\t        elseif file == \&quot;PRIEST\&quot; then\n  1380\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1381\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1382\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1383\t            if p3 &gt;= p1 and p3 &gt;= p2 then\n  1384\t                -- 暗牧\n  1385\t                local sp = GetSpellBonusDamage(2)\n  1386\t                local critChance = GetSpellCritChance(2)\n  1387\t                report = report .. \&quot;, 法强 \&quot; .. sp\n  1388\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1389\t            else\n  1390\t                -- 治疗牧师\n  1391\t                local heal = GetSpellBonusHealing()\n  1392\t                local critChance = GetSpellCritChance(2)\n  1393\t                report = report .. \&quot;, 治疗 \&quot; .. heal\n  1394\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1395\t            end\n  1396\t        elseif file == \&quot;PALADIN\&quot; then\n  1397\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1398\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1399\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1400\t            if p1 &gt;= p2 and p1 &gt;= p3 then\n  1401\t                -- 神圣骑士\n  1402\t                local heal = GetSpellBonusHealing()\n  1403\t                local critChance = GetSpellCritChance(2)\n  1404\t                report = report .. \&quot;, 治疗 \&quot; .. heal\n  1405\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1406\t            elseif p2 &gt;= p1 and p2 &gt;= p3 then\n  1407\t                -- 防护骑士\n  1408\t                local _, armor = UnitArmor('player')\n  1409\t                local pChance = GetParryChance()\n  1410\t                local dChance = GetDodgeChance()\n  1411\t                local bChance = GetBlockChance()\n  1412\t                local block = GetShieldBlock()\n  1413\t                report = report .. \&quot;, 护甲 \&quot; .. armor\n  1414\t                report = report .. \&quot;, 招架 \&quot; .. string.format(\&quot;%.1f\&quot;, pChance) .. \&quot;%\&quot;\n  1415\t                report = report .. \&quot;, 躲闪 \&quot; .. string.format(\&quot;%.1f\&quot;, dChance) .. \&quot;%\&quot;\n  1416\t                report = report .. \&quot;, 格挡 \&quot; .. string.format(\&quot;%.1f\&quot;, bChance) .. \&quot;% (\&quot; .. block .. \&quot;)\&quot;\n  1417\t            else\n  1418\t                -- 惩戒骑士\n  1419\t                local apBase, apPos, apNeg = UnitAttackPower('player')\n  1420\t                local critChance = GetCritChance()\n  1421\t                report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1422\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1423\t            end\n  1424\t        elseif file == \&quot;ROGUE\&quot; then\n  1425\t            local apBase, apPos, apNeg = UnitAttackPower('player')\n  1426\t            local critChance = GetCritChance()\n  1427\t            report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1428\t            report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1429\t        elseif file == \&quot;DRUID\&quot; then\n  1430\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1431\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1432\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1433\t            if p1 &gt; p2 and p1 &gt;= p3 then\n  1434\t                -- 平衡德鲁伊\n  1435\t                local sp = GetSpellBonusDamage(4) -- 自然法术\n  1436\t                local critChance = GetSpellCritChance(4)\n  1437\t                report = report .. \&quot;, 法强 \&quot; .. sp\n  1438\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1439\t            elseif p3 &gt;= p1 and p3 &gt;= p2 then\n  1440\t                -- 恢复德鲁伊\n  1441\t                local heal = GetSpellBonusHealing()\n  1442\t                local critChance = GetSpellCritChance(4)\n  1443\t                report = report .. \&quot;, 治疗 \&quot; .. heal\n  1444\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1445\t            else\n  1446\t                -- 野性德鲁伊 (需要根据形态判断)\n  1447\t                local apBase, apPos, apNeg = UnitAttackPower('player')\n  1448\t                local critChance = GetCritChance()\n  1449\t                report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1450\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1451\t            end\n  1452\t        elseif file == \&quot;SHAMAN\&quot; then\n  1453\t            local _, _, _, _, p1 = GetTalentTabInfo(1)\n  1454\t            local _, _, _, _, p2 = GetTalentTabInfo(2)\n  1455\t            local _, _, _, _, p3 = GetTalentTabInfo(3)\n  1456\t            if p1 &gt; p2 and p1 &gt;= p3 then\n  1457\t                -- 元素萨满\n  1458\t                local sp = GetSpellBonusDamage(4) -- 自然法术\n  1459\t                local critChance = GetSpellCritChance(4)\n  1460\t                report = report .. \&quot;, 法强 \&quot; .. sp\n  1461\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1462\t            elseif p2 &gt;= p1 and p2 &gt;= p3 then\n  1463\t                -- 增强萨满\n  1464\t                local apBase, apPos, apNeg = UnitAttackPower('player')\n  1465\t                local critChance = GetCritChance()\n  1466\t                report = report .. \&quot;, 攻强 \&quot; .. (apBase + apPos + apNeg)\n  1467\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1468\t            else\n  1469\t                -- 恢复萨满\n  1470\t                local heal = GetSpellBonusHealing()\n  1471\t                local critChance = GetSpellCritChance(4)\n  1472\t                report = report .. \&quot;, 治疗 \&quot; .. heal\n  1473\t                report = report .. \&quot;, 暴击 \&quot; .. string.format(\&quot;%.1f\&quot;, critChance) .. \&quot;%\&quot;\n  1474\t            end\n  1475\t        end\n  1476\t\n  1477\t        return report\n  1478\t    end\n  1479\t\n  1480\t\n  1481\t    -- 使用新的详细属性报告\n  1482\t    return GetStatReport()\n  1483\tend\n  1484\t\n  1485\t\n  1486\t\n  1487\t-- 刷新聊天条按钮（当插件加载状态改变时调用）\n  1488\tfunction WChat:RefreshChatBarButtons()\n  1489\t    if not WChatBar then return end\n  1490\t\n  1491\t    -- 隐藏所有现有按钮\n  1492\t    for i = 1, WChatBar:GetNumChildren() do\n  1493\t        local child = select(i, WChatBar:GetChildren())\n  1494\t        if child and child:GetObjectType() == \&quot;Button\&quot; then\n  1495\t            child:Hide()\n  1496\t        end\n  1497\t    end\n  1498\t\n  1499\t    -- 重新创建按钮\n  1500\t    local buttonIndex = 1\n  1501\t    for i = 1, #ChannelButtons do\n  1502\t        local buttonData = ChannelButtons[i]\n  1503\t        local shouldShow = true\n  1504\t\n  1505\t        -- 检查插件按钮是否应该显示（必须已加载才显示）\n  1506\t        if buttonData.name == \&quot;biaoge\&quot; then\n  1507\t            shouldShow = WChat_Config.ShowBiaoGeButton and IsAddOnLoaded(\&quot;BiaoGe\&quot;)\n  1508\t        elseif buttonData.name == \&quot;biaogeai\&quot; then\n  1509\t            shouldShow = WChat_Config.ShowBiaoGeAIButton and IsAddOnLoaded(\&quot;BiaoGeAI\&quot;)\n  1510\t        elseif buttonData.name == \&quot;atlas\&quot; then\n  1511\t            shouldShow = WChat_Config.ShowAtlasButton and IsAddOnLoaded(\&quot;AtlasLootClassic\&quot;)\n  1512\t        elseif buttonData.name == \&quot;meetinghorn\&quot; then\n  1513\t            shouldShow = WChat_Config.ShowMeetingHornButton and IsAddOnLoaded(\&quot;MeetingHorn\&quot;)\n  1514\t        end\n  1515\t\n  1516\t        if shouldShow then\n  1517\t            CreateChannelButton(buttonData, buttonIndex)\n  1518\t            buttonIndex = buttonIndex + 1\n  1519\t        end\n  1520\t    end\n  1521\t\n  1522\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 聊天条按钮已刷新\&quot;)\n  1523\tend\n  1524\t\n  1525\t-- 模块初始化函数\n  1526\tfunction WChat:Initialize()\n  1527\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 开始初始化聊天增强模块...\&quot;)\n  1528\t\n  1529\t    -- 逐步初始化各个子模块，便于排查问题\n  1530\t    local success, err = pcall(function()\n  1531\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化频道功能...\&quot;)\n  1532\t        self:InitChannel()\n  1533\t\n  1534\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化表情功能...\&quot;)\n  1535\t        self:InitEmoteTableFrame()\n  1536\t\n  1537\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化聊天条...\&quot;)\n  1538\t        self:InitChatBar()\n  1539\t\n  1540\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 初始化时间戳复制...\&quot;)\n  1541\t        self:InitTimestampCopy()\n  1542\t    end)\n  1543\t\n  1544\t    if success then\n  1545\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 聊天增强模块加载完成\&quot;)\n  1546\t    else\n  1547\t        print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 模块初始化失败: \&quot; .. tostring(err))\n  1548\t    end\n  1549\tend\n  1550\t\n  1551\t-- 配置保存和加载\n  1552\tfunction WChat:LoadConfig()\n  1553\t    if WChatDB then\n  1554\t        for key, value in pairs(WChatDB) do\n  1555\t            if WChat_Config[key] ~= nil then\n  1556\t                WChat_Config[key] = value\n  1557\t            end\n  1558\t        end\n  1559\t    end\n  1560\tend\n  1561\t\n  1562\tfunction WChat:SaveConfig()\n  1563\t    WChatDB = {}\n  1564\t    for key, value in pairs(WChat_Config) do\n  1565\t        WChatDB[key] = value\n  1566\t    end\n  1567\tend\n  1568\t\n  1569\t-- 防止重复初始化\n  1570\tlocal WChat_Initialized = false\n  1571\t\n  1572\t-- 事件处理\n  1573\tlocal WChatFrame = CreateFrame(\&quot;Frame\&quot;)\n  1574\tWChatFrame:RegisterEvent(\&quot;PLAYER_LOGOUT\&quot;)\n  1575\tWChatFrame:SetScript(\&quot;OnEvent\&quot;, function(self, event, addonName)\n  1576\t    if event == \&quot;PLAYER_LOGOUT\&quot; then\n  1577\t        WChat:SaveConfig()\n  1578\t    end\n  1579\tend)\n  1580\t\n  1581\t-- 注册到WanTiny模块系统\n  1582\tif WanTiny_RegisterModule then\n  1583\t    WanTiny_RegisterModule(\&quot;WChat\&quot;, function()\n  1584\t        if not WChat_Initialized then\n  1585\t            WChat_Initialized = true\n  1586\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r WChat模块开始注册...\&quot;)\n  1587\t\n  1588\t            local success, err = pcall(function()\n  1589\t                WChat:LoadConfig()\n  1590\t                WChat:Initialize()\n  1591\t            end)\n  1592\t            if not success then\n  1593\t                print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r 模块加载失败: \&quot; .. tostring(err))\n  1594\t            end\n  1595\t        end\n  1596\t    end)\n  1597\telse\n  1598\t    print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r WanTiny_RegisterModule 函数不存在\&quot;)\n  1599\tend\n  1600\t\n  1601\t\n  1602\t\n  1603\t\n  1604\t\n  1605\t\nTotal lines in file: 1605\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;a835dba3-0b06-4603-9d75-d872757484e5;toolu_vrtx_01TJFVuqQWwJUJv9YkS2wFC9&quot;:{&quot;requestId&quot;:&quot;a835dba3-0b06-4603-9d75-d872757484e5&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01TJFVuqQWwJUJv9YkS2wFC9&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: Moudel/WChat.lua\n...\n     7\t\n     8\t-- 模块配置\n     9\tlocal WChat = {}\n    10\tlocal WChat_Config = {\n    11\t    -- 默认配置\n    12\t    ShortChannel = true,        -- 频道标签精简\n    13\t    EnableEmoteInput = true,    -- 启用表情输入\n    14\t    EmoteIconSize = 16,         -- 表情图标大小\n    15\t    EmoteIconListSize = 20,     -- 表情列表图标大小\n    16\t    EmoteOffsetX = 0,           -- 表情面板X偏移\n    17\t    EmoteOffsetY = 30,          -- 表情面板Y偏移\n    18\t    UseVertical = false,        -- 聊天条垂直布局\n    19\t    UseTopChatbar = false,      -- 聊天条在上方\n    20\t    UseTopInput = false,        -- 输入框在上方\n    21\t    ChatBarOffsetX = 0,         -- 聊天条X偏移\n    22\t    ChatBarOffsetY = 0,         -- 聊天条Y偏移\n    23\t    DistanceVertical = -25,     -- 垂直间距\n    24\t    DistanceHorizontal = 25,    -- 水平间距\n    25\t    AlphaOnEnter = 1.0,         -- 鼠标悬停透明度\n    26\t    AlphaOnLeave = 0.7,         -- 鼠标离开透明度\n    27\t    Position = nil,             -- 聊天条位置\n    28\t    EnableTimestampCopy = true, -- 启用时间戳点击复制\n    29\t    TimestampFormat = \&quot;%H:%M:%S\&quot;, -- 时间戳格式\n    30\t    TimestampColor = { 1.0, 1.0, 1.0 }, -- 时间戳颜色\n    31\t    -- 插件按钮显示控制\n    32\t    ShowBiaoGeButton = true,    -- 显示BiaoGe按钮\n    33\t    ShowBiaoGeAIButton = true,  -- 显示BiaoGeAI按钮\n    34\t    ShowAtlasButton = true,     -- 显示Atlas按钮\n    35\t    ShowMeetingHornButton = true, -- 显示MeetingHorn按钮\n    36\t    -- 频道隐藏设置\n    37\t    HiddenChannels = {}\n    38\t}\n    39\t\n    40\t-- 频道配置映射\n    41\tlocal CHANNEL_CONFIG = {\n    42\t    MAPPINGS = {\n    43\t        chnGen = \&quot;综合\&quot;,\n    44\t        chnTrade = \&quot;交易\&quot;,\n    45\t        chnLFG = \&quot;寻求组队\&quot;,\n    46\t        world = \&quot;大脚世界频道\&quot;\n    47\t    }\n    48\t}\n    49\t\n    50\t-- 频道显示/隐藏管理函数\n    51\tlocal function IsChannelShown(channelName)\n    52\t    local channels = {GetChatWindowChannels(DEFAULT_CHAT_FRAME:GetID() or 1)}\n    53\t    for i = 1, #channels, 2 do\n    54\t        if channels[i] == channelName then return true end\n    55\t    end\n    56\t    return false\n    57\tend\n...\n   530\t\n   531\tlocal cycles = {\n   532\t        -- \&quot;说\&quot;\n   533\t        {\n   534\t            chatType = \&quot;SAY\&quot;,\n   535\t            use = function(self, editbox)\n   536\t                return 1\n   537\t            end\n   538\t        },\n   539\t        --大喊\n   540\t        {\n   541\t            chatType = \&quot;YELL\&quot;,\n   542\t            use = function(self, editbox)\n   543\t                return 1\n   544\t            end\n   545\t        },\n   546\t        --小队\n   547\t        {\n   548\t            chatType = \&quot;PARTY\&quot;,\n   549\t            use = function(self, editbox)\n   550\t                return IsInGroup()\n   551\t            end\n   552\t        },\n   553\t        --团队\n   554\t        {\n   555\t            chatType = \&quot;RAID\&quot;,\n   556\t            use = function(self, editbox)\n   557\t                return IsInRaid()\n   558\t            end\n   559\t        },\n   560\t        --实时聊天\n   561\t        {\n   562\t            chatType = \&quot;INSTANCE_CHAT\&quot;,\n   563\t            use = function(self, editbox)\n   564\t                return select(2, IsInInstance()) == \&quot;pvp\&quot;\n   565\t            end\n   566\t        },\n   567\t        --公会\n   568\t        {\n   569\t            chatType = \&quot;GUILD\&quot;,\n   570\t            use = function(self, editbox)\n   571\t                return IsInGuild()\n   572\t            end\n   573\t        },\n...\n   605\t\n   606\tlocal chatTypeBeforeSwitch, tellTargetBeforeSwitch --记录在频道和密语之间切换时的状态\n   607\tfunction ChatEdit_CustomTabPressed_Inner(self)\n   608\t    if strsub(tostring(self:GetText()), 1, 1) == \&quot;/\&quot; then\n   609\t        return\n   610\t    end\n   611\t    local currChatType = self:GetAttribute(\&quot;chatType\&quot;)\n   612\t    if (IsControlKeyDown()) then\n   613\t        if (currChatType == \&quot;WHISPER\&quot; or currChatType == \&quot;BN_WHISPER\&quot;) then\n   614\t            --记录之前的密语对象，以便后续切回\n   615\t            self:SetAttribute(\&quot;chatType\&quot;, chatTypeBeforeSwitch or \&quot;SAY\&quot;)\n   616\t            ChatEdit_UpdateHeader(self)\n   617\t            chatTypeBeforeSwitch = \&quot;WHISPER\&quot;\n   618\t            tellTargetBeforeSwitch = self:GetAttribute(\&quot;tellTarget\&quot;)\n   619\t            return --这里和下面不同，这里可以不返回true\n   620\t        else\n   621\t            local newTarget, newTargetType = ChatEdit_GetNextTellTarget()\n   622\t            if tellTargetBeforeSwitch or (newTarget and newTarget ~= \&quot;\&quot;) then\n...\n   789\t\n   790\t-----------------------------聊天条\n   791\t\n   792\t--[[=========================== 变量区 ==========================]]\n   793\t-- 是否可移动的标记\n   794\tlocal IsMovable = false -- 没事干别动这个，你改成ture那么进入游戏后聊天条就是可以移动的\n   795\t--[[=============================== END ==============================]]\n   796\tlocal chatFrame = SELECTED_DOCK_FRAME -- 聊天框架\n   797\tlocal inputbox = chatFrame.editBox -- 输入框\n   798\t\n   799\tCOLORSCHEME_BORDER = {0.3, 0.3, 0.3, 1}\n   800\t-- 边框颜色\n   801\t-- 主框架初始化\n   802\tlocal ChatBar = CreateFrame(\&quot;Frame\&quot;, nil, UIParent, BackdropTemplateMixin and \&quot;BackdropTemplate\&quot; or nil)\n   803\tWChatBar = ChatBar\n   804\t\n   805\tlocal function ChannelSay_OnClick()\n   806\t    ChatFrame_OpenChat(\&quot;/s \&quot; .. inputbox:GetText(), chatFrame)\n   807\tend\n   808\t\n   809\tlocal function ChannelYell_OnClick()\n   810\t    ChatFrame_OpenChat(\&quot;/y \&quot; .. inputbox:GetText(), chatFrame)\n   811\tend\n...\n  1067\t\n  1068\t            if relativeTo then\n  1069\t                WChat_Config.Position = {point = point, relativeTo = relativeTo:GetName(), relativePoint = relativePoint, xOfs = xOfs, yOfs = yOfs}\n  1070\t            else\n  1071\t                WChat_Config.Position = {point = point, relativeTo = nil, relativePoint = relativePoint, xOfs = xOfs, yOfs = yOfs}\n  1072\t            end\n  1073\t        else\n  1074\t            print(\&quot;|cffffe00a&lt;|r|cffff7d0aWChat|r|cffffe00a&gt;|r |cff00d200解锁聊天条|r\&quot;)\n  1075\t            IsMovable = true\n  1076\t            WChatBar:SetBackdrop(\n  1077\t                {\n  1078\t                    bgFile = \&quot;Interface/DialogFrame/UI-DialogBox-Background\&quot;,\n  1079\t                    edgeFile = \&quot;Interface/DialogFrame/UI-DialogBox-Border\&quot;,\n  1080\t                    tile = true,\n  1081\t                    tileSize = 16,\n  1082\t                    edgeSize = 16,\n  1083\t                    insets = {left = 4, right = 4, top = 4, bottom = 4}\n  1084\t                }\n  1085\t        )\n  1086\t        end\n  1087\t        WChatBar:EnableMouse(IsMovable)\n  1088\t    elseif button == \&quot;MiddleButton\&quot; then\n  1089\t        if IsMovable == false then\n  1090\t            return\n  1091\t        end\n  1092\t        WChatBar:ClearAllPoints()\n  1093\t        if WChat_Config.UseVertical then\n  1094\t            if WChat_Config.UseTopChatbar then\n  1095\t                WChatBar:SetPoint(\&quot;TOPRIGHT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX - 30, WChat_Config.ChatBarOffsetY + 25)\n  1096\t            else\n  1097\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPRIGHT\&quot;, WChat_Config.ChatBarOffsetX + 30, WChat_Config.ChatBarOffsetY + 25)\n  1098\t            end\n  1099\t        else\n  1100\t            if WChat_Config.UseTopChatbar then\n  1101\t                WChatBar:SetPoint(\&quot;BOTTOMLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY + 30)\n  1102\t            else\n  1103\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;BOTTOMLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY - 30)\n  1104\t            end\n  1105\t        end\n  1106\t    end\n  1107\tend\n...\n  1162\t    if WChat_Config.UseVertical then\n  1163\t        frame:SetPoint(\&quot;TOP\&quot;, WChatBar, \&quot;TOP\&quot;, 0, (1 - index) * WChat_Config.DistanceVertical)\n  1164\t    else\n  1165\t        frame:SetPoint(\&quot;LEFT\&quot;, WChatBar, \&quot;LEFT\&quot;, 10 + (index - 1) * WChat_Config.DistanceHorizontal, 0)\n  1166\t    end\n  1167\t    \n  1168\t    frame:RegisterForClicks(\&quot;AnyUp\&quot;)\n  1169\t    frame:SetScript(\&quot;OnClick\&quot;, data.callback)\n  1170\t    -- 显示的文字\n  1171\t    frameText = frame:CreateFontString(data.name .. \&quot;Text\&quot;, \&quot;OVERLAY\&quot;)\n  1172\t    -- 字体设置\n  1173\t    frameText:SetFont(STANDARD_TEXT_FONT, 15, \&quot;OUTLINE\&quot;)\n  1174\t    \n  1175\t    frameText:SetJustifyH(\&quot;CENTER\&quot;)\n  1176\t    frameText:SetWidth(26)\n  1177\t    frameText:SetHeight(26)\n  1178\t    frameText:SetText(data.text)\n  1179\t    frameText:SetPoint(\&quot;CENTER\&quot;, 0, 0)\n  1180\t    \n  1181\t    -- 文字按钮的颜色\n  1182\t    frameText:SetTextColor(data.color[1], data.color[2], data.color[3])\n...\n  1199\t\n  1200\tfunction WChat:InitChatBar()\n  1201\t\n  1202\t    WChatBar:SetFrameLevel(0)\n  1203\t\n  1204\t    -- 使用竖直布局\n  1205\t    if WChat_Config.UseVertical then\n  1206\t        -- 主框体宽度\n  1207\t        WChatBar:SetWidth(30)\n  1208\t        -- 主框体高度\n  1209\t        WChatBar:SetHeight(#ChannelButtons * WChat_Config.DistanceVertical + 10)\n  1210\t    else\n  1211\t        -- 主框体宽度\n  1212\t        WChatBar:SetWidth(#ChannelButtons * WChat_Config.DistanceHorizontal + 10)\n  1213\t        -- 主框体高度\n  1214\t        WChatBar:SetHeight(30)\n  1215\t    end\n  1216\t\n  1217\t    -- 上方聊天输入框\n  1218\t    if WChat_Config.UseTopInput then\n  1219\t        inputbox:ClearAllPoints()\n  1220\t        inputbox:SetPoint(\&quot;BOTTOMLEFT\&quot;, chatFrame, \&quot;TOPLEFT\&quot;, 0, 20)\n  1221\t        inputbox:SetPoint(\&quot;BOTTOMRIGHT\&quot;, chatFrame, \&quot;TOPRIGHT\&quot;, 0, 20)\n  1222\t    end\n  1223\t    \n  1224\t    -- 位置设定\n  1225\t    if WChat_Config.Position == nil then\n  1226\t        if WChat_Config.UseVertical then\n  1227\t            if WChat_Config.UseTopChatbar then\n  1228\t                WChatBar:SetPoint(\&quot;TOPRIGHT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX - 30, WChat_Config.ChatBarOffsetY + 25)\n  1229\t            else\n  1230\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPRIGHT\&quot;, WChat_Config.ChatBarOffsetX + 30, WChat_Config.ChatBarOffsetY + 25)\n  1231\t            end\n  1232\t        else\n  1233\t            if WChat_Config.UseTopChatbar then\n  1234\t                WChatBar:SetPoint(\&quot;BOTTOMLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;TOPLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY + 30)\n  1235\t            else\n  1236\t                WChatBar:SetPoint(\&quot;TOPLEFT\&quot;, \&quot;ChatFrame1\&quot;, \&quot;BOTTOMLEFT\&quot;, WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY - 30)\n  1237\t            end\n  1238\t        end\n  1239\t    else\n  1240\t        local point = WChat_Config.Position.point\n  1241\t        local relativeTo = WChat_Config.Position.relativeTo\n...\n  1252\t    \n  1253\t    -- 动态创建按钮，根据配置和插件存在性\n  1254\t    local buttonIndex = 1\n  1255\t    for i = 1, #ChannelButtons do\n  1256\t        local buttonData = ChannelButtons[i]\n  1257\t        local shouldShow = true\n  1258\t\n  1259\t        -- 检查插件按钮是否应该显示（必须已加载才显示）\n  1260\t        if buttonData.name == \&quot;biaoge\&quot; then\n  1261\t            shouldShow = WChat_Config.ShowBiaoGeButton and IsAddOnLoaded(\&quot;BiaoGe\&quot;)\n  1262\t        elseif buttonData.name == \&quot;biaogeai\&quot; then\n  1263\t            shouldShow = WChat_Config.ShowBiaoGeAIButton and IsAddOnLoaded(\&quot;BiaoGeAI\&quot;)\n  1264\t        elseif buttonData.name == \&quot;atlas\&quot; then\n  1265\t            shouldShow = WChat_Config.ShowAtlasButton and IsAddOnLoaded(\&quot;AtlasLootClassic\&quot;)\n  1266\t        elseif buttonData.name == \&quot;meetinghorn\&quot; then\n  1267\t            shouldShow = WChat_Config.ShowMeetingHornButton and IsAddOnLoaded(\&quot;MeetingHorn\&quot;)\n  1268\t        end\n  1269\t\n  1270\t        if shouldShow then\n  1271\t            CreateChannelButton(buttonData, buttonIndex)\n  1272\t            buttonIndex = buttonIndex + 1\n  1273\t        end\n  1274\t    end\n...\n  1498\t\n  1499\t    -- 重新创建按钮\n  1500\t    local buttonIndex = 1\n  1501\t    for i = 1, #ChannelButtons do\n  1502\t        local buttonData = ChannelButtons[i]\n  1503\t        local shouldShow = true\n  1504\t\n  1505\t        -- 检查插件按钮是否应该显示（必须已加载才显示）\n  1506\t        if buttonData.name == \&quot;biaoge\&quot; then\n  1507\t            shouldShow = WChat_Config.ShowBiaoGeButton and IsAddOnLoaded(\&quot;BiaoGe\&quot;)\n  1508\t        elseif buttonData.name == \&quot;biaogeai\&quot; then\n  1509\t            shouldShow = WChat_Config.ShowBiaoGeAIButton and IsAddOnLoaded(\&quot;BiaoGeAI\&quot;)\n  1510\t        elseif buttonData.name == \&quot;atlas\&quot; then\n  1511\t            shouldShow = WChat_Config.ShowAtlasButton and IsAddOnLoaded(\&quot;AtlasLootClassic\&quot;)\n  1512\t        elseif buttonData.name == \&quot;meetinghorn\&quot; then\n  1513\t            shouldShow = WChat_Config.ShowMeetingHornButton and IsAddOnLoaded(\&quot;MeetingHorn\&quot;)\n  1514\t        end\n  1515\t\n  1516\t        if shouldShow then\n  1517\t            CreateChannelButton(buttonData, buttonIndex)\n  1518\t            buttonIndex = buttonIndex + 1\n  1519\t        end\n  1520\t    end\n...\nPath: WanTinyUI.lua\n...\n   627\t\n   628\t    -- 创建模块复选框\n   629\t    ControlFactory.CreateGrid(parent, MODULE_CONFIGS, 4, WanTinyUI.LAYOUT.CHECKBOX_WIDTH, currentY, function(parent, config, x, y)\n   630\t        local moduleName, displayName, description = config[1], config[2], config[3]\n   631\t        local checkBox = ControlFactory.CreateCheckbox(parent, displayName, x, y,\n   632\t            enabledModules[moduleName] ~= false,\n   633\t            function(self) enabledModules[moduleName] = self:GetChecked() end, description)\n   634\t        return checkBox\n   635\t    end)\n...\n   650\t\n   651\t    local function updateMinimap()\n   652\t        C_Timer.After(0.1, function()\n   653\t            if _G.WanTiny_RestoreAllCollectedButtons then _G.WanTiny_RestoreAllCollectedButtons() end\n   654\t            if WanTinyDB.Map.MiniButShouNa_YN == 1 and _G.WanTiny_ShowAllCollectedButtons then _G.WanTiny_ShowAllCollectedButtons() end\n   655\t        end)\n   656\t    end\n   657\t\n   658\t    local controlWidth, spacing = WanTinyUI.LAYOUT.SLIDER_WIDTH, WanTinyUI.LAYOUT.EXTRA_SPACING\n   659\t    local startX = LayoutUtils.GetCenteredStartXSafe(parent, controlWidth, 2, spacing)\n   660\t\n   661\t    -- 第1行：复选框和下拉框\n   662\t    local checkBox = CreateLabeledCheckButton(parent, \&quot;启用收纳\&quot;, startX, currentY,\n   663\t        WanTinyDB.Map.MiniButShouNa_YN==1,\n   664\t        function(self) WanTinyDB.Map.MiniButShouNa_YN = self:GetChecked() and 1 or 2; updateMinimap() end)\n...\n   816\t\n   817\t        local cfg = GetWanMenuConfig()\n   818\t        local checkboxStartX = LayoutUtils.GetCenteredStartXSafe(parent, WanTinyUI.LAYOUT.CHECKBOX_WIDTH, 3, 0)\n   819\t\n   820\t        -- 创建复选框（独立行）\n   821\t        for i, ctrl in ipairs(markControls) do\n   822\t            if ctrl.type == \&quot;checkbox\&quot; then\n   823\t                local x = checkboxStartX + (i-1) * WanTinyUI.LAYOUT.CHECKBOX_WIDTH\n   824\t                local value = cfg[ctrl.key] ~= false\n   825\t                CreateLabeledCheckButton(parent, ctrl.label, x, currentY, value, function(self) UpdateWanMenuConfig(ctrl.key, self:GetChecked()) end, ctrl.desc)\n   826\t            end\n   827\t        end\n   828\t        currentY = currentY - 35  -- 复选框占用1行\n   829\t\n   830\t        -- 创建滑块（独立行，两个滑块在同一行）\n   831\t        local sliderWidth = WanTinyUI.LAYOUT.SLIDER_WIDTH\n   832\t        local sliderSpacing = 30\n   833\t        local sliderStartX = LayoutUtils.GetCenteredStartXSafe(parent, sliderWidth * 2 + sliderSpacing, 1, 0)\n   834\t        local sliderIndex = 0\n...\n   866\t\n   867\t-- 【标签页4】创建WCombatTimes战斗计时器设置面板\n   868\tfunction WanTinyUI.CreateWCombatTimesPanel(parent)\n   869\t    if parent.wcombatPanelCreated then return end\n   870\t\n   871\t\n   872\t\n   873\t    parent.wcombatPanelCreated = true\n   874\t\n   875\t    -- 检查模块加载状态\n   876\t    local wcombatLoaded = checkModuleLoaded(\&quot;WCombatTimes\&quot;)\n   877\t\n   878\t    local ROW_HEIGHT = 35  -- 标准行高35px\n   879\t    local currentY = -10   -- 起始坐标\n   880\t\n   881\t    -- WCombatTimes 战斗计时器设置\n   882\t    ControlFactory.CreateTitle(parent, \&quot;|cffff6b6b战斗计时器设置|r\&quot;, currentY)\n   883\t    currentY = currentY - WanTinyUI.LAYOUT.TITLE_SPACING\n   884\t\n   885\t    if wcombatLoaded then\n   886\t        local config = _G.WCT.Config\n   887\t        local SetConfig = function(k,v) _G.WCT.SetConfig(k,v) end\n   888\t\n   889\t    local function CreateCheck(text, x, y, key, tip, callback)\n   890\t        return ControlFactory.CreateCheckbox(parent, text, x, y, config[key] ~= false,\n   891\t            callback or function(self) SetConfig(key, self:GetChecked()) end, tip)\n   892\t    end\n   893\t\n   894\t    local function CreateEditBox(key, x, y, tip, options)\n   895\t        options = options or {}\n   896\t        local hasLabel = options.label ~= nil\n   897\t        local containerHeight = hasLabel and ROW_HEIGHT * 2 or 20\n   898\t\n   899\t        local container = CreateFrame(\&quot;Frame\&quot;, nil, parent)\n   900\t        container:SetSize(options.width or 120, containerHeight)\n   901\t        container:SetPoint(\&quot;TOPLEFT\&quot;, parent, \&quot;TOPLEFT\&quot;, x, y - (hasLabel and 0 or 7))\n...\n   957\t\n   958\t    -- 创建复选框\n   959\t    local checkConfigs = {\n   960\t        {\&quot;|cff00ff00显示计时器|r\&quot;, \&quot;showMainFrame\&quot;, \&quot;启用或禁用主计时器框体\&quot;, function(self)\n   961\t            local checked = self:GetChecked()\n   962\t            SetConfig(\&quot;showMainFrame\&quot;, checked)\n   963\t            local frame = LayoutUtils.SafeGetGlobal(\&quot;WCombatTimesFrame\&quot;)\n   964\t            if frame and frame.Show and frame.Hide then\n   965\t                frame[checked and \&quot;Show\&quot; or \&quot;Hide\&quot;](frame)\n   966\t            end\n   967\t        end},\n   968\t        {\&quot;|cffff8000显示横幅|r\&quot;, \&quot;showBanner\&quot;, \&quot;进入/离开战斗时显示横幅提醒\&quot;},\n   969\t        {\&quot;|cff00bfff进战音效|r\&quot;, \&quot;playSoundOnEnter\&quot;, \&quot;进入战斗时播放声音提醒\&quot;},\n   970\t        {\&quot;|cff00bfff脱战音效|r\&quot;, \&quot;playSoundOnLeave\&quot;, \&quot;离开战斗时播放声音提醒\&quot;}\n   971\t    }\n   972\t\n   973\t    local checkBoxes = ControlFactory.CreateGrid(parent, checkConfigs, 4, WanTinyUI.LAYOUT.CHECKBOX_WIDTH, currentY, function(parent, cfg, x, y)\n   974\t        return CreateCheck(cfg[1], x, y, cfg[2], cfg[3], cfg[4])\n   975\t    end)\n   976\t    currentY = currentY - ROW_HEIGHT  -- 复选框占用1行\n   977\t\n   978\t    -- 创建输入框\n   979\t    local editConfigs = {\n   980\t        {\&quot;进战主文字\&quot;, \&quot;enterBannerTitle\&quot;},\n   981\t        {\&quot;进战次文字\&quot;, \&quot;enterBannerLabel\&quot;},\n   982\t        {\&quot;脱战主文字\&quot;, \&quot;leaveBannerTitle\&quot;}\n   983\t    }\n...\nPath: Moudel/WaPlus.lua\n...\n   181\t        local enableBtn = CreateFrame(\&quot;CheckButton\&quot;, nil, f, \&quot;ChatConfigCheckButtonTemplate\&quot;)\n   182\t        enableBtn:SetSize(20, 20)\n   183\t        enableBtn:SetPoint(\&quot;TOPLEFT\&quot;, 20, -35)\n   184\t        enableBtn.Text:SetText(\&quot;启用欢迎语\&quot;)\n   185\t        enableBtn.Text:ClearAllPoints()\n   186\t        enableBtn.Text:SetPoint(\&quot;LEFT\&quot;, enableBtn, \&quot;RIGHT\&quot;, 2, 2)\n   187\t        enableBtn:SetHitRectInsets(0, 0, 0, 0)\n   188\t        -- 频道单选按钮组（绝对定位，横向等距）\n   189\t        local channelConfig = {\n   190\t            labels = { {\&quot;|cFFE066FF密语|r\&quot;,\&quot;WHISPER\&quot;}, {\&quot;|cFFFFA500团队|r\&quot;,\&quot;RAID\&quot;}, {\&quot;|cFF66B3FF小队|r\&quot;,\&quot;PARTY\&quot;} },\n   191\t            btns = {}, baseX = 120, baseY = -35, spacing = 70\n   192\t        }\n   193\t        -- 启用/禁用频道选择（只控制可点，不再动态变灰，颜色由颜色码决定）\n   194\t        local function updateChannelEnable()\n   195\t            local enabled = enableBtn:GetChecked()\n   196\t            for _, b in ipairs(channelConfig.btns) do b:EnableMouse(enabled) end\n   197\t        end\n   198\t        enableBtn:SetScript(\&quot;OnClick\&quot;, function(self)\n   199\t            getWelcomeConfig().enabled = self:GetChecked()\n   200\t            updateChannelEnable()\n   201\t        end)\n   202\t        for i, v in ipairs(channelConfig.labels) do\n   203\t            local btn = CreateFrame(\&quot;CheckButton\&quot;, nil, f, \&quot;ChatConfigCheckButtonTemplate\&quot;)\n   204\t            btn:SetSize(20, 20)\n   205\t            btn:SetPoint(\&quot;TOPLEFT\&quot;, f, \&quot;TOPLEFT\&quot;, channelConfig.baseX + (i-1)*channelConfig.spacing, channelConfig.baseY)\n   206\t            btn.Text:ClearAllPoints(); btn.Text:SetPoint(\&quot;LEFT\&quot;, btn, \&quot;RIGHT\&quot;, 2, 2)\n   207\t            btn.Text:SetText(v[1])\n   208\t            btn:SetHitRectInsets(0, 0, 0, 0)\n   209\t            btn:SetScript(\&quot;OnClick\&quot;, function(self)\n...\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;9386cad7-d5e5-45ab-9c6e-d7b27448c854;toolu_vrtx_015G3qvKSiC6eYyqhveB6TUd&quot;:{&quot;requestId&quot;:&quot;9386cad7-d5e5-45ab-9c6e-d7b27448c854&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_015G3qvKSiC6eYyqhveB6TUd&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;No matches found for regex pattern: WChat.*垂直|UseVertical|聊天条.*垂直 in WanTinyUI.lua&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;60dc19e5-3a78-4190-83b2-98de0abd20b6;toolu_vrtx_01N28mWw2CqfGZkm2ma8fCt9&quot;:{&quot;requestId&quot;:&quot;60dc19e5-3a78-4190-83b2-98de0abd20b6&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01N28mWw2CqfGZkm2ma8fCt9&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Regex search results for pattern: WChat|聊天 in WanTinyUI.lua\nFound 1 matching lines:\n\n    662\t    local checkBox = CreateLabeledCheckButton(parent, \&quot;启用收纳\&quot;, startX, currentY,\n    663\t        WanTinyDB.Map.MiniButShouNa_YN==1,\n    664\t        function(self) WanTinyDB.Map.MiniButShouNa_YN = self:GetChecked() and 1 or 2; updateMinimap() end)\n    665\t\n    666\t    local dropDown = CreateLabeledDropDown(parent, \&quot;按钮位置：\&quot;, startX+controlWidth+spacing, currentY,\n&gt;   667\t        {[1]=\&quot;小地图\&quot;,[2]=\&quot;聊天框\&quot;,[3]=\&quot;自由\&quot;}, WanTinyDB.Map.MinimapPointMode,\n    668\t        function(k) WanTinyDB.Map.MinimapPointMode=k; if _G.WanTiny_MiniMapBut and _G.WanTiny_MiniMapBut.SetButtonStyle then _G.WanTiny_MiniMapBut:SetButtonStyle(k) end end)\n    669\t    currentY = currentY - 35  -- 第1行完成\n    670\t\n    671\t    -- 第2行：两个滑块\n    672\t    local slider1 = CreateLabeledSlider(parent, \&quot;每行按钮：\&quot;, startX, currentY, 3, 12, 1,\n\nTotal matches: 1\nTotal lines in file: 1186&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;c49fb4aa-7dea-4689-9a4a-a14d0bc08b07;toolu_vrtx_01V9XpgBBvC6HSAc5FMZgrtj&quot;:{&quot;requestId&quot;:&quot;c49fb4aa-7dea-4689-9a4a-a14d0bc08b07&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01V9XpgBBvC6HSAc5FMZgrtj&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;No matches found for regex pattern: CreateWChatPanel|WChatPanel|function.*WChat in WanTinyUI.lua&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;ab8240d1-deb9-4933-aad1-79851c382287;toolu_vrtx_011aB3ujoRbKhJMwFu4mccfm&quot;:{&quot;requestId&quot;:&quot;ab8240d1-deb9-4933-aad1-79851c382287&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_011aB3ujoRbKhJMwFu4mccfm&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Regex search results for pattern: function.*CreatePanel|function.*Panel in WanTinyUI.lua\nFound 4 matching lines:\n\n    613\t    {\&quot;WCombatTimes\&quot;, \&quot;|cffff6b6b战斗计时|r\&quot;, \&quot;显示战斗持续时间和战斗状态变化提醒，支持自定义横幅和声音\&quot;},\n    614\t\n    615\t}\n    616\t\n    617\t-- 【标签页1】创建模块开关设置面板\n&gt;   618\tfunction WanTinyUI.CreateSettingsPanel(parent)\n    619\t    _G.WanTinyDB = _G.WanTinyDB or {config = {enabledModules = {}}}\n    620\t    _G.WanTinyDB.Map = _G.WanTinyDB.Map or {MiniButShouNa_YN = 1, MinimapPointMode = 1, MiniButPerRow = 6, MiniButHideDelay = 1.5}\n    621\t    local enabledModules = _G.WanTinyDB.config.enabledModules\n    622\t\n    623\t    -- 模块开关标题\n...\n    709\t        slider.valueText:SetText(formatValue(setting.default))\n    710\t    end\n    711\tend\n    712\t\n    713\t-- 【标签页2】创建CVar设置面板\n&gt;   714\tfunction WanTinyUI.CreateCVarPanel(parent)\n    715\t    -- 检查模块加载状态，分别处理两个模块\n    716\t    local wcvarLoaded = checkModuleLoaded(\&quot;WCvar\&quot;)\n    717\t    local wanmenuLoaded = checkModuleLoaded(\&quot;WanMenu\&quot;)\n    718\t\n    719\t    local currentY = -10\n...\n    847\t        createModuleNotLoadedText(parent, \&quot;WanMenu\&quot;, \&quot;TOP\&quot;, parent, 0, currentY)\n    848\t    end\n    849\tend\n    850\t\n    851\t-- 【标签页3】快捷标记面板（简化版）\n&gt;   852\tfunction WanTinyUI.CreateWanMenuPanel(parent)\n    853\t    if not checkModuleLoaded(\&quot;WanMenu\&quot;) then\n    854\t        createModuleNotLoadedText(parent, \&quot;WanMenu\&quot;)\n    855\t        return\n    856\t    end\n    857\t    \n...\n    863\t    desc:SetWidth(500)\n    864\t    desc:SetNonSpaceWrap(true)\n    865\tend\n    866\t\n    867\t-- 【标签页4】创建WCombatTimes战斗计时器设置面板\n&gt;   868\tfunction WanTinyUI.CreateWCombatTimesPanel(parent)\n    869\t    if parent.wcombatPanelCreated then return end\n    870\t\n    871\t\n    872\t\n    873\t    parent.wcombatPanelCreated = true\n\nTotal matches: 4\nTotal lines in file: 1186&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;06b4cd34-bda3-4b0d-8db3-6f1d3bc9d5d8;toolu_vrtx_01XbfGq1QqT33jGNgDQZrEXR&quot;:{&quot;requestId&quot;:&quot;06b4cd34-bda3-4b0d-8db3-6f1d3bc9d5d8&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01XbfGq1QqT33jGNgDQZrEXR&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Regex search results for pattern: 标签页|tab|Tab in WanTinyUI.lua\nFound 82 matching lines:\n\n     27\t\n     28\t        -- 偏移量\n     29\t        TEXT_OFFSET_Y = -2,     -- 文字垂直偏移\n     30\t        CHECKBOX_TEXT_OFFSET_X = 27,  -- 复选框文字水平偏移\n     31\t\n&gt;    32\t        -- 标签页布局\n     33\t        TAB_PADDING = 5,        -- 标签页边距\n     34\t        TAB_SPACING = 1,        -- 标签页间距\n     35\t        TAB_BOTTOM_OFFSET = -28, -- 标签页底部偏移\n     36\t\n     37\t        -- 特殊布局常量\n     38\t        TITLE_SPACING = 20,     -- 标题和内容间距\n     39\t        SECTION_GAP = 25,       -- 区块间隙\n     40\t        EXTRA_SPACING = 15,     -- 额外间距\n...\n    102\tfunction LayoutUtils.SafeGetGlobal(name)\n    103\t    return _G[name]\n    104\tend\n    105\t\n    106\t-- 通用配置更新函数\n&gt;   107\tfunction LayoutUtils.UpdateConfig(configTable, key, value, callbackTable)\n    108\t    configTable[key] = value\n    109\t    if callbackTable and callbackTable[key] then\n    110\t        callbackTable[key](value)\n    111\t    end\n    112\tend\n    113\t\n    114\t-- ==================== 布局工具 ====================\n    115\t\n...\n    328\tend\n    329\t\n    330\t-- 通用控件工厂 - 统一所有控件创建\n    331\tlocal ControlFactory = {\n    332\t    CreateButton = function(parent, text, width, height, onClick, tooltip)\n&gt;   333\t        local btn = WanTinyUI.CreateTabButton(parent, text, width or 80, height or 25)\n    334\t        if onClick then btn:SetScript(\&quot;OnClick\&quot;, onClick) end\n    335\t        if tooltip then setTooltip(btn, tooltip.title or text, tooltip.desc, \&quot;ANCHOR_TOP\&quot;) end\n    336\t        return btn\n    337\t    end,\n    338\t\n...\n    428\t    return f\n    429\tend\n    430\t\n    431\t\n    432\t\n&gt;   433\t-- 创建标签页按钮\n    434\tfunction WanTinyUI.CreateTabButton(parent, text, width, height)\n    435\t    local r, g, b = unpack(WanTinyUI.GetClassRGB())\n    436\t\n    437\t    local bt = CreateFrame(\&quot;Button\&quot;, nil, parent, \&quot;BackdropTemplate\&quot;)\n    438\t    setStandardBackdrop(bt, 0.07)\n    439\t    \n...\n    478\t    -- 创建主窗口\n    479\t    local f = WanTinyUI.CreateMainFrame(\&quot;WanTinyUI_MainFrame\&quot;, \&quot;|cff00bfff&lt;WanTiny&gt;|r|cffff80ff晚妹的小合集|r\&quot;)\n    480\t    WanTinyUI.MainFrame = f\n    481\t    \n    482\t    -- 左侧标题\n&gt;   483\t    WanTinyUI.TabTitleLeft = f:CreateFontString(nil, \&quot;ARTWORK\&quot;)\n    484\t    WanTinyUI.TabTitleLeft:SetFont(STANDARD_TEXT_FONT, 20, \&quot;OUTLINE\&quot;)\n    485\t    WanTinyUI.TabTitleLeft:SetPoint(\&quot;TOPLEFT\&quot;, f, \&quot;TOPLEFT\&quot;, 15, -25)\n    486\t    WanTinyUI.TabTitleLeft:SetTextColor(unpack(WanTinyUI.RGB(\&quot;00BFFF\&quot;)))\n    487\t\n    488\t    -- 右侧版本号\n    489\t    WanTinyUI.TabTitleRight = f:CreateFontString(nil, \&quot;ARTWORK\&quot;, \&quot;GameFontNormal\&quot;)\n    490\t    setFont(WanTinyUI.TabTitleRight, \&quot;LARGE\&quot;)\n    491\t    WanTinyUI.TabTitleRight:SetPoint(\&quot;TOPRIGHT\&quot;, f, \&quot;TOPRIGHT\&quot;, -18, -30)\n    492\t    WanTinyUI.TabTitleRight:SetTextColor(1, 1, 1)\n    493\t    WanTinyUI.CreateInstructionButton()\n    494\t    WanTinyUI.CreateMainContent()\n    495\t    tinsert(UISpecialFrames, \&quot;WanTinyUI_MainFrame\&quot;)\n    496\tend\n    497\t\n...\n    524\t        GameTooltip:Hide()\n    525\t        t:SetTextColor(unpack(COLORS.GREEN))\n    526\t    end)\n    527\tend\n    528\t\n&gt;   529\t-- 创建主内容区域（包含所有标签页内容的容器）\n    530\tfunction WanTinyUI.CreateMainContent()\n    531\t    local mainFrame = WanTinyUI.MainFrame\n    532\t    \n    533\t    -- 创建内容容器框架\n    534\t    local contentFrame = CreateFrame(\&quot;Frame\&quot;, nil, mainFrame, \&quot;BackdropTemplate\&quot;)\n...\n    541\t    })\n    542\t    contentFrame:SetBackdropColor(0, 0, 0, 0)\n    543\t    contentFrame:SetBackdropBorderColor(0.3, 0.3, 0.3, 0.5)\n    544\t    WanTinyUI.contentFrame = contentFrame\n    545\t    \n&gt;   546\t    -- 创建所有标签页的内容面板\n    547\t    WanTinyUI.CreateTabContents()\n    548\t    -- 创建底部标签页切换按钮\n    549\t    WanTinyUI.CreateBottomTabs()\n    550\tend\n    551\t\n    552\t-- ==================== 标签页内容 ====================\n    553\t\n    554\t-- 创建所有标签页的内容面板\n    555\tfunction WanTinyUI.CreateTabContents()\n    556\t    local contentFrame = WanTinyUI.contentFrame\n    557\t    WanTinyUI.tabContents = {}\n    558\t    \n    559\t    -- 标签页配置表：定义每个标签页的内容创建方式\n    560\t    local tabConfigs = {\n    561\t        -- 标签页1: 模块开关面板 - 包含17个功能模块的启用/禁用开关\n    562\t        {createFunc = WanTinyUI.CreateSettingsPanel, visible = true},\n    563\t\n    564\t        -- 标签页2: CVar设置面板 - 游戏参数调整界面\n    565\t        {createFunc = WanTinyUI.CreateCVarPanel, visible = false},\n    566\t\n    567\t        -- 标签页3: 快捷标记设置面板\n    568\t        {createFunc = WanTinyUI.CreateWanMenuPanel, visible = false},\n    569\t\n    570\t        -- 标签页4: WCombatTimes战斗计时器设置面板\n    571\t        {createFunc = WanTinyUI.CreateWCombatTimesPanel, visible = false}\n    572\t    }\n    573\t    \n    574\t    -- 为每个标签页创建对应的内容面板\n    575\t    for i, config in ipairs(tabConfigs) do\n    576\t        local tab = CreateFrame(\&quot;Frame\&quot;, nil, contentFrame)\n    577\t        tab:SetAllPoints(contentFrame)\n    578\t        if not config.visible then tab:Hide() end\n    579\t        WanTinyUI.tabContents[i] = tab\n    580\t        \n    581\t        -- 根据配置创建具体内容\n    582\t        if config.createFunc then\n    583\t            config.createFunc(tab)  -- 调用专门的创建函数\n    584\t        elseif config.text then\n    585\t            -- 创建占位文本\n    586\t            local text = tab:CreateFontString()\n    587\t            text:SetPoint(\&quot;CENTER\&quot;)\n    588\t            setFont(text, \&quot;LARGE\&quot;)\n    589\t            text:SetTextColor(0.7, 0.7, 0.7)\n    590\t            text:SetText(config.text)\n    591\t        end\n    592\t    end\n    593\tend\n    594\t\n&gt;   595\t-- 【标签页1】模块配置数据：17个功能模块的详细信息\n    596\tlocal MODULE_CONFIGS = {\n    597\t    {\&quot;BuffTimers\&quot;, \&quot;|cff00bfff光环计时|r\&quot;, \&quot;显示自身和队友的增益/减益持续时间\&quot;},\n    598\t    {\&quot;idTip\&quot;, \&quot;|cffffd200ID提示|r\&quot;, \&quot;鼠标提示中显示NPC/法术/物品ID\&quot;},\n    599\t    {\&quot;Focuser\&quot;, \&quot;|cffb8860b快捷焦点|r\&quot;, \&quot;快捷设置焦点目标，Shift+左键设置/取消焦点\&quot;},\n    600\t    {\&quot;RaidInfoFrame\&quot;, \&quot;|cffc41f3b团队信息|r\&quot;, \&quot;显示团队副本CD在团队面板\&quot;},\n...\n    612\t    {\&quot;WanMenu\&quot;, \&quot;|cffff80ff快捷标记|r\&quot;, \&quot;提供团队标记、倒计时等快捷功能，支持自定义大小和位置\&quot;},\n    613\t    {\&quot;WCombatTimes\&quot;, \&quot;|cffff6b6b战斗计时|r\&quot;, \&quot;显示战斗持续时间和战斗状态变化提醒，支持自定义横幅和声音\&quot;},\n    614\t\n    615\t}\n    616\t\n&gt;   617\t-- 【标签页1】创建模块开关设置面板\n    618\tfunction WanTinyUI.CreateSettingsPanel(parent)\n    619\t    _G.WanTinyDB = _G.WanTinyDB or {config = {enabledModules = {}}}\n    620\t    _G.WanTinyDB.Map = _G.WanTinyDB.Map or {MiniButShouNa_YN = 1, MinimapPointMode = 1, MiniButPerRow = 6, MiniButHideDelay = 1.5}\n    621\t    local enabledModules = _G.WanTinyDB.config.enabledModules\n    622\t\n...\n    708\t        slider:SetValue(setting.default)\n    709\t        slider.valueText:SetText(formatValue(setting.default))\n    710\t    end\n    711\tend\n    712\t\n&gt;   713\t-- 【标签页2】创建CVar设置面板\n    714\tfunction WanTinyUI.CreateCVarPanel(parent)\n    715\t    -- 检查模块加载状态，分别处理两个模块\n    716\t    local wcvarLoaded = checkModuleLoaded(\&quot;WCvar\&quot;)\n    717\t    local wanmenuLoaded = checkModuleLoaded(\&quot;WanMenu\&quot;)\n    718\t\n...\n    846\t        -- WanMenu模块未加载时显示提示\n    847\t        createModuleNotLoadedText(parent, \&quot;WanMenu\&quot;, \&quot;TOP\&quot;, parent, 0, currentY)\n    848\t    end\n    849\tend\n    850\t\n&gt;   851\t-- 【标签页3】快捷标记面板（简化版）\n    852\tfunction WanTinyUI.CreateWanMenuPanel(parent)\n    853\t    if not checkModuleLoaded(\&quot;WanMenu\&quot;) then\n    854\t        createModuleNotLoadedText(parent, \&quot;WanMenu\&quot;)\n    855\t        return\n    856\t    end\n    857\t    \n    858\t    -- 简单的说明文字\n    859\t    local desc = parent:CreateFontString(nil, \&quot;ARTWORK\&quot;, \&quot;GameFontNormal\&quot;)\n    860\t    desc:SetPoint(\&quot;TOPLEFT\&quot;, parent, \&quot;TOPLEFT\&quot;, 20, -20)\n    861\t    desc:SetJustifyH(\&quot;LEFT\&quot;)\n&gt;   862\t    desc:SetText(\&quot;|cffff80ff快捷标记功能设置|r\\n\\n快捷标记的详细设置已移至 |cff00ff00CVAR设置|r 标签页下方。\\n\\n包含团队标记按钮、倒计时功能、显示选项等设置。\\n\\n当前标签页保留用于未来扩展功能。\&quot;)\n    863\t    desc:SetWidth(500)\n    864\t    desc:SetNonSpaceWrap(true)\n    865\tend\n    866\t\n    867\t-- 【标签页4】创建WCombatTimes战斗计时器设置面板\n    868\tfunction WanTinyUI.CreateWCombatTimesPanel(parent)\n    869\t    if parent.wcombatPanelCreated then return end\n    870\t\n    871\t\n    872\t\n...\n   1091\t\n   1092\t\n   1093\t\n   1094\tend\n   1095\t\n&gt;  1096\t-- 创建底部标签页切换按钮\n   1097\tfunction WanTinyUI.CreateBottomTabs()\n   1098\t    local mainFrame = WanTinyUI.MainFrame\n   1099\t    WanTinyUI.tabButtons = {}\n   1100\t    WanTinyUI.activeTab = 1\n   1101\t\n   1102\t    -- 标签页按钮布局\n   1103\t    local frameWidth = WanTinyUI.FRAME_WIDTH\n   1104\t    local leftPadding = WanTinyUI.LAYOUT.TAB_PADDING\n   1105\t    local rightPadding = WanTinyUI.LAYOUT.TAB_PADDING\n   1106\t    local availableWidth = frameWidth - leftPadding - rightPadding\n   1107\t    local spacing = WanTinyUI.LAYOUT.TAB_SPACING\n   1108\t\n   1109\t    -- 标签页按钮文字定义\n   1110\t    local tabTexts = {\&quot;模块开关\&quot;, \&quot;CVAR/标记\&quot;, \&quot;占位\&quot;, \&quot;战斗计时\&quot;}\n   1111\t\n   1112\t    -- 计算每个按钮的宽度（平均分配可用宽度）\n   1113\t    local totalSpacing = spacing * (table.getn(tabTexts) - 1)\n   1114\t    local buttonWidth = math.floor((availableWidth - totalSpacing) / table.getn(tabTexts))\n   1115\t\n   1116\t    local currentX = leftPadding\n   1117\t    local startY = WanTinyUI.LAYOUT.TAB_BOTTOM_OFFSET\n   1118\t\n   1119\t    for i, tabText in ipairs(tabTexts) do\n   1120\t        local bt = WanTinyUI.CreateTabButton(mainFrame, tabText, buttonWidth, 28)\n   1121\t\n   1122\t        -- 直接定位到底部\n   1123\t        bt:SetPoint(\&quot;BOTTOMLEFT\&quot;, mainFrame, \&quot;BOTTOMLEFT\&quot;, currentX, startY)\n   1124\t        currentX = currentX + buttonWidth + spacing\n   1125\t\n&gt;  1126\t        bt:SetScript(\&quot;OnClick\&quot;, function() WanTinyUI.SelectTab(i) end)\n   1127\t        WanTinyUI.tabButtons[i] = bt\n   1128\t    end\n   1129\t\n   1130\t    -- 默认选中第一个标签页\n   1131\t    WanTinyUI.SelectTab(1)\n   1132\tend\n   1133\tlocal TAB_TITLES = {\n   1134\t    left = {\&quot;WanTiny|cffff80ff晚妹的小合集|r\&quot;, \&quot;CVar/快捷标记\&quot;, \&quot;|cffff80ff占位|r\&quot;, \&quot;|cffff6b6b战斗计时|r\&quot;},\n   1135\t    right = {\&quot;|cff00bfffV 2.0.0|r\&quot;, \&quot;|cff00bfffV 2.0.0|r\&quot;, \&quot;|cff00bfffV 2.0.0|r\&quot;, \&quot;|cff00bfffV 2.0.0|r\&quot;}\n   1136\t}\n   1137\t\n   1138\t-- 标签页切换核心函数：控制标签页按钮状态和内容显示\n   1139\tfunction WanTinyUI.SelectTab(tabIndex)\n   1140\t    WanTinyUI.activeTab = tabIndex\n   1141\t    \n   1142\t    -- 更新标签页按钮的选中状态\n   1143\t    if WanTinyUI.tabButtons and type(WanTinyUI.tabButtons) == \&quot;table\&quot; then\n   1144\t        for i, bt in ipairs(WanTinyUI.tabButtons) do\n   1145\t            if bt and bt.SetSelected then\n   1146\t                bt:SetSelected(i == tabIndex)\n   1147\t            end\n   1148\t        end\n   1149\t    end\n   1150\t    \n   1151\t    -- 控制标签页内容的显示/隐藏\n   1152\t    if WanTinyUI.tabContents then\n   1153\t        for i, content in ipairs(WanTinyUI.tabContents) do\n   1154\t            content:SetShown(i == tabIndex)\n   1155\t        end\n   1156\t    end\n   1157\t\n   1158\t    -- 更新窗口标题文字\n   1159\t    if WanTinyUI.TabTitleLeft and WanTinyUI.TabTitleRight then\n   1160\t        WanTinyUI.TabTitleLeft:SetText(TAB_TITLES.left[tabIndex] or \&quot;\&quot;)\n   1161\t        WanTinyUI.TabTitleRight:SetText(TAB_TITLES.right[tabIndex] or \&quot;\&quot;)\n   1162\t    end\n   1163\tend\n   1164\t\n   1165\tfunction WanTinyUI.ToggleMainFrame()\n   1166\t    if not WanTinyUI.MainFrame then\n\nTotal matches: 82\nTotal lines in file: 1186&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;5c8f1a6a-ade0-4753-b306-bd23a7e71b17;toolu_vrtx_01X7kLgAaFYXjF3bW2r5M7W1&quot;:{&quot;requestId&quot;:&quot;5c8f1a6a-ade0-4753-b306-bd23a7e71b17&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01X7kLgAaFYXjF3bW2r5M7W1&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Successfully edited the file WanTinyUI.lua.\nResult for str_replace for entry with index [1]:\nReplacement successful.\nnew_str starts at line 559 and ends at line 572.\n\nReview the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).\nEdit the file again if necessary.\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;bb3de588-2cf7-488f-a388-62112562706a;toolu_vrtx_01C7YrjEEtGyK6tFAt8YpNfE&quot;:{&quot;requestId&quot;:&quot;bb3de588-2cf7-488f-a388-62112562706a&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01C7YrjEEtGyK6tFAt8YpNfE&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Successfully edited the file WanTinyUI.lua.\nResult for str_replace for entry with index [1]:\nReplacement successful.\nnew_str starts at line 1109 and ends at line 1110.\n\nReview the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).\nEdit the file again if necessary.\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}}},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:false,&quot;baselineTimestamp&quot;:0},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;6a4981bf-e6fe-41ef-9c9e-7ff38e1d8de3&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>